{"name": "pos-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "@tanstack/react-query": "^5.66.0", "axios": "^1.7.9", "framer-motion": "^11.18.1", "notistack": "^3.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.3", "recharts": "^2.15.4", "tailwind-scrollbar-hide": "^2.0.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}