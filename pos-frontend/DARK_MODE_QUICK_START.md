# Dark Mode Quick Start Guide

## 🚀 Quick Setup (5 minutes)

### 1. Wrap Your App with ThemeProvider

```jsx
// src/App.jsx
import ThemeProvider from './components/shared/ThemeProvider';

function App() {
  return (
    <ThemeProvider>
      {/* Your existing app content */}
    </ThemeProvider>
  );
}
```

### 2. Use Theme Hook in Components

```jsx
// Any component
import { useTheme } from './hooks/useTheme';

const MyComponent = () => {
  const { adaptive } = useTheme();
  
  return (
    <div className={adaptive('card')}>
      <h1 className={adaptive('text')}>Auto-themed content!</h1>
    </div>
  );
};
```

### 3. Replace Hardcoded Colors

```jsx
// ❌ Before (hardcoded)
<div className="bg-[#1a1a1a] text-[#f5f5f5]">

// ✅ After (auto-themed)
<div className={adaptive('card')}>
```

## 🎨 Common Patterns

### Cards & Containers
```jsx
const { adaptive } = useTheme();

// Basic card
<div className={adaptive('card')}>Content</div>

// Page container
<div className={adaptive('page')}>Page content</div>
```

### Text Elements
```jsx
// Primary text (headings, important content)
<h1 className={adaptive('text')}>Main heading</h1>

// Secondary text (descriptions, labels)
<p className={adaptive('textSecondary')}>Description text</p>
```

### Form Elements
```jsx
// Input fields
<input className={adaptive('input')} />

// Buttons
<button className={adaptive('button')}>Click me</button>
```

### Borders & Dividers
```jsx
// Borders
<div className={adaptive('border')}>With border</div>

// Divider lines
<hr className={adaptive('border')} />
```

## 🔧 Pre-built Components

Use these components for instant dark mode support:

```jsx
import { 
  ThemedCard, 
  ThemedButton, 
  ThemedInput,
  ThemedModal 
} from './components/shared/ThemedComponents';

const QuickExample = () => (
  <ThemedCard>
    <ThemedInput placeholder="Auto-themed input" />
    <ThemedButton variant="primary">Auto-themed button</ThemedButton>
  </ThemedCard>
);
```

## 🎯 Component Variants

### Button Variants
```jsx
<ThemedButton variant="primary">Primary</ThemedButton>
<ThemedButton variant="secondary">Secondary</ThemedButton>
<ThemedButton variant="ghost">Ghost</ThemedButton>
<ThemedButton variant="danger">Danger</ThemedButton>
```

### Badge Variants
```jsx
<ThemedBadge variant="success">Success</ThemedBadge>
<ThemedBadge variant="warning">Warning</ThemedBadge>
<ThemedBadge variant="error">Error</ThemedBadge>
<ThemedBadge variant="info">Info</ThemedBadge>
```

## 🔄 Migration Checklist

### For Existing Components:

1. **Add theme hook**
   ```jsx
   import { useTheme } from '../hooks/useTheme';
   const { adaptive, cn } = useTheme();
   ```

2. **Replace hardcoded backgrounds**
   ```jsx
   // Find: bg-[#...] or bg-gray-800
   // Replace with: adaptive('card') or adaptive('surface')
   ```

3. **Replace hardcoded text colors**
   ```jsx
   // Find: text-[#...] or text-white
   // Replace with: adaptive('text') or adaptive('textSecondary')
   ```

4. **Replace hardcoded borders**
   ```jsx
   // Find: border-[#...] or border-gray-600
   // Replace with: adaptive('border')
   ```

5. **Test both themes**
   - Toggle dark mode in app settings
   - Verify all elements are visible and readable

## 🚨 Common Mistakes to Avoid

### ❌ Don't use hardcoded colors
```jsx
// Bad
<div className="bg-[#1a1a1a] text-[#f5f5f5]">
```

### ❌ Don't forget hover states
```jsx
// Bad - no hover styling
<button className={adaptive('button')}>

// Good - includes hover
<button className={cn(adaptive('button'), adaptive('hover'))}>
```

### ❌ Don't mix hardcoded with adaptive
```jsx
// Bad - inconsistent theming
<div className={cn(adaptive('card'), 'text-white')}>

// Good - fully adaptive
<div className={cn(adaptive('card'), adaptive('text'))}>
```

## 🎨 Chart Theming

For charts (Recharts, Chart.js, etc.):

```jsx
import { useChartTheme } from '../hooks/useTheme';

const MyChart = ({ data }) => {
  const chartTheme = useChartTheme();
  
  return (
    <BarChart data={data}>
      <CartesianGrid {...chartTheme.cartesianGrid} />
      <XAxis {...chartTheme.xAxis} />
      <YAxis {...chartTheme.yAxis} />
      <Tooltip {...chartTheme.tooltip} />
    </BarChart>
  );
};
```

## 🔍 Debugging Tips

### Check if dark mode is active:
```jsx
const { isDark } = useTheme();
console.log('Dark mode:', isDark);
```

### Inspect CSS variables:
```javascript
// In browser console
getComputedStyle(document.documentElement).getPropertyValue('--color-bg-primary')
```

### Verify theme classes:
```javascript
// Check if dark class is applied
document.documentElement.classList.contains('dark')
```

## 📱 Testing Checklist

- [ ] Component renders in light mode
- [ ] Component renders in dark mode  
- [ ] Text is readable in both themes
- [ ] Interactive elements have proper hover states
- [ ] Focus indicators are visible
- [ ] Charts/graphs display correctly
- [ ] No hardcoded colors remain

## 🆘 Need Help?

### Common Issues:

**Theme not applying?**
- Ensure `ThemeProvider` wraps your component
- Check Redux state has correct `darkMode` value

**Styles not updating?**
- Replace hardcoded classes with `adaptive()` calls
- Import and use the `useTheme` hook

**Charts not theming?**
- Use `useChartTheme()` hook
- Apply theme props to chart components

### Resources:
- Full documentation: `THEMING_SYSTEM.md`
- Theme utilities: `src/utils/theme.js`
- Pre-built components: `src/components/shared/ThemedComponents.jsx`
- Theme hook: `src/hooks/useTheme.js`

## 🎉 You're Done!

Your component now automatically supports both light and dark modes! 

The theme will change automatically when users toggle dark mode in the app settings, and your component will seamlessly adapt without any additional code changes.
