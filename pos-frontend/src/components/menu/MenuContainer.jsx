import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { addItems } from "../../redux/slices/cartSlice";
import { axiosWrapper } from "../../https/axiosWrapper";
import PageNav from "../shared/PageNav";
import { enqueueSnackbar } from 'notistack';


const MenuContainer = ({ selectedCartItemId, openModal, onOpenMessageModal }) => {
  const [families, setFamilies] = useState([]);
  const [subFamilies, setSubFamilies] = useState([]);
  const [articles, setArticles] = useState([]);
  const [selectedFamily, setSelectedFamily] = useState(null);
  const [selectedSubFamily, setSelectedSubFamily] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [showFavorites, setShowFavorites] = useState(false);
  const [showInfoButtons, setShowInfoButtons] = useState(false);
  const dispatch = useDispatch();
  const user = useSelector(state => state.user);
  const cart = useSelector(state => state.cart);
  const table = useSelector(state => state.table.table);
  const [page, setPage] = useState(0);
  const { numberOfOrdersPerPage } = useSelector(state => state.general);
  const { defaultCurrency, trackStock, mode } = useSelector(state => state.general);
  const [pageSize, setPageSize] = useState(numberOfOrdersPerPage || 20);





  // Helper to get auth headersee
  // const getAuthHeader = () => {
  //   const token = user?.access || localStorage.getItem("accessToken");
  //   return token ? { Authorization: `Bearer ${token}` } : {};
  // };


  const handleClearQuantity = () => {
    setQuantity(1);
  };

  // On mount: fetch families, then subfamilies, then articles
  useEffect(() => {
    setLoading(true);
    axiosWrapper.get("/api/products/family/?paginate=false")
      .then(res => {
        const fams = res.data.data || [];
        setFamilies(fams);
        if (fams.length) {
          const firstFamilyId = fams[0].id;
          setSelectedFamily(firstFamilyId);
          // Fetch subfamilies for the first family
          axiosWrapper.get(`/api/products/sub-family/?family=${firstFamilyId}&paginate=false`)
            .then(subRes => {
              const subs = subRes.data.data || [];
              setSubFamilies(subs);
              if (subs.length) {
                const firstSubFamilyId = subs[0].id;
                setSelectedSubFamily(firstSubFamilyId);
                // Fetch articles for the first subfamily
                axiosWrapper.get(`/api/products/articles/workstation/?sub_family=${firstSubFamilyId}`)
                  .then(artRes => {
                    setArticles(artRes.data.data || []);
                    setLoading(false);
                  })
                  .catch(() => { setError("Failed to load items"); setLoading(false); });
              } else {
                setArticles([]);
                setLoading(false);
              }
            })
            .catch(() => { setError("Failed to load subcategories"); setLoading(false); });
        } else {
          setLoading(false);
        }
      })
      .catch(() => { setError("Failed to load categories"); setLoading(false); });
  }, []);

  // When family changes (user click), fetch subfamilies and articles for first subfamily
  const handleFamilySelect = famId => {
    setSelectedFamily(famId);
    setSelectedSubFamily(null);
    setSubFamilies([]);
    setShowInfoButtons(false); // Reset messages mode

    if (famId === 'favorites') {
      setShowFavorites(true);
      setLoading(true);
      axiosWrapper.get(`/api/products/articles/workstation/?is_favorite=true`)
        .then(artRes => {
          setArticles(artRes.data.data || []);
          setLoading(false);
        })
        .catch(() => { setError("Failed to load items"); setLoading(false); });
    } else {
      setShowFavorites(false);
      setLoading(true);
      axiosWrapper.get(`/api/products/sub-family/?family=${famId}&paginate=false`)
        .then(subRes => {
          const subs = subRes.data.data || [];
          setSubFamilies(subs);
          if (subs.length) {
            const firstSubFamilyId = subs[0].id;
            setSelectedSubFamily(firstSubFamilyId);
            axiosWrapper.get(`/api/products/articles/workstation/?sub_family=${firstSubFamilyId}`)
              .then(artRes => {
                setArticles(artRes.data.data || []);
                setLoading(false);
              })
              .catch(() => { setError("Failed to load items"); setLoading(false); });
          } else {
            setArticles([]);
            setLoading(false);
          }
        })
        .catch(() => { setError("Failed to load subcategories"); setLoading(false); });
    }
  };

  // When subfamily changes (user click), fetch articles
  const handleSubFamilySelect = subId => {
    setSelectedSubFamily(subId);
    setShowInfoButtons(false); // Reset messages mode
    setLoading(true);
    axiosWrapper.get(`/api/products/articles/workstation/?sub_family=${subId}`)
      .then(artRes => {
        setArticles(artRes.data.data || []);
        setLoading(false);
      })
      .catch(() => { setError("Failed to load items"); setLoading(false); });
  };

  // Add to cart: if item exists, increase quantity; else add new
  const handleAddToCart = (article) => {
    // Show table modal only if cart is empty and no table is selected
    if (cart.items.length === 0 && (!table || !table.name)) {
      openModal();
      return;
    }

    if (!quantity || quantity < 1) return;

    // Use the same addItems action for both new and existing items
    // The addItems action automatically handles incrementing existing items
    dispatch(addItems({
      article: {
        id: article.id,
        name: article.name,
        code: article.code,
        price: article.price
      },
      quantity
    }));

    setQuantity(1);
  };

  useEffect(() => {
    if (numberOfOrdersPerPage) setPageSize(numberOfOrdersPerPage);
  }, [numberOfOrdersPerPage]);

  // Reset page to 0 when articles change
  useEffect(() => {
    setPage(0);
  }, [articles]);

  // Paging logic for articles only
  const columns = 4; // You may want to make this responsive
  const fullPageSize = columns * Math.floor(pageSize / columns);
  const maxArticlesPerPage = fullPageSize - 1;
  const totalPages = Math.ceil(articles.length / maxArticlesPerPage);
  const hasPrev = page > 0;
  const hasNext = page < totalPages - 1;
  let startIdx = page * maxArticlesPerPage;
  let endIdx = startIdx + maxArticlesPerPage;
  let pagedArticles = articles.slice(startIdx, endIdx);

  // Inject INFO BAR and INFO KITCHN as special cards if showInfoButtons is true
  let specialInfoCards = [];
  if (showInfoButtons) {
    specialInfoCards = [
      <div
        key="info-bar"
        className="relative rounded-xl shadow bg-yellow-500 border border-gray-800 p-4 flex flex-col justify-between min-h-[180px] max-h-[180px] h-[180px] cursor-pointer transition-shadow hover:shadow-2xl hover:bg-yellow-600"
        onClick={() => onOpenMessageModal('restaurant')}
        tabIndex={0}
        aria-label="Send message to Restaurant"
      >
        <div className="flex-1 flex flex-col items-center justify-center">
          <span className="text-4xl mb-2">🍽️</span>
          <div className="font-extrabold text-xl text-black mb-1 text-center">INFO BAR</div>
          <div className="text-black text-center text-sm opacity-80">Send message to Restaurant</div>
        </div>
      </div>,
      <div
        key="info-kitchn"
        className="relative rounded-xl shadow bg-green-500 border border-gray-800 p-4 flex flex-col justify-between min-h-[180px] max-h-[180px] h-[180px] cursor-pointer transition-shadow hover:shadow-2xl hover:bg-green-600"
        onClick={() => onOpenMessageModal('bar')}
        tabIndex={0}
        aria-label="Send message to Bar"
      >
        <div className="flex-1 flex flex-col items-center justify-center">
          <span className="text-4xl mb-2">🍹</span>
          <div className="font-extrabold text-xl text-black mb-1 text-center">INFO KITCHN</div>
          <div className="text-black text-center text-sm opacity-80">Send message to Bar</div>
        </div>
      </div>
    ];
  }
  const articleCards = pagedArticles.map(article => article ? (
    <div
      key={article.id}
      className="relative rounded-xl shadow bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 flex flex-col justify-between min-h-[180px] max-h-[180px] h-[180px] cursor-pointer transition-all hover:shadow-2xl hover:scale-105"
      onClick={() => {
        if (trackStock && (article.quantity ?? 0) <= 0) {
          enqueueSnackbar(`${article.name} is out of stock!`, { variant: "error" });
          return;
        }
        handleAddToCart(article);
      }}
      tabIndex={0}
      aria-label={`Add ${article.name} to cart`}
      style={{ backgroundColor: article.generatedColor || undefined }}
    >
      {/* Status dot */}
      {trackStock && (
        <span
          className={`absolute top-3 right-4 w-2.5 h-2.5 rounded-full 
          ${article.quantity <= (article.minimumStock ?? 0)
              ? 'bg-red-500 shadow-[0_0_6px_#ff0000]'
              : 'bg-green-400 shadow-[0_0_6px_#00ff88]'}
          `}
        ></span>
      )}
      
      {/* Name - Centered and prominent */}
      <div className="flex-1 flex items-center justify-center">
        <div className="font-black text-2xl text-white text-center leading-tight drop-shadow-lg">
          {article.name}
        </div>
      </div>
      
      {/* Bottom section with price and quantity */}
      <div className="mt-auto flex items-center justify-between">
        <div className="font-extrabold text-xl text-blue-400">
          {defaultCurrency}{Number(article.price).toFixed(2)}
        </div>
        {trackStock && (article.quantity ?? 0) > 0 && (
          <div className="inline-block px-2 py-1 rounded bg-gray-700 text-xs font-semibold text-gray-200">
            Qty: {article.quantity ?? 0}
          </div>
        )}
      </div>
    </div>
  ) : null).filter(Boolean);

  // Prepend special info cards if needed
  const gridItems = [...specialInfoCards, ...articleCards];

  const showPageNav = articles.length > maxArticlesPerPage;
  if (showPageNav) {
    // Calculate how many empty divs to add before PageNav to push it to the last column
    const itemsInLastRow = gridItems.length % columns;
    const emptyBefore = itemsInLastRow === 0 ? columns - 1 : columns - itemsInLastRow - 1;
    for (let i = 0; i < emptyBefore; i++) {
      gridItems.push(<div key={`empty-before-nav-${i}`} />);
    }
    gridItems.push(
      <div key="page-nav" className="flex justify-end items-end h-full">
        <PageNav
          onPageUp={() => setPage(prev => (prev > 0 ? prev - 1 : 0))}
          onPageDown={() => setPage(prev => prev + 1)}
          disableUp={!hasPrev}
          disableDown={!hasNext}
        />
      </div>
    );
  }
  if (gridItems.length % columns !== 0) {
    for (let i = 0; i < columns - (gridItems.length % columns); i++) {
      gridItems.push(<div key={`empty-${i}`} />);
    }
  }

  if (loading) return <div className="text-center text-gray-400 py-10">Loading...</div>;
  if (error) return <div className="text-center text-red-400 py-10">{error}</div>;

  return (
    <div className="flex h-full bg-white dark:bg-gray-800 transition-colors">
      {/* Sidebar: Families */}
      <div className="w-56 bg-gray-200 dark:bg-gray-700 border-r border-gray-300 dark:border-gray-600 flex flex-col transition-colors">
        <div className="p-4 border-b border-gray-300 dark:border-gray-600 bg-gray-300 dark:bg-gray-600 font-bold text-lg text-gray-900 dark:text-gray-100">Categories</div>
        <div className="flex-1 overflow-y-auto p-2">

          {/* Messages Button */}

          {(mode === "restaurant" || mode === "bar") && (
            <button
              className="p-4 mb-2 rounded bg-blue-700 hover:bg-blue-800 text-white font-semibold w-full"
              onClick={() => setShowInfoButtons((prev) => !prev)}
            >
              Messages
            </button>
          )}
          {/* Favorites Option */}
          <div
            className={`p-4 mb-2 rounded cursor-pointer transition-all ${selectedFamily === 'favorites' ? "ring-2 ring-blue-400" : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"}`}
            style={{ backgroundColor: selectedFamily === 'favorites' ? '#ffc107' : undefined }}
            onClick={() => handleFamilySelect('favorites')}
          >
            <div className="font-semibold text-white">⭐ Favorites</div>
            <div className="text-xs opacity-70 text-white">FAV</div>
          </div>
          {families.map(fam => (
            <div
              key={fam.id}
              className={`p-4 mb-2 rounded cursor-pointer transition-all ${selectedFamily === fam.id ? "ring-2 ring-blue-400" : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"}`}
              style={{ backgroundColor: fam.generatedColor || undefined }}
              onClick={() => handleFamilySelect(fam.id)}
            >
              <div className="font-semibold text-white">{fam.name}</div>
              <div className="text-xs opacity-70 text-white">{fam.code}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Quantity Buttons */}
        <div className="flex gap-3 justify-center py-4 bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-600 transition-colors">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 0].map(num => (
            <button
              key={num}
              className={`w-16 h-14 rounded-lg font-bold text-2xl transition-all flex items-center justify-center ${quantity === num ? "bg-blue-600 text-white" : "bg-gray-300 dark:bg-gray-600 text-gray-900 dark:text-gray-100 hover:bg-gray-400 dark:hover:bg-gray-500"}`}
              onClick={() => setQuantity(num)}
            >
              {num}
            </button>
          ))}
          <button
            className="w-16 h-14 rounded-lg font-bold text-xl transition-all flex items-center justify-center bg-white hover:bg-gray-100 text-black border border-gray-300"
            onClick={handleClearQuantity}>X
          </button>
        </div>
        {/* SubFamilies Bar - Only show when not showing favorites */}
        {!showFavorites && (
          <div className="bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-600 p-4 transition-colors">
            <div className="grid grid-cols-4 gap-4">
              {subFamilies.map((sub) => (
                <div
                  key={sub.id}
                  className={`p-4 rounded-lg cursor-pointer font-semibold transition-all ${selectedSubFamily === sub.id ? "border-2 border-blue-500 dark:border-blue-400" : "border-2 border-transparent"}`}
                  style={{ backgroundColor: sub.generatedColor || undefined, color: sub.generatedColor && sub.generatedColor.toLowerCase() === '#ffc107' ? '#000' : '#fff' }}
                  onClick={() => handleSubFamilySelect(sub.id)}
                >
                  <div>{sub.name}</div>
                  <div className="text-xs opacity-80">{sub.description}</div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Articles Grid or Info Buttons */}
        <div className="flex-1 p-2 overflow-y-hidden mb-6 max-h-[700px]">
          {showInfoButtons ? (
            <div className="flex h-full w-full gap-6 items-center justify-center">
              {mode === 'restaurant' && (
                <>
                  <button
                    className="flex-1 h-64 bg-green-400 hover:bg-green-500 rounded-xl flex flex-col items-center justify-center text-4xl font-bold text-white mx-2 transition-all shadow-lg border-4 border-green-700 text-center"
                    style={{ minWidth: 0 }}
                    onClick={() => onOpenMessageModal('bar')}
                  >
                    INFO KITCHEN
                  </button>
                  <button
                    className="flex-1 h-64 bg-yellow-400 hover:bg-yellow-500 rounded-xl flex flex-col items-center justify-center text-4xl font-bold text-black mx-2 transition-all shadow-lg border-4 border-yellow-700 text-center"
                    style={{ minWidth: 0 }}
                    onClick={() => onOpenMessageModal('restaurant')}
                  >
                    INFO BAR
                  </button>
                </>
              )}
              {mode === 'bar' && (
                <button
                  className="flex-1 h-64 bg-yellow-400 hover:bg-yellow-500 rounded-xl flex flex-col items-center justify-center text-4xl font-bold text-black mx-2 transition-all shadow-lg border-4 border-yellow-700 text-center"
                  style={{ minWidth: 0 }}
                  onClick={() => onOpenMessageModal('restaurant')}
                >
                  INFO BAR
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-4 gap-4 items-start pb-4">
              {articles.length === 0 ? (
                <div className="col-span-2 text-center text-gray-400 flex flex-col items-center justify-center">
                  {/* <span className="text-6xl mb-2">🍽️</span> */}
                  <span className="font-bold text-lg">Select a Category</span>
                  <span className="text-gray-400">Choose a category from the top to view  items</span>
                </div>
              ) : (
                gridItems
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MenuContainer;
