import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { useSelector } from "react-redux";
import { useSnackbar } from "notistack";

const SplitCheckModal = ({ isOpen, onClose, cartItems, onSplit }) => {

    const [itemState, setItemState] = useState({ left: [], right: [] });

    const [animatingItems, setAnimatingItems] = useState(new Set());
    const defaultCurrency = useSelector(state => state.general.defaultCurrency);
    const { enqueueSnackbar } = useSnackbar();

    useEffect(() => {
        if (isOpen) {
            // Deep copy and add line_total to each item
            const leftItems = JSON.parse(JSON.stringify(cartItems)).map(item => ({
                ...item,
                line_total: (parseFloat(item.article.price) || 0) * item.quantity


            }));
            setItemState({
                left: leftItems,
                right: []
            });
        }
    }, [isOpen, cartItems]);

    useEffect(() => {
        // Show a warning toast if the original check becomes empty,
        // but only after it has been initialized.
        if (isOpen && itemState.left.length === 0 && itemState.right.length > 0) {
            enqueueSnackbar("At least one item must be left on the original check.", { variant: "warning" });
        }
    }, [itemState.left, itemState.right, isOpen, enqueueSnackbar]);

    const handleMoveItem = (itemToMove, direction) => {
        const itemKey = `${itemToMove.article.id}-${direction}`;

        // Add item to animating set
        setAnimatingItems(prev => new Set([...prev, itemKey]));

        // Remove from animating set after animation completes
        setTimeout(() => {
            setAnimatingItems(prev => {
                const newSet = new Set(prev);
                newSet.delete(itemKey);
                return newSet;
            });
        }, 300);

        setItemState(currentState => {
            const sourceListName = direction === 'toRight' ? 'left' : 'right';
            const destListName = direction === 'toRight' ? 'right' : 'left';

            const sourceList = [...currentState[sourceListName]];
            const destList = [...currentState[destListName]];

            const originalItem = cartItems.find(item => item.article.id === itemToMove.article.id);

            if (!originalItem) return currentState;

            const unitPrice = parseFloat(originalItem.article.price) || 0;

            // Find item in source list
            const sourceItemIndex = sourceList.findIndex(i => i.article.id === itemToMove.article.id);
            if (sourceItemIndex === -1) return currentState;

            const sourceItem = { ...sourceList[sourceItemIndex] };

            // Decrement from source
            if (sourceItem.quantity > 1) {
                sourceItem.quantity -= 1;
                sourceItem.line_total = sourceItem.quantity * unitPrice;
                sourceList[sourceItemIndex] = sourceItem;
            } else {
                sourceList.splice(sourceItemIndex, 1);
            }

            // Increment or add to destination
            const destItemIndex = destList.findIndex(i => i.article.id === itemToMove.article.id);
            if (destItemIndex !== -1) {
                const destItem = { ...destList[destItemIndex] };
                destItem.quantity += 1;
                destItem.line_total = destItem.quantity * unitPrice;
                destList[destItemIndex] = destItem;
            } else {
                destList.push({
                    ...originalItem,
                    quantity: 1,
                    line_total: unitPrice
                });
            }

            return {
                [sourceListName]: sourceList,
                [destListName]: destList
            };
        });
    };

    const handleSplit = () => {
        if (itemState.right.length === 0 || itemState.left.length === 0) return;

        const splitData = {
            data: itemState.right.map(item => ({
                order_item_id: item.orderItemId,
                split_quantity: item.quantity,
            }))
        };
        onSplit(splitData);
        onClose();
    };

    const formatPrice = (price) => {
        const numPrice = parseFloat(price) || 0;
        return `${defaultCurrency}${numPrice.toFixed(2)}`;
    };

    if (!isOpen) return null;

    const leftTotal = itemState.left.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const rightTotal = itemState.right.reduce((sum, item) => sum + (item.line_total || 0), 0);

    const renderItems = (items, direction) => (
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {items.length === 0 ? (
                <div className="text-center text-gray-400 py-8 transition-opacity duration-300">
                    <p className="text-lg">No items</p>
                    <p className="text-sm">Click items from the other side to move them here</p>
                </div>
            ) : (
                items.map((item, index) => {
                    const itemKey = `${item.article.id}-${direction}`;
                    const isAnimating = animatingItems.has(itemKey);

                    return (
                        <div
                            key={`${direction}-${item.article.id}`}
                            className={`bg-[#2d2d2d] p-4 rounded-lg cursor-pointer hover:bg-[#333] border border-transparent hover:border-blue-500 transition-all duration-300 transform hover:scale-105 ${isAnimating ? 'animate-pulse scale-105 border-blue-400' : ''
                                }`}
                            style={{
                                animationDelay: `${index * 50}ms`,
                                transform: isAnimating ? 'scale(1.05) translateX(0)' : '',
                                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                            }}
                            onClick={() => handleMoveItem(item, direction === 'left' ? 'toRight' : 'toLeft')}
                        >
                            <div className="flex justify-between items-center text-white">
                                <div className="flex items-center gap-2">
                                    <span className={`bg-blue-600 text-white px-2 py-1 rounded text-sm font-bold transition-all duration-300 ${isAnimating ? 'bg-blue-500 scale-110' : ''
                                        }`}>
                                        {item.quantity}
                                    </span>
                                    <span className="font-semibold text-lg">{item.article.name}</span>
                                </div>
                                <div className="text-right">
                                    <span className="font-bold text-lg">{formatPrice(item.line_total)}</span>
                                    {item.quantity > 0 && <div className="text-sm text-gray-400">
                                        {formatPrice(item.article.price || (item.line_total / item.quantity))} each
                                    </div>}
                                </div>
                            </div>
                        </div>
                    );
                })
            )}
        </div>
    );

    return (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="bg-[#1a1a1a] rounded-2xl w-full max-w-7xl h-[90vh] flex flex-col overflow-hidden shadow-2xl border border-[#404040] animate-in fade-in-0 zoom-in-95 duration-300">
                {/* Header */}
                <div className="bg-[#2d2d2d] border-b border-[#404040] p-4 text-center">
                    <h2 className="text-2xl font-bold text-white">Split Check</h2>
                    <p className="text-gray-400 text-sm mt-1">Click items to move them between checks</p>
                </div>

                {/* Main Content */}
                <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-4 p-4">
                    {/* Original Bill */}
                    <div className="bg-[#1f1f1f] rounded-lg border border-[#404040] flex flex-col transform transition-all duration-300 hover:shadow-lg">
                        <div className="bg-[#2d2d2d] p-4 border-b border-[#404040] flex justify-between items-center">
                            <h3 className="text-xl font-semibold text-white">Original Check</h3>
                            <span className="text-xl font-bold text-white transition-all duration-300">{formatPrice(leftTotal)}</span>
                        </div>
                        {renderItems(itemState.left, 'left')}
                    </div>

                    {/* New Bill */}
                    <div className="bg-[#1f1f1f] rounded-lg border border-[#404040] flex flex-col transform transition-all duration-300 hover:shadow-lg">
                        <div className="bg-[#2d2d2d] p-4 border-b border-[#404040] flex justify-between items-center">
                            <h3 className="text-xl font-semibold text-white">New Check</h3>
                            <span className="text-xl font-bold text-white transition-all duration-300">{formatPrice(rightTotal)}</span>
                        </div>
                        {renderItems(itemState.right, 'right')}
                    </div>
                </div>

                {/* Footer with Action Buttons */}
                <div className="bg-[#2d2d2d] p-4 border-t border-[#404040]">
                    <div className="flex justify-between items-center">
                        <div className="text-white">
                            <p className="text-lg font-semibold transition-all duration-300">Total: {formatPrice(leftTotal + rightTotal)}</p>
                            <p className="text-sm text-gray-400 transition-all duration-300">
                                Original: {formatPrice(leftTotal)} | New: {formatPrice(rightTotal)}
                            </p>
                        </div>
                        <div className="flex gap-4">
                            <button
                                onClick={onClose}
                                className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-4 rounded-xl font-bold text-xl transition-all duration-200 transform hover:scale-105 active:scale-95"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleSplit}
                                className={`px-8 py-4 rounded-xl font-bold text-xl transition-all duration-200 transform hover:scale-105 active:scale-95 ${itemState.right.length > 0 && itemState.left.length > 0
                                    ? "bg-green-600 hover:bg-green-700 text-white hover:shadow-lg"
                                    : "bg-[#404040] text-gray-400 cursor-not-allowed"
                                    }`}
                                disabled={itemState.right.length === 0 || itemState.left.length === 0}
                            >
                                Split Bill
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

SplitCheckModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    cartItems: PropTypes.array.isRequired,
    onSplit: PropTypes.func.isRequired
};

export default SplitCheckModal;