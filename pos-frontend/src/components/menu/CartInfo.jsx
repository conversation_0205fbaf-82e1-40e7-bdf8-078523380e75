import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { AiOutlineMinus, AiOutlinePlus } from "react-icons/ai";
import { clearCart, removeItem, updateQuantity } from "../../redux/slices/cartSlice";
import { useNavigate } from "react-router-dom";
import { FaChevronUp, FaChevronDown } from 'react-icons/fa';


const CartInfo = ({ selectedCartItemId, onSelectCartItem, onEditTableName, isEditMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { items, total } = useSelector((state) => state.cart);
  const { defaultCurrency, numberOfOrdersPerPage } = useSelector((state) => state.general);
  const { table } = useSelector((state) => state.table);


  const ITEMS_PER_PAGE = numberOfOrdersPerPage ? numberOfOrdersPerPage - 7 : 8;

  const [cartPage, setCartPage] = React.useState(0);
  const totalPages = Math.ceil((items?.length || 0) / ITEMS_PER_PAGE);
  const hasPrev = cartPage > 0;
  const hasNext = cartPage < totalPages - 1;
  const startIdx = cartPage * ITEMS_PER_PAGE;
  const endIdx = startIdx + ITEMS_PER_PAGE;
  const pagedItems = items.slice(startIdx, endIdx);

  React.useEffect(() => {
    // Reset to first page if items change and current page is out of range
    if (cartPage > 0 && startIdx >= items.length) {
      setCartPage(0);
    }
  }, [items, cartPage, startIdx]);

  const handleClearCart = () => {
    dispatch(clearCart());
  };

  const handleRemoveItem = (articleId) => {
    dispatch(removeItem(articleId));
  };

  const handleUpdateQuantity = (articleId, newQuantity) => {
    if (newQuantity <= 0) {
      dispatch(removeItem(articleId));
    } else {
      dispatch(updateQuantity({ articleId, quantity: newQuantity }));
    }
  };

  const formatPrice = (price) => {
    if (typeof price !== 'number') return `${defaultCurrency}0.00`;
    return `${defaultCurrency}${price.toFixed(2)}`;
  };

  return (
    <div className="flex flex-col h-full p-4 relative">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-gray-900 dark:text-gray-100 text-xl font-bold">
          {table?.code || ""}
        </h2>
        {table && (
          <button
            onClick={onEditTableName}
            className="text-yellow-400 font-bold text-lg bg-gray-800 px-3 py-1 rounded-lg hover:bg-gray-700 transition-colors"
          >
            {table.name}
          </button>
        )}
      </div>
      <div className="flex-1 min-h-0 flex flex-row">
        {/* Cart items list */}
        <div className="flex-1 flex flex-col space-y-4 h-full min-h-0">
          {pagedItems?.map((item) => {
            if (!item?.article) return null;
            const isSelected = selectedCartItemId === item.article.id;
            return (
              <div
                key={item.article.id}
                className={`flex items-center justify-between bg-gray-100 dark:bg-gray-700 p-3 rounded-lg cursor-pointer transition-all duration-150 ${isSelected ? 'ring-2 ring-yellow-400 bg-yellow-100 dark:bg-yellow-900' : 'hover:bg-gray-200 dark:hover:bg-gray-600'}`}
                onClick={() => onSelectCartItem(item.article.id)}
              >
                <div className="flex-1">
                  <h3 className={`font-medium ${isSelected ? 'text-yellow-600 dark:text-yellow-400 font-bold text-xl' : 'text-gray-900 dark:text-gray-100'}`}>{item.article.name || 'Unknown Item'}</h3>
                  <p className={`text-sm ${isSelected ? 'text-yellow-700 dark:text-yellow-300 font-bold' : 'text-gray-600 dark:text-gray-400'}`}>{formatPrice(item.article.price)}</p>
                </div>
                <div className="flex items-center gap-3">
                  {/* Show plus/minus only for new items in edit mode, or for all items outside edit mode */}
                  {(!isEditMode || (isEditMode && !item.orderItemId)) && (
                    <>
                      <button
                        onClick={e => { e.stopPropagation(); handleUpdateQuantity(item.article.id, item.quantity - 1); }}
                        className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200"
                      >
                        <AiOutlineMinus size={20} />
                      </button>
                    </>
                  )}
                  <span className="text-gray-900 dark:text-gray-100 font-medium min-w-[20px] text-center">
                    {item.quantity || 0}
                  </span>
                  {(!isEditMode || (isEditMode && !item.orderItemId)) && (
                    <>
                      <button
                        onClick={e => { e.stopPropagation(); handleUpdateQuantity(item.article.id, item.quantity + 1); }}
                        className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200"
                      >
                        <AiOutlinePlus size={20} />
                      </button>
                    </>
                  )}
                </div>
              </div>
            );
          })}
          {!pagedItems?.length && (
            <div className="text-center text-gray-600 dark:text-gray-400"> empty</div>
          )}
          {/* Cart total at the bottom */}



          {pagedItems?.length > 0 && (
            <div className="mt-4 text-right font-extrabold text-lg text-blue-400 border-t border-gray-700 pt-3">
              Total: {formatPrice(total)}
            </div>
          )}



        </div>
        {/* Paging navigation (right side, vertically centered) */}
        {(hasPrev || hasNext) && (
          <div className="flex flex-col justify-center items-center h-full ml-2">
            <button
              onClick={() => setCartPage(p => Math.max(0, p - 1))}
              disabled={!hasPrev}
              className={`w-10 h-10 flex items-center justify-center rounded-full bg-[#232323] shadow border-2 border-gray-700 text-white text-xl transition-all duration-150 hover:bg-blue-600 hover:border-blue-500 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-400 mb-2 ${!hasPrev ? 'opacity-40 cursor-not-allowed' : ''}`}
              aria-label="Page Up"
            >
              <FaChevronUp />
            </button>
            <button
              onClick={() => setCartPage(p => Math.min(totalPages - 1, p + 1))}
              disabled={!hasNext}
              className={`w-10 h-10 flex items-center justify-center rounded-full bg-[#232323] shadow border-2 border-gray-700 text-white text-xl transition-all duration-150 hover:bg-blue-600 hover:border-blue-500 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-400 mt-2 ${!hasNext ? 'opacity-40 cursor-not-allowed' : ''}`}
              aria-label="Page Down"
            >
              <FaChevronDown />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CartInfo;
