import React from 'react'

const MiniCard = ({title, icon, number, footerNum}) => {
  return (
    <div className='bg-white dark:bg-gray-800 py-5 px-5 rounded-lg w-[50%] border border-gray-200 dark:border-gray-700 transition-colors'>
        <div className='flex items-start justify-between'>
            <h1 className='text-gray-900 dark:text-gray-100 text-lg font-semibold tracking-wide'>{title}</h1>
            <button className={`${title === "Total Earnings" ? "bg-green-500" : "bg-orange-500"} p-3 rounded-lg text-white text-2xl hover:opacity-90 transition-opacity`}>{icon}</button>
        </div>
        <div>
            <h1 className='text-gray-900 dark:text-gray-100 text-4xl font-bold mt-5'>{
              title === "Total Earnings" ? `₹${number}` : number}</h1>
            <h1 className='text-gray-900 dark:text-gray-100 text-lg mt-2'><span className='text-green-500'>{footerNum}%</span> than yesterday</h1>
        </div>
    </div>
  )
}

export default MiniCard