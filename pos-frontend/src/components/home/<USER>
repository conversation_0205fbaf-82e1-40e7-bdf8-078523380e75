import React from "react";
import { FaSearch } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import OrderList from "./OrderList";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { getOrders } from "../../https/index";

const RecentOrders = () => {
  const navigate = useNavigate();

  const { data: resData, isError } = useQuery({
    queryKey: ["home-orders"],
    queryFn: async () => {
      return await getOrders();
    },
    placeholderData: keepPreviousData,
  });

  if (isError) {
    enqueueSnackbar("Something went wrong!", { variant: "error" });
  }

  const handleViewAll = () => {
    navigate("/orders");
  };

  return (
    <div className="px-8 mt-6">
      <div className="bg-white dark:bg-gray-800 w-full h-[450px] rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
        <div className="flex justify-between items-center px-6 py-4">
          <h1 className="text-gray-900 dark:text-gray-100 text-lg font-semibold tracking-wide">
            Recent Orders
          </h1>
          <button
            onClick={handleViewAll}
            className="text-blue-600 dark:text-blue-400 text-sm font-semibold hover:underline cursor-pointer"
          >
            View all
          </button>
        </div>

        <div className="flex items-center gap-4 bg-gray-100 dark:bg-gray-700 rounded-[15px] px-6 py-4 mx-6 transition-colors">
          <FaSearch className="text-gray-600 dark:text-gray-300" />
          <input
            type="text"
            placeholder="Search recent orders"
            className="bg-transparent outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 flex-1"
          />
        </div>

        {/* Order list */}
        <div className="mt-4 px-6 overflow-y-scroll h-[300px] scrollbar-hide">
          {resData?.data.data.length > 0 ? (
            resData.data.data.map((order) => {
              return <OrderList key={order._id} order={order} />;
            })
          ) : (
            <p className="col-span-3 text-gray-500">No orders available</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default RecentOrders;
