import React from "react";
import { keepPreviousData, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { getOrders, updateOrder } from "../../https/index";
import { formatDateAndTime } from "../../utils";
import { useSelector } from "react-redux";

const RecentOrders = () => {
  const queryClient = useQueryClient();
  const { defaultCurrency } = useSelector((state) => state.general);

  const handleStatusChange = (orderId, status) => {
    orderStatusUpdateMutation.mutate({ orderId, data: { status } });
  };

  const orderStatusUpdateMutation = useMutation({
    mutationFn: ({ orderId, data }) => updateOrder(orderId, data),
    onSuccess: () => {
      enqueueSnackbar("Order status updated successfully!", { variant: "success" });
      queryClient.invalidateQueries(["dashboard-orders"]); // Refresh dashboard order list
    },
    onError: (error) => {
      const message = error.response?.data?.message || "Failed to update order status";
      enqueueSnackbar(message, { variant: "error" });
    }
  });

  const { data: resData, isError } = useQuery({
    queryKey: ["dashboard-orders"],
    queryFn: async () => {
      return await getOrders({
        paginate: false,
        date_range_preset: "today"
      });
    },
    placeholderData: keepPreviousData,
  });

  if (isError) {
    enqueueSnackbar("Failed to fetch orders", { variant: "error" });
    return null;
  }

  const orders = resData?.data?.data || [];

  return (
    <div className="container mx-auto bg-[#262626] p-4 rounded-lg">
      <h2 className="text-[#f5f5f5] text-xl font-semibold mb-4">
        Recent Orders
      </h2>
      <div className="overflow-x-auto">
        <table className="w-full text-left text-[#f5f5f5]">
          <thead className="bg-[#333] text-[#ababab]">
            <tr>
              <th className="p-3">Order ID</th>
              <th className="p-3">Table</th>
              <th className="p-3">Status</th>
              <th className="p-3">Date & Time</th>
              <th className="p-3">Items</th>
              <th className="p-3">Total</th>
              <th className="p-3 text-center">Payment Method</th>
            </tr>
          </thead>
          <tbody>
            {orders.map((order) => (
              <tr
                key={order.id}
                className="border-b border-gray-600 hover:bg-[#333]"
              >
                <td className="p-4">#{order.id}</td>
                <td className="p-4">{order.table.name} ({order.table.code})</td>
                <td className="p-4">
                  <select
                    className={`bg-[#1a1a1a] text-[#f5f5f5] border border-gray-500 p-2 rounded-lg focus:outline-none ${order.status === "Closed"
                      ? "text-green-500"
                      : "text-yellow-500"
                      }`}
                    value={order.status}
                    onChange={(e) => handleStatusChange(order.id, e.target.value)}
                  >
                    <option className="text-yellow-500" value="Opened">
                      Opened
                    </option>
                    <option className="text-green-500" value="Closed">
                      Closed
                    </option>
                  </select>
                </td>
                <td className="p-4">{formatDateAndTime(order.createdAt)}</td>
                <td className="p-4">{order.order_items?.length || 0} Items</td>
                <td className="p-4">{defaultCurrency}{order.totalAmount}</td>
                <td className="p-4 text-center">
                  {order.payment_method_name || "-"}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default RecentOrders;
