/**
 * ===================================================================
 * THEMED COMPONENT FACTORY
 * ===================================================================
 * 
 * Pre-built components that automatically inherit theme styling.
 * These components can be used throughout the application without
 * manual theme configuration.
 */

import React from 'react';
import { useTheme } from '../../hooks/useTheme';

/**
 * Themed Card Component
 * Automatically applies appropriate background, border, and shadow
 */
export const ThemedCard = ({ 
  children, 
  className = '', 
  padding = 'p-6',
  hover = false,
  ...props 
}) => {
  const { adaptive, cn } = useTheme();
  
  return (
    <div 
      className={cn(
        adaptive('card'),
        padding,
        'rounded-lg shadow-sm transition-all duration-200',
        hover && 'hover:shadow-md',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Themed Button Component
 * Automatically applies theme-aware styling based on variant
 */
export const ThemedButton = ({ 
  children, 
  variant = 'primary', 
  size = 'md',
  className = '',
  disabled = false,
  ...props 
}) => {
  const { cn } = useTheme();
  
  const variants = {
    primary: 'bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white border border-blue-600 hover:border-blue-700',
    secondary: 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600',
    ghost: 'bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 border border-transparent',
    danger: 'bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white border border-red-600 hover:border-red-700'
  };
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };
  
  return (
    <button 
      className={cn(
        'font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800',
        variants[variant],
        sizes[size],
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};

/**
 * Themed Input Component
 * Automatically applies theme-aware styling
 */
export const ThemedInput = ({ 
  className = '',
  error = false,
  ...props 
}) => {
  const { adaptive, cn } = useTheme();
  
  return (
    <input 
      className={cn(
        adaptive('input'),
        'w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200',
        error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
        className
      )}
      {...props}
    />
  );
};

/**
 * Themed Select Component
 * Automatically applies theme-aware styling
 */
export const ThemedSelect = ({ 
  children,
  className = '',
  error = false,
  ...props 
}) => {
  const { adaptive, cn } = useTheme();
  
  return (
    <select 
      className={cn(
        adaptive('input'),
        'w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200',
        error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
        className
      )}
      {...props}
    >
      {children}
    </select>
  );
};

/**
 * Themed Textarea Component
 * Automatically applies theme-aware styling
 */
export const ThemedTextarea = ({ 
  className = '',
  error = false,
  rows = 4,
  ...props 
}) => {
  const { adaptive, cn } = useTheme();
  
  return (
    <textarea 
      className={cn(
        adaptive('input'),
        'w-full px-3 py-2 rounded-lg resize-vertical focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200',
        error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
        className
      )}
      rows={rows}
      {...props}
    />
  );
};

/**
 * Themed Modal Component
 * Automatically applies theme-aware styling
 */
export const ThemedModal = ({ 
  children, 
  isOpen, 
  onClose,
  title,
  size = 'md',
  className = ''
}) => {
  const { adaptive, cn } = useTheme();
  
  const sizes = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div 
        className={cn(
          adaptive('card'),
          'relative w-full rounded-lg shadow-lg max-h-[90vh] overflow-y-auto',
          sizes[size],
          className
        )}
      >
        {title && (
          <div className={cn(adaptive('border'), 'border-b px-6 py-4')}>
            <h2 className={cn(adaptive('text'), 'text-xl font-semibold')}>
              {title}
            </h2>
          </div>
        )}
        
        <div className="p-6">
          {children}
        </div>
      </div>
    </div>
  );
};

/**
 * Themed Table Component
 * Automatically applies theme-aware styling
 */
export const ThemedTable = ({ 
  children,
  className = '',
  ...props 
}) => {
  const { adaptive, cn } = useTheme();
  
  return (
    <div className={cn(adaptive('card'), 'overflow-hidden', className)}>
      <table className="w-full" {...props}>
        {children}
      </table>
    </div>
  );
};

/**
 * Themed Table Header Component
 */
export const ThemedTableHeader = ({ children, className = '' }) => {
  const { adaptive, cn } = useTheme();
  
  return (
    <thead className={cn('bg-gray-50 dark:bg-gray-700', className)}>
      {children}
    </thead>
  );
};

/**
 * Themed Table Row Component
 */
export const ThemedTableRow = ({ children, className = '', hover = true }) => {
  const { cn } = useTheme();
  
  return (
    <tr className={cn(
      'border-b border-gray-200 dark:border-gray-600 transition-colors',
      hover && 'hover:bg-gray-50 dark:hover:bg-gray-700',
      className
    )}>
      {children}
    </tr>
  );
};

/**
 * Themed Table Cell Component
 */
export const ThemedTableCell = ({ 
  children, 
  className = '', 
  header = false,
  ...props 
}) => {
  const { adaptive, cn } = useTheme();
  
  const Component = header ? 'th' : 'td';
  
  return (
    <Component 
      className={cn(
        'px-4 py-3',
        header 
          ? 'text-left font-medium text-gray-600 dark:text-gray-400' 
          : adaptive('text'),
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
};

/**
 * Themed Badge Component
 * Automatically applies theme-aware styling based on variant
 */
export const ThemedBadge = ({ 
  children, 
  variant = 'default',
  size = 'md',
  className = ''
}) => {
  const { cn } = useTheme();
  
  const variants = {
    default: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
    success: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
    warning: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
    error: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
    info: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
  };
  
  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-2.5 py-1.5 text-sm',
    lg: 'px-3 py-2 text-base'
  };
  
  return (
    <span className={cn(
      'inline-flex items-center font-medium rounded-full',
      variants[variant],
      sizes[size],
      className
    )}>
      {children}
    </span>
  );
};

/**
 * Themed Loading Spinner Component
 */
export const ThemedSpinner = ({ size = 'md', className = '' }) => {
  const { cn } = useTheme();
  
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };
  
  return (
    <div className={cn(
      'animate-spin rounded-full border-2 border-gray-300 dark:border-gray-600 border-t-blue-600',
      sizes[size],
      className
    )} />
  );
};
