/**
 * ===================================================================
 * AUTOMATIC THEME INHERITANCE PROVIDER
 * ===================================================================
 *
 * This component automatically applies theme changes to the entire
 * application without requiring manual updates to individual components.
 */

import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

const ThemeProvider = ({ children }) => {
  // Get dark mode state from Redux
  const darkMode = useSelector((state) => state.general.darkMode);

  useEffect(() => {
    // Get the HTML element
    const htmlElement = document.documentElement;

    // Apply or remove the dark class based on darkMode state
    if (darkMode) {
      htmlElement.classList.add('dark');
    } else {
      htmlElement.classList.remove('dark');
    }

    // Add a data attribute for CSS targeting
    htmlElement.setAttribute('data-theme', darkMode ? 'dark' : 'light');

    // Update meta theme-color for mobile browsers
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }
    metaThemeColor.setAttribute('content', darkMode ? '#1f2937' : '#ffffff');

    // Prevent flash of unstyled content on theme change
    htmlElement.style.setProperty('--theme-transition', 'none');

    // Re-enable transitions after a brief delay
    const timeoutId = setTimeout(() => {
      htmlElement.style.removeProperty('--theme-transition');
    }, 50);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [darkMode]);

  // Add a class to prevent transitions on initial load
  useEffect(() => {
    const htmlElement = document.documentElement;
    htmlElement.classList.add('no-transition');

    const timeoutId = setTimeout(() => {
      htmlElement.classList.remove('no-transition');
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  return <>{children}</>;
};

export default ThemeProvider;
