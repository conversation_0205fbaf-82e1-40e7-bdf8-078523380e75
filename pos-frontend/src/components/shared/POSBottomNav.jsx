import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import FunctionChecksDropdown from "./FunctionChecksDropdown";
import BottomNav from "./BottomNav";
import { IoArrowBackOutline } from 'react-icons/io5';

const PosBottomNav = ({
  onPrint,
  onClear,
  onTransCkl,
  isEditMode,
  onBack,
  onVoid,
  voidEnabled,
  voidQuantity,
  onSendOrder,
  onPay,
  onFunctionChecks,
  cartIsEmpty = false,
  payLabelAsBack = false,
  payButtonDisabled = false,
  voidButtonDisabled = false,
  printButtonDisabled = false,
  sendOrderButtonDisabled = false,
  functionChecksButtonDisabled = false,
}) => {
  // Live updating time with seconds
  const [currentTime, setCurrentTime] = useState(new Date());
  const [functionChecksOpen, setFunctionChecksOpen] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleFunctionChecksClick = () => {
    setFunctionChecksOpen(!functionChecksOpen);
  };

  const handleFunctionAction = (actionId) => {
    // Call the original onFunctionChecks with the specific action
    if (onFunctionChecks) {
      onFunctionChecks(actionId);
    }
  };

  const actions = [
    isEditMode
      ? {
        label: (
          <span className="flex items-center justify-center">
            <IoArrowBackOutline className="mr-2" />BACK
          </span>
        ),
        color: "bg-gray-600 hover:bg-gray-500 active:bg-gray-700",
        onClick: onBack,
        flex: "flex-1",
      }
      : {
        label:
          payButtonDisabled && !payLabelAsBack ? (
            <span className="flex items-center justify-center"><IoArrowBackOutline className="mr-2" />BACK</span>
          ) : (
            cartIsEmpty ? "BACK" : "CLEAR"
          ),
        color: cartIsEmpty ? "bg-gray-600 hover:bg-gray-500 active:bg-gray-700" : "bg-red-600 hover:bg-red-500 active:bg-red-700",
        onClick: onClear,
        flex: "flex-1"
      },
    {
      label: "TRANS CANCEL",
      color: "bg-amber-600 hover:bg-amber-500 active:bg-amber-700",
      onClick: onTransCkl,
      flex: "flex-1"
    },
    {
      label: voidQuantity > 0 ? `VOID (${voidQuantity})` : "VOID",
      color: "bg-red-700 hover:bg-red-600 active:bg-red-800",
      onClick: voidEnabled && !voidButtonDisabled ? onVoid : undefined,
      disabled: !voidEnabled || voidButtonDisabled,
      flex: "flex-1"
    },
    {
      label: "FUNCTION CHECKS",
      color: "bg-purple-600 hover:bg-purple-500 active:bg-purple-700",
      onClick: functionChecksButtonDisabled ? undefined : handleFunctionChecksClick,
      flex: "flex-1",
      hasDropdown: true,
      disabled: functionChecksButtonDisabled,
    },
    payLabelAsBack
      ? {
        label: <span className="flex items-center justify-center"><IoArrowBackOutline className="mr-2" />BACK</span>,
        color: "bg-gray-600 hover:bg-gray-500 active:bg-gray-700",
        onClick: onPay,
        flex: "flex-1"
      }
      : {
        label: "PAY",
        color: "bg-green-600 hover:bg-green-500 active:bg-green-700",
        onClick: onPay,
        flex: "flex-1",
        disabled: payButtonDisabled,
      },
    {
      label: "PRINT",
      color: "bg-blue-600 hover:bg-blue-500 active:bg-blue-700",
      onClick: printButtonDisabled ? undefined : onPrint,
      flex: "flex-1",
      disabled: printButtonDisabled,
    },
    {
      label: "SEND ORDER",
      color: "bg-yellow-500 hover:bg-yellow-400 active:bg-yellow-600 text-black",
      onClick: sendOrderButtonDisabled ? undefined : onSendOrder,
      flex: "flex-1",
      disabled: sendOrderButtonDisabled,
    },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-700 flex flex-col z-50 shadow-lg">
      {/* Main button row - increased height for touch */}
      <div className="flex items-center justify-between px-3 py-3 gap-3 h-20">
        <div className="flex gap-3 flex-1">
          {actions.map((action, index) => (
            <div key={index} className={`${action.flex} mx-1 relative`}>
              <button
                className={`${action.color} text-white font-bold px-2 py-4 rounded-xl w-full text-xl transition-all duration-100
                  ${action.disabled ? 'opacity-50 cursor-not-allowed' : 'transform active:scale-95'}`}
                onClick={action.onClick}
                disabled={action.disabled}
              >
                {action.label}
                {action.hasDropdown && (
                  <span className="ml-1 text-sm">▲</span>
                )}
              </button>
              {action.hasDropdown && (
                <FunctionChecksDropdown
                  isOpen={functionChecksOpen}
                  onToggle={setFunctionChecksOpen}
                  onActionSelect={handleFunctionAction}
                  isEditMode={isEditMode}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Status bar with ticking clock */}
      <div className="bg-gray-800 text-gray-300 text-sm px-4 py-2 flex justify-between items-center h-6">
        <BottomNav />
      </div>
    </div>
  );
};

PosBottomNav.propTypes = {
  onPrint: PropTypes.func.isRequired,
  onClear: PropTypes.func.isRequired,
  onTransCkl: PropTypes.func.isRequired,
  isEditMode: PropTypes.bool.isRequired,
  onBack: PropTypes.func,
  onVoid: PropTypes.func.isRequired,
  voidEnabled: PropTypes.bool.isRequired,
  voidQuantity: PropTypes.number.isRequired,
  onSendOrder: PropTypes.func.isRequired,
  onPay: PropTypes.func.isRequired,
  onFunctionChecks: PropTypes.func.isRequired,
  cartIsEmpty: PropTypes.bool,
  payLabelAsBack: PropTypes.bool,
  payButtonDisabled: PropTypes.bool,
  voidButtonDisabled: PropTypes.bool,
  printButtonDisabled: PropTypes.bool,
  sendOrderButtonDisabled: PropTypes.bool,
  functionChecksButtonDisabled: PropTypes.bool,
};

export default PosBottomNav;