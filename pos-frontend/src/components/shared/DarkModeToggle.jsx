import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { IoSunny, IoMoon } from 'react-icons/io5';
import { setGeneralSettings } from '../../redux/slices/generalSlice';

const DarkModeToggle = () => {
  const darkMode = useSelector((state) => state.general.darkMode);
  const dispatch = useDispatch();

  const toggleDarkMode = () => {
    // For now, just toggle the local state
    // In a real implementation, this would also update the backend
    dispatch(setGeneralSettings({ darkMode: !darkMode }));
  };

  return (
    <button
      onClick={toggleDarkMode}
      className="bg-gray-200 dark:bg-gray-700 rounded-[15px] p-3 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
      title={darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
    >
      {darkMode ? (
        <IoSunny className="text-yellow-500 text-2xl" />
      ) : (
        <IoMoon className="text-gray-700 dark:text-gray-200 text-2xl" />
      )}
    </button>
  );
};

export default DarkModeToggle;
