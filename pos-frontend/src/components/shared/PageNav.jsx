import React from "react";
import PropTypes from "prop-types";

const PageNav = ({ onPageUp, onPageDown, disableUp = false, disableDown = false }) => {
    return (
        <div className="flex flex-col w-full h-full">
            <button
                onClick={onPageUp}
                disabled={disableUp}
                className={`flex-1 h-1/2 w-full bg-[#444] text-white text-2xl font-extrabold border-2 border-gray-700 rounded-none focus:outline-none focus:ring-2 focus:ring-blue-400 uppercase transition-all duration-150 hover:bg-gray-600 hover:border-blue-500 active:scale-95 ${disableUp ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
                PAGE UP
            </button>
            <button
                onClick={onPageDown}
                disabled={disableDown}
                className={`flex-1 h-1/2 w-full bg-[#444] text-white text-2xl font-extrabold border-2 border-gray-700 rounded-none focus:outline-none focus:ring-2 focus:ring-blue-400 uppercase transition-all duration-150 hover:bg-gray-600 hover:border-blue-500 active:scale-95 ${disableDown ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
                PAGE DOWN
            </button>
        </div>
    );
};

PageNav.propTypes = {
    onPageUp: PropTypes.func.isRequired,
    onPageDown: PropTypes.func.isRequired,
    disableUp: PropTypes.bool,
    disableDown: PropTypes.bool,
};

export default PageNav; 