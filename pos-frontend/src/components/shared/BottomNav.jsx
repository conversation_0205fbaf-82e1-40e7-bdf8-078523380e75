import React, { useState, useEffect } from "react";
import CustomerGreeting from "../shared/CustomerGreeting";

const BottomNav = () => {
    const [currentTime, setCurrentTime] = useState(new Date());
    const [employeeName, setEmployeeName] = useState("");
    const [selectedRevenueCenter, setSelectedRevenueCenter] = useState("");

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);
        return () => clearInterval(timer);
    }, []);

    // Load employee name once on mount
    useEffect(() => {
        try {
            const user = JSON.parse(localStorage.getItem("user"));
            setEmployeeName(user?.name || "");
        } catch {
            setEmployeeName("");
        }
    }, []);

    // Poll only the revenue center
    useEffect(() => {
        const updateRevenueCenter = () => {
            try {
                const revenueCenter = JSON.parse(localStorage.getItem("revenueCenter"));
                setSelectedRevenueCenter(revenueCenter?.name || "");
            } catch {
                setSelectedRevenueCenter("");
            }
        };

        updateRevenueCenter(); // Initial load
        const interval = setInterval(updateRevenueCenter, 500);
        return () => clearInterval(interval);
    }, []);

    return (
        <div className="fixed bottom-0 left-0 right-0 bg-gray-800 text-gray-300 text-sm px-4 py-2 flex justify-between items-center h-6 z-50 shadow-lg">
            <span>Lambda POS v1.0</span>
            <span> SVP : <CustomerGreeting /></span>
            <span>User: {employeeName || "-"}</span>
            <span>RVC: {selectedRevenueCenter}</span>
            <span className="font-mono">
                {new Intl.DateTimeFormat('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true,
                })
                    .format(currentTime)
                    .replace(/(am|pm)/i, match => match.toUpperCase())}
            </span>
        </div>
    );
};

export default BottomNav;