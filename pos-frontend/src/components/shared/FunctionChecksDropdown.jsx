import React, { useState, useRef, useEffect } from "react";
import PropTypes from "prop-types";

const FunctionChecksDropdown = ({ onActionSelect, isOpen, onToggle, isEditMode }) => {
    const dropdownRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                onToggle(false);
            }
        };

        if (isOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isOpen, onToggle]);

    const functionActions = [
        {
            id: "split_check",
            label: "Split Check",
            description: "Split the current check into multiple checks",
            icon: "✂️",
            color: "hover:bg-blue-100",
            disabled: !isEditMode,
        },
        {
            id: "merge_check",
            label: "Merge Check",
            description: "Merge multiple checks into one",
            icon: "🔗",
            color: "hover:bg-green-100"
        },
    ];

    const handleActionClick = (actionId) => {
        onActionSelect(actionId);
        onToggle(false);
    };

    if (!isOpen) return null;

    return (
        <div
            ref={dropdownRef}
            className="absolute bottom-full left-0 mb-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50"
        >
            <div className="p-2">
                <div className="text-sm font-semibold text-gray-700 mb-2 px-3 py-2 border-b border-gray-100">
                    Function Checks
                </div>
                <div className="max-h-96 overflow-y-auto">
                    {functionActions.map((action) => (
                        <button
                            key={action.id}
                            onClick={() => handleActionClick(action.id)}
                            className={`w-full text-left px-3 py-3 rounded-md transition-colors ${action.color} flex items-center gap-3 ${action.disabled ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                            disabled={action.disabled}
                        >
                            <span className="text-xl">{action.icon}</span>
                            <div className="flex-1">
                                <div className="font-medium text-gray-900">{action.label}</div>
                                <div className="text-sm text-gray-500">{action.description}</div>
                            </div>
                        </button>
                    ))}
                </div>
            </div>
        </div>
    );
};

FunctionChecksDropdown.propTypes = {
    onActionSelect: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    onToggle: PropTypes.func.isRequired,
    isEditMode: PropTypes.bool.isRequired,
};

export default FunctionChecksDropdown; 