import React, { useEffect, useState } from "react";
import { FaUserCircle } from "react-icons/fa";
import { FaBell } from "react-icons/fa";
import logo from "../../assets/images/logo.png";
import { useDispatch, useSelector } from "react-redux";
import { IoLogOut } from "react-icons/io5";
import { useMutation } from "@tanstack/react-query";
import { logout, getGeneralSettings } from "../../https";
import { removeUser } from "../../redux/slices/userSlice";
import { useNavigate } from "react-router-dom";
import { MdDashboard } from "react-icons/md";
import { MdRestaurant } from "react-icons/md";
import { IoStorefront, IoBarChart } from "react-icons/io5";
import DarkModeToggle from "./DarkModeToggle";

const Header = () => {
  const userData = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [companyName, setCompanyName] = useState("Restro");
  const [companyLogo, setCompanyLogo] = useState("");

  useEffect(() => {
    getGeneralSettings().then(res => {
      const data = res.data?.data;
      setCompanyName(data?.companyName || "Restro");
      setCompanyLogo(data?.companyLogo || "");
    }).catch(() => {
      setCompanyName("Restro");
      setCompanyLogo("");
    });
  }, []);

  const logoutMutation = useMutation({
    mutationFn: () => logout(),
    onSuccess: (data) => {
      console.log(data);
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      dispatch(removeUser());
      navigate("/auth");
    },
    onError: (error) => {
      console.log(error);
    },
  });

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  return (
    <header className="flex justify-between items-center py-4 px-8 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      {/* LOGO */}
      <div onClick={() => navigate("/")} className="flex items-center gap-2 cursor-pointer">
        <img
          src={companyLogo || logo}
          className="h-8 w-8"
          alt="company logo"
          onError={(e) => {
            e.target.src = logo;
          }}
        />
        <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100 tracking-wide">
          {companyName}
        </h1>
      </div>

      {/* LOGGED USER DETAILS */}
      <div className="flex items-center gap-4">
        {userData.role === "administrator" && (
          <div onClick={() => navigate("/kot")} className="bg-gray-200 dark:bg-gray-700 rounded-[15px] p-3 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
            <MdRestaurant className="text-gray-700 dark:text-gray-200 text-2xl" />
          </div>
        )}
        {userData.role === "administrator" && (
          <div onClick={() => navigate("/dashboard")} className="bg-gray-200 dark:bg-gray-700 rounded-[15px] p-3 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
            <MdDashboard className="text-gray-700 dark:text-gray-200 text-2xl" />
          </div>
        )}
        {userData.role === "administrator" && (
          <div onClick={() => navigate("/inventory")} className="bg-gray-200 dark:bg-gray-700 rounded-[15px] p-3 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
            <IoStorefront className="text-gray-700 dark:text-gray-200 text-2xl" />
          </div>
        )}
        {userData.role === "administrator" && (
          <div onClick={() => navigate("/stock-reports")} className="bg-gray-200 dark:bg-gray-700 rounded-[15px] p-3 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
            <IoBarChart className="text-gray-700 dark:text-gray-200 text-2xl" />
          </div>
        )}
        <div className="bg-gray-200 dark:bg-gray-700 rounded-[15px] p-3 cursor-pointer">
          <FaBell className="text-gray-700 dark:text-gray-200 text-2xl" />
        </div>
        <DarkModeToggle />
        <div className="flex items-center gap-3 cursor-pointer">
          <FaUserCircle className="text-gray-700 dark:text-gray-200 text-4xl" />
          <div className="flex flex-col items-start">
            <h1 className="text-md text-gray-900 dark:text-gray-100 font-semibold tracking-wide">
              {userData.name || "TEST USER"}
            </h1>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              {userData.role || "Role"}
            </p>
          </div>
          <IoLogOut
            onClick={handleLogout}
            className="text-gray-700 dark:text-gray-200 ml-2 hover:text-red-500 dark:hover:text-red-400 transition-colors"
            size={40}
          />
        </div>
      </div>
    </header>
  );
};

export default Header;
