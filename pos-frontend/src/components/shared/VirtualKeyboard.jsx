import React, { useState, useRef, useEffect } from 'react';
import { IoKeypad, IoClose } from 'react-icons/io5';

const VirtualKeyboard = ({ 
  value = "", 
  onChange, 
  placeholder = "Type here...",
  className = "",
  inputClassName = "",
  keyboardClassName = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isShift, setIsShift] = useState(false);
  const [isCapsLock, setIsCapsLock] = useState(false);
  const inputRef = useRef(null);
  const [cursorPosition, setCursorPosition] = useState(0);

  // Update cursor position when input changes
  useEffect(() => {
    if (inputRef.current) {
      setCursorPosition(inputRef.current.selectionStart || 0);
    }
  }, [value]);

  // Keyboard layout
  const keyboardLayout = [
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='],
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'"],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/']
  ];

  const shiftMap = {
    '1': '!', '2': '@', '3': '#', '4': '$', '5': '%',
    '6': '^', '7': '&', '8': '*', '9': '(', '0': ')',
    '-': '_', '=': '+', '[': '{', ']': '}',
    ';': ':', "'": '"', ',': '<', '.': '>', '/': '?'
  };

  const insertAtCursor = (char) => {
    const currentValue = value || "";
    const newValue = currentValue.slice(0, cursorPosition) + char + currentValue.slice(cursorPosition);
    onChange(newValue);
    
    // Update cursor position
    const newPosition = cursorPosition + char.length;
    setCursorPosition(newPosition);
    
    // Focus and set cursor position in input
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(newPosition, newPosition);
      }
    }, 0);
  };

  const handleKeyPress = (key) => {
    let char = key;
    
    // Handle shift/caps lock
    if (key.match(/[a-z]/)) {
      if (isShift || isCapsLock) {
        char = char.toUpperCase();
      }
    } else if (isShift && shiftMap[key]) {
      char = shiftMap[key];
    }
    
    insertAtCursor(char);
    
    // Reset shift after key press (but not caps lock)
    if (isShift) {
      setIsShift(false);
    }
  };

  const handleSpecialKey = (action) => {
    const currentValue = value || "";
    
    switch (action) {
      case 'space':
        insertAtCursor(' ');
        break;
      case 'backspace':
        if (cursorPosition > 0) {
          const newValue = currentValue.slice(0, cursorPosition - 1) + currentValue.slice(cursorPosition);
          onChange(newValue);
          const newPosition = cursorPosition - 1;
          setCursorPosition(newPosition);
          setTimeout(() => {
            if (inputRef.current) {
              inputRef.current.focus();
              inputRef.current.setSelectionRange(newPosition, newPosition);
            }
          }, 0);
        }
        break;
      case 'enter':
        insertAtCursor('\n');
        break;
      case 'shift':
        setIsShift(!isShift);
        break;
      case 'caps':
        setIsCapsLock(!isCapsLock);
        break;
      case 'clear':
        onChange('');
        setCursorPosition(0);
        if (inputRef.current) {
          inputRef.current.focus();
        }
        break;
      default:
        break;
    }
  };

  const handleInputChange = (e) => {
    if (onChange) {
      onChange(e.target.value);
    }
    setCursorPosition(e.target.selectionStart || 0);
  };

  const handleInputClick = (e) => {
    setCursorPosition(e.target.selectionStart || 0);
  };

  const toggleKeyboard = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Input with keyboard toggle */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value || ""}
          onChange={handleInputChange}
          onClick={handleInputClick}
          placeholder={placeholder}
          className={`w-full pr-12 ${inputClassName}`}
        />
        <button
          type="button"
          onClick={toggleKeyboard}
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded transition-colors
            ${isOpen 
              ? 'text-blue-600 bg-blue-50 hover:bg-blue-100' 
              : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'
            }`}
        >
          <IoKeypad size={20} />
        </button>
      </div>

      {/* Virtual Keyboard */}
      {isOpen && (
        <div className={`absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-2xl p-4 z-50 ${keyboardClassName}`}>
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Virtual Keyboard</h3>
            <button
              type="button"
              onClick={() => setIsOpen(false)}
              className="p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <IoClose size={16} />
            </button>
          </div>

          {/* Keyboard Keys */}
          <div className="space-y-2">
            {/* Number row */}
            <div className="flex gap-1 justify-center">
              {keyboardLayout[0].map((key) => (
                <button
                  key={key}
                  type="button"
                  onClick={() => handleKeyPress(key)}
                  className="w-10 h-10 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded border text-sm font-medium transition-colors"
                >
                  {isShift && shiftMap[key] ? shiftMap[key] : key}
                </button>
              ))}
              <button
                type="button"
                onClick={() => handleSpecialKey('backspace')}
                className="px-3 h-10 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 rounded border text-sm font-medium transition-colors text-red-600 dark:text-red-400"
              >
                ⌫
              </button>
            </div>

            {/* QWERTY rows */}
            {keyboardLayout.slice(1).map((row, rowIndex) => (
              <div key={rowIndex} className="flex gap-1 justify-center">
                {row.map((key) => (
                  <button
                    key={key}
                    type="button"
                    onClick={() => handleKeyPress(key)}
                    className="w-10 h-10 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded border text-sm font-medium transition-colors"
                  >
                    {(isShift || isCapsLock) && key.match(/[a-z]/) ? key.toUpperCase() : key}
                  </button>
                ))}
              </div>
            ))}

            {/* Bottom row with special keys */}
            <div className="flex gap-1 justify-center items-center">
              <button
                type="button"
                onClick={() => handleSpecialKey('caps')}
                className={`px-3 h-10 rounded border text-sm font-medium transition-colors ${
                  isCapsLock 
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' 
                    : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                Caps
              </button>
              <button
                type="button"
                onClick={() => handleSpecialKey('shift')}
                className={`px-3 h-10 rounded border text-sm font-medium transition-colors ${
                  isShift 
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' 
                    : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                Shift
              </button>
              <button
                type="button"
                onClick={() => handleSpecialKey('space')}
                className="flex-1 h-10 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded border text-sm font-medium transition-colors"
              >
                Space
              </button>
              <button
                type="button"
                onClick={() => handleSpecialKey('clear')}
                className="px-3 h-10 bg-orange-100 dark:bg-orange-900/30 hover:bg-orange-200 dark:hover:bg-orange-900/50 rounded border text-sm font-medium transition-colors text-orange-600 dark:text-orange-400"
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VirtualKeyboard;