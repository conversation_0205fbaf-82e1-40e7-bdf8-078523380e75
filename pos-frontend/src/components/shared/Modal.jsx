import React from 'react';
import { motion } from 'framer-motion';


const Modal = ({ isOpen, onClose, title, children, size = "default", height = null }) => {
  if (!isOpen) return null;

  // Size configurations
  const sizeClasses = {
    small: "max-w-md max-h-[60vh]",
    default: "max-w-lg max-h-[70vh]",
    large: "max-w-4xl max-h-[85vh]",
    xlarge: "max-w-6xl max-h-[90vh]",
    fullscreen: "max-w-[95vw] max-h-[95vh]"
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
      <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className={`bg-[#1a1a1a] rounded-lg shadow-lg w-full ${sizeClasses[size]} mx-4 overflow-hidden flex flex-col ${height ? height : ''}`}>
        <div className="flex justify-between items-center px-6 py-4 border-b border-b-[#333] flex-shrink-0">
          <h2 className="text-xl text-[#f5f5f5] font-semibold">{title}</h2>
          <button
            className="text-gray-500 text-3xl hover:text-gray-300 p-2 rounded-lg hover:bg-gray-700 transition-colors touch-manipulation"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <div className="p-8 overflow-y-auto flex-1">
          {children}
        </div>
      </motion.div>
    </div>
  );
};

export default Modal;
