import React, { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query"
import { emailLogin, getGeneralSettings } from "../../https/index"
import { enqueueSnackbar } from "notistack";
import { useDispatch } from "react-redux";
import { setUser } from "../../redux/slices/userSlice";
import { setGeneralSettings } from "../../redux/slices/generalSlice";
import { useNavigate } from "react-router-dom";

const EmailLogin = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [backgroundImage, setBackgroundImage] = useState("");
    const [companyName, setCompanyName] = useState("");

    useEffect(() => {
        getGeneralSettings().then(res => {
            const data = res.data?.data;
            setBackgroundImage(data?.backgroundImage || "");
            setCompanyName(data?.companyName || "");
            dispatch(setGeneralSettings({
                defaultCurrency: data?.defaultCurrency || "GH₵",
                backgroundImage: data?.backgroundImage || "",
                companyName: data?.companyName || "",
                darkMode: data?.darkMode || false,
            }));
        }).catch(() => {
            setBackgroundImage("");
            setCompanyName("");
        });
    }, [dispatch]);

    const handleSubmit = (e) => {
        e.preventDefault();
        if (email && password) {
            loginMutation.mutate({ email, password });
        } else {
            enqueueSnackbar("Please enter both email and password", { variant: "warning" });
        }
    }

    const loginMutation = useMutation({
        mutationFn: (reqData) => emailLogin(reqData),
        onSuccess: (res) => {
            const { token, user } = res.data.data;
            if (token?.access) localStorage.setItem('accessToken', token.access);
            if (token?.refresh) localStorage.setItem('refreshToken', token.refresh);
            dispatch(setUser({
                _id: user.id,
                name: ((user.firstName || "") + (user.lastName ? (" " + user.lastName) : "")).trim(),
                phone: user.mobile ? String(user.mobile) : "",
                email: user.email || "",
                role: user.role || ""
            }));

            // Redirect based on role
            if (user.role === "administrator") {
                navigate("/");
            } else {
                navigate("/orders");
            }
        },
        onError: (error) => {
            const { response } = error;
            enqueueSnackbar(response?.data?.message || "Login failed", { variant: "error" });
            setPassword("");
        }
    })

    return (
        <div
            className="fixed inset-0 w-screen h-screen bg-cover bg-center flex items-center justify-center"
            style={{
                backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'linear-gradient(135deg, #1e293b 0%, #3b82f6 50%, #1e293b 100%)',
                backgroundColor: '#1e293b'
            }}
        >
            <div className="relative z-10 flex flex-col items-center justify-center w-full h-full">
                <div
                    className="bg-white rounded-3xl shadow-2xl flex flex-col items-center px-12 py-12"
                    style={{
                        minWidth: '700px',
                        maxWidth: '700px',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)'
                    }}
                >
                    {companyName && (
                        <div className="text-center mb-10">
                            <h1 className="text-4xl font-bold text-gray-800 mb-2">
                                {companyName}
                            </h1>
                            <div
                                className="w-24 h-1 rounded-full mx-auto"
                                style={{ background: 'linear-gradient(to right, #3b82f6, #8b5cf6)' }}
                            ></div>
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="w-full space-y-8">
                        <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-3">
                                Email Address
                            </label>
                            <input
                                type="email"
                                id="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                className="w-full px-6 py-4 rounded-xl border-2 border-gray-200 bg-gray-50 text-gray-800 focus:outline-none focus:border-blue-400 transition-all duration-300 text-lg"
                                placeholder="Enter your email"
                                required
                                disabled={loginMutation.isLoading}
                            />
                        </div>

                        <div>
                            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-3">
                                Password
                            </label>
                            <input
                                type="password"
                                id="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                className="w-full px-6 py-4 rounded-xl border-2 border-gray-200 bg-gray-50 text-gray-800 focus:outline-none focus:border-blue-400 transition-all duration-300 text-lg"
                                placeholder="Enter your password"
                                required
                                disabled={loginMutation.isLoading}
                            />
                        </div>

                        <button
                            type="submit"
                            disabled={!email || !password || loginMutation.isLoading}
                            className="w-full rounded-xl py-5 text-xl font-bold transition-all duration-300 shadow-lg relative overflow-hidden mt-8"
                            style={{
                                background: email && password && !loginMutation.isLoading
                                    ? 'linear-gradient(to right, #2563eb, #7c3aed)'
                                    : 'linear-gradient(to right, #cbd5e1, #94a3b8)',
                                color: email && password && !loginMutation.isLoading ? 'white' : '#64748b',
                                border: '2px solid',
                                borderColor: email && password && !loginMutation.isLoading ? '#2563eb' : '#cbd5e1',
                                cursor: email && password && !loginMutation.isLoading ? 'pointer' : 'not-allowed',
                                transform: 'scale(1)'
                            }}
                            onMouseEnter={(e) => {
                                if (email && password && !loginMutation.isLoading) {
                                    e.target.style.transform = 'scale(1.02)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                e.target.style.transform = 'scale(1)';
                            }}
                        >
                            <span className="flex items-center justify-center">
                                {loginMutation.isLoading ? (
                                    <>
                                        <div
                                            className="rounded-full h-6 w-6 border-2 border-white mr-2"
                                            style={{
                                                borderTopColor: 'transparent',
                                                animation: 'spin 1s linear infinite'
                                            }}
                                        ></div>
                                        Signing In...
                                    </>
                                ) : (
                                    "Sign In"
                                )}
                            </span>
                        </button>
                    </form>

                    <div className="text-center mt-8">
                        <p className="text-sm text-gray-500">Sign in with your email and password</p>
                    </div>
                </div>
            </div>

            <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
        </div>
    );
};

export default EmailLogin; 