import React, { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query"
import { codeLogin, getGeneralSettings } from "../../https/index"
import { enqueueSnackbar } from "notistack";
import { useDispatch } from "react-redux";
import { setUser } from "../../redux/slices/userSlice";
import { setGeneralSettings } from "../../redux/slices/generalSlice";
import { useNavigate } from "react-router-dom";

const KEYPAD = [
  ["7", "8", "9"],
  ["4", "5", "6"],
  ["1", "2", "3"],
  ["CE", "0", "Quit"]
];

const Login = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [code, setCode] = useState("");
  const [backgroundImage, setBackgroundImage] = useState("");
  const [companyName, setCompanyName] = useState("");

  useEffect(() => {
    getGeneralSettings().then(res => {
      const data = res.data?.data;
      setBackgroundImage(data?.backgroundImage || "");
      setCompanyName(data?.companyName || "");
      dispatch(setGeneralSettings({
        defaultCurrency: data?.defaultCurrency || "GH₵",
        backgroundImage: data?.backgroundImage || "",
        companyName: data?.companyName || "",
        trackStock: data?.trackStock || false,
        darkMode: data?.darkMode || false,
      }));
    }).catch(() => {
      setBackgroundImage("");
      setCompanyName("");
    });
  }, [dispatch]);

  // Add keyboard event listener
  useEffect(() => {
    const handleKeyDown = (event) => {
      const key = event.key;

      // Handle number keys (0-9)
      if (/^[0-9]$/.test(key) && code.length < 4) {
        setCode(prevCode => prevCode + key);
      }
      // Handle Enter key
      else if (key === 'Enter') {
        handleEnter();
      }
      // Handle Backspace key
      else if (key === 'Backspace') {
        setCode(prevCode => prevCode.slice(0, -1));
      }
      // Handle Escape key (clear code)
      else if (key === 'Escape') {
        setCode("");
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup event listener
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [code]); // Include code in dependencies to access current value

  const handleKeypad = (val) => {
    if (val === "CE") {
      setCode("");
    } else if (val === "Quit") {
      setCode("");
    } else if (code.length < 4 && /\d/.test(val)) {
      setCode(code + val);
    }
  }

  const handleEnter = () => {
    if (code.length === 4) {
      loginMutation.mutate({ code });
    } else {
      enqueueSnackbar("Code must be 4 digits", { variant: "warning" });
    }
  }

  const loginMutation = useMutation({
    mutationFn: (reqData) => codeLogin(reqData),
    onSuccess: (res) => {
      const { token, user } = res.data.data;
      if (token?.access) localStorage.setItem('accessToken', token.access);
      if (token?.refresh) localStorage.setItem('refreshToken', token.refresh);
      dispatch(setUser({
        _id: user.id,
        name: ((user.firstName || "") + (user.lastName ? (" " + user.lastName) : "")).trim(),
        phone: user.mobile ? String(user.mobile) : "",
        email: user.email || "",
        role: user.role || ""
      }));

      // Redirect based on role
      if (user.role === "administrator") {
        navigate("/");
      } else {
        navigate("/orders");
      }
    },
    onError: (error) => {
      const { response } = error;
      enqueueSnackbar(response?.data?.message || "Login failed", { variant: "error" });
      setCode("");
    }
  })

  return (
    <div
      className="fixed inset-0 w-screen h-screen bg-cover bg-center flex items-center justify-center"
      style={{
        backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'linear-gradient(135deg, #1e293b 0%, #3b82f6 50%, #1e293b 100%)',
        backgroundColor: '#1e293b'
      }}
    >
      <div className="relative z-10 flex flex-col items-center justify-center w-full h-full">
        <div
          className="bg-white rounded-3xl shadow-2xl flex flex-col items-center px-12 py-8"
          style={{
            minWidth: '700px',
            maxWidth: '700px',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)'
          }}
        >
          {companyName && (
            <div className="text-center mb-6">
              <h1 className="text-4xl font-bold text-gray-800 mb-2">
                {companyName}
              </h1>
              <div
                className="w-24 h-1 rounded-full mx-auto"
                style={{ background: 'linear-gradient(to right, #3b82f6, #8b5cf6)' }}
              ></div>
            </div>
          )}

          <div className="relative mb-6">
            <input
              type="password"
              value={code}
              readOnly
              className="w-full text-4xl text-center p-6 rounded-2xl border-2 border-gray-200 bg-gray-50 text-gray-800 focus:outline-none focus:border-blue-400 transition-all duration-300"
              maxLength={4}
              placeholder="••••"
              style={{
                letterSpacing: '2rem',
                minWidth: '300px',
                boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.1)'
              }}
            />
          </div>

          <div className="grid grid-cols-3 gap-4 w-full mb-6">
            {KEYPAD.flat().map((val, idx) => (
              <button
                key={val}
                onClick={() => handleKeypad(val)}
                className={`h-16 rounded-xl text-2xl font-bold transition-all duration-200 shadow-lg ${val === "CE"
                  ? "text-white border-2"
                  : val === "Quit"
                    ? "text-white border-2"
                    : "text-gray-700 border-2 border-gray-200 hover:border-gray-300"
                  }`}
                style={{
                  background: val === "CE"
                    ? 'linear-gradient(to right, #ef4444, #dc2626)'
                    : val === "Quit"
                      ? 'linear-gradient(to right, #64748b, #475569)'
                      : 'linear-gradient(to right, #ffffff, #f8fafc)',
                  borderColor: val === "CE"
                    ? '#ef4444'
                    : val === "Quit"
                      ? '#64748b'
                      : '#e2e8f0',
                  transform: 'scale(1)',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'scale(1)';
                }}
              >
                {val}
              </button>
            ))}
          </div>

          <button
            onClick={handleEnter}
            disabled={code.length !== 4 || loginMutation.isLoading}
            className="w-full rounded-2xl py-4 text-2xl font-bold transition-all duration-300 shadow-lg relative overflow-hidden mb-4"
            style={{
              background: code.length === 4 && !loginMutation.isLoading
                ? 'linear-gradient(to right, #2563eb, #7c3aed)'
                : 'linear-gradient(to right, #cbd5e1, #94a3b8)',
              color: code.length === 4 && !loginMutation.isLoading ? 'white' : '#64748b',
              border: '2px solid',
              borderColor: code.length === 4 && !loginMutation.isLoading ? '#2563eb' : '#cbd5e1',
              cursor: code.length === 4 && !loginMutation.isLoading ? 'pointer' : 'not-allowed',
              transform: 'scale(1)'
            }}
            onMouseEnter={(e) => {
              if (code.length === 4 && !loginMutation.isLoading) {
                e.target.style.transform = 'scale(1.02)';
              }
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'scale(1)';
            }}
          >
            <span className="flex items-center justify-center">
              {loginMutation.isLoading ? (
                <>
                  <div
                    className="rounded-full h-6 w-6 border-2 border-white mr-2"
                    style={{
                      borderTopColor: 'transparent',
                      animation: 'spin 1s linear infinite'
                    }}
                  ></div>
                  Authenticating...
                </>
              ) : (
                "Enter"
              )}
            </span>
          </button>

          <div className="text-center">
            <p className="text-sm text-gray-500">Enter your 4-digit access code</p>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default Login;