import React from "react";
import { IoTrophy, IoBasket, IoCash, IoTrendingUp } from "react-icons/io5";

const PopularItemsSection = ({ popularByQuantity, popularByRevenue, isLoading, currency = "GH₵" }) => {
  // Extract data arrays from API responses
  const quantityDataArray = popularByQuantity?.data?.data || [];
  const revenueDataArray = popularByRevenue?.data?.data || [];

console.log('popularByQuantity aggre', popularByQuantity)

  const formatCurrency = (amount) => {
    // Handle custom currency symbols like GH₵
    if (currency === "GH₵" || currency.includes("₵")) {
      return `${currency} ${new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
      }).format(amount || 0)}`;
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency === "GH₵" ? "USD" : currency,
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number || 0);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="w-64 h-6 bg-gray-600 rounded animate-pulse"></div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(2)].map((_, index) => (
            <div key={index} className="bg-[#262626] rounded-lg p-6 animate-pulse">
              <div className="w-48 h-6 bg-gray-600 rounded mb-4"></div>
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div className="w-32 h-4 bg-gray-600 rounded"></div>
                    <div className="w-16 h-4 bg-gray-600 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const getRankIcon = (index) => {
    if (index === 0) return "🥇";
    if (index === 1) return "🥈";
    if (index === 2) return "🥉";
    return `${index + 1}.`;
  };

  const getRankColor = (index) => {
    if (index === 0) return "bg-yellow-500";
    if (index === 1) return "bg-gray-400";
    if (index === 2) return "bg-orange-500";
    return "bg-[#1a1a1a]";
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-[#f5f5f5] flex items-center gap-2">
        <IoTrophy className="text-2xl" />
        Popular Items
      </h2>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Popular by Quantity */}
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoBasket className="text-xl text-blue-400" />
            Most Sold Items (by Quantity)
          </h3>
          <div className="space-y-3">
            {quantityDataArray.length === 0 ? (
              <p className="text-[#ababab] text-center py-8">No data available</p>
            ) : (
              quantityDataArray.map((item, index) => (
                <div
                  key={item.articleId || item.article_id || index}
                  className="flex items-center justify-between p-3 bg-[#1a1a1a] rounded-lg hover:bg-[#333] transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${getRankColor(index)}`}>
                      {index < 3 ? getRankIcon(index) : index + 1}
                    </div>
                    <div>
                      <p className="text-[#f5f5f5] font-medium">{item.articleName || item.article_name}</p>
                      <p className="text-[#ababab] text-sm">
                        {item.categoryName || item.category_name} • {formatCurrency(item.articlePrice || item.article_price)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-[#f5f5f5] font-bold text-lg">
                      {formatNumber(item.totalQuantitySold || item.total_quantity_sold)}
                    </p>
                    <p className="text-[#ababab] text-sm">
                      {formatNumber(item.totalOrders || item.total_orders)} orders
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Popular by Revenue */}
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoCash className="text-xl text-green-400" />
            Top Revenue Items
          </h3>
          <div className="space-y-3">
            {revenueDataArray.length === 0 ? (
              <p className="text-[#ababab] text-center py-8">No data available</p>
            ) : (
              revenueDataArray.map((item, index) => (
                <div
                  key={item.articleId || item.article_id || index}
                  className="flex items-center justify-between p-3 bg-[#1a1a1a] rounded-lg hover:bg-[#333] transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${getRankColor(index)}`}>
                      {index < 3 ? getRankIcon(index) : index + 1}
                    </div>
                    <div>
                      <p className="text-[#f5f5f5] font-medium">{item.articleName || item.article_name}</p>
                      <p className="text-[#ababab] text-sm">
                        {item.categoryName || item.category_name} • {formatNumber(item.totalQuantitySold || item.total_quantity_sold)} sold
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-[#f5f5f5] font-bold text-lg">
                      {formatCurrency(item.totalRevenue || item.total_revenue)}
                    </p>
                    <p className="text-[#ababab] text-sm">
                      {formatNumber(item.totalOrders || item.total_orders)} orders
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      
      {(popularByQuantity.length > 0 || popularByRevenue.length > 0) && (
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoTrendingUp className="text-xl" />
            Popular Items Summary
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {popularByQuantity.length > 0 && (
              <>
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-400">
                    {formatNumber(popularByQuantity[0]?.totalQuantitySold || popularByQuantity[0]?.total_quantity_sold || 0)}
                  </p>
                  <p className="text-sm text-[#ababab]">Top Item Quantity</p>
                  <p className="text-xs text-[#ababab]">{popularByQuantity[0]?.articleName || popularByQuantity[0]?.article_name}</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-400">
                    {formatNumber(popularByQuantity.reduce((sum, item) => sum + (item.totalQuantitySold || item.total_quantity_sold || 0), 0))}
                  </p>
                  <p className="text-sm text-[#ababab]">Total Top 10 Quantity</p>
                </div>
              </>
            )}
            {popularByRevenue.length > 0 && (
              <>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-400">
                    {formatCurrency(popularByRevenue[0]?.totalRevenue || popularByRevenue[0]?.total_revenue || 0)}
                  </p>
                  <p className="text-sm text-[#ababab]">Top Item Revenue</p>
                  <p className="text-xs text-[#ababab]">{popularByRevenue[0]?.articleName || popularByRevenue[0]?.article_name}</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-400">
                    {formatCurrency(popularByRevenue.reduce((sum, item) => sum + (item.totalRevenue || item.total_revenue || 0), 0))}
                  </p>
                  <p className="text-sm text-[#ababab]">Total Top 10 Revenue</p>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PopularItemsSection;
