import React, { useState } from "react";
import { MdTrendingUp, MdExpandMore, MdExpandLess } from "react-icons/md";

const FamilyBreakdownSection = ({
  salesHierarchy,
  isLoading,
  expandedFamilies,
  setExpandedFamilies,
  expandedSubFamilies,
  setExpandedSubFamilies,
  expandAll,
  setExpandAll,
  currency = "GH₵"
}) => {
  const formatCurrency = (amount) => {
    // Handle custom currency symbols like GH₵
    if (currency === "GH₵" || currency.includes("₵")) {
      return `${currency} ${new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
      }).format(amount || 0)}`;
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency === "GH₵" ? "USD" : currency,
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const formatNumber = (num) => {
    return Number(num || 0).toLocaleString();
  };

  const toggleFamily = (familyId) => {
    setExpandedFamilies((prev) => ({ ...prev, [familyId]: !prev[familyId] }));
  };

  const toggleSubFamily = (subFamilyId) => {
    setExpandedSubFamilies((prev) => ({ ...prev, [subFamilyId]: !prev[subFamilyId] }));
  };

  // Expand/collapse all logic
  const handleExpandAll = () => {
    const hierarchy = salesHierarchy?.data?.data?.[0]?.hierarchy || {};
    const allFamilyIds = Object.values(hierarchy).map(({ family }) => family.id);
    const allSubFamilyIds = Object.values(hierarchy)
      .flatMap(({ subFamilies }) => Object.values(subFamilies).map((sf) => sf.id));
    
    if (!expandAll) {
      // Expand all
      setExpandedFamilies(Object.fromEntries(allFamilyIds.map((id) => [id, true])));
      setExpandedSubFamilies(Object.fromEntries(allSubFamilyIds.map((id) => [id, true])));
    } else {
      // Collapse all
      setExpandedFamilies({});
      setExpandedSubFamilies({});
    }
    setExpandAll((prev) => !prev);
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 transition-colors">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <MdTrendingUp className="w-6 h-6" />
            Detailed Sales Breakdown
          </h2>
        </div>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Loading sales breakdown...</p>
        </div>
      </div>
    );
  }

  if (!salesHierarchy?.data?.data?.[0]?.hierarchy) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 transition-colors">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <MdTrendingUp className="w-6 h-6" />
            Detailed Sales Breakdown
          </h2>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-600 dark:text-gray-400">No sales data available for the selected period</p>
        </div>
      </div>
    );
  }

  const hierarchy = salesHierarchy.data.data[0].hierarchy;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 transition-colors">
      {/* Header with Expand/Collapse All Button */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <MdTrendingUp className="w-6 h-6" />
          Detailed Sales Breakdown
        </h2>
        <button
          onClick={handleExpandAll}
          className={`px-6 py-3 rounded-lg font-semibold transition-all text-sm flex items-center gap-2 ${
            expandAll
              ? 'bg-blue-600 text-white shadow-lg'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600'
          }`}
        >
          {expandAll ? <MdExpandLess className="w-4 h-4" /> : <MdExpandMore className="w-4 h-4" />}
          {expandAll ? 'Collapse All' : 'Expand All'}
        </button>
      </div>

      {/* Family Breakdown */}
      <div className="space-y-2">
        {Object.values(hierarchy).map(({ family, subFamilies }) => (
          <div key={family.id} className="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 transition-colors">
            {/* Family Header */}
            <button
              onClick={() => toggleFamily(family.id)}
              className="w-full text-left p-4 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
                    {family.name.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <h3 className="text-white font-semibold text-lg">{family.name}</h3>
                    <p className="text-gray-400 text-sm">
                      {formatNumber(family.totalQuantitySold || family.total_quantity_sold)} items sold
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-white font-bold text-lg">
                      {formatCurrency(family.totalRevenue || family.total_revenue)}
                    </div>
                    <div className="text-xs text-gray-400">
                      {formatNumber(family.totalQuantitySold || family.total_quantity_sold)} items
                    </div>
                  </div>
                  <div className="text-gray-400 text-xl">
                    {expandedFamilies[family.id] ? '▼' : '▶'}
                  </div>
                </div>
              </div>
            </button>

            {/* Sub-families */}
            {expandedFamilies[family.id] && (
              <div className="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600 transition-colors">
                {Object.values(subFamilies).map((subFamily) => (
                  <div key={subFamily.id} className="border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                    <button
                      onClick={() => toggleSubFamily(subFamily.id)}
                      className="w-full text-left p-4 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center text-white text-sm font-bold">
                            {subFamily.name.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <h4 className="text-white font-medium">{subFamily.name}</h4>
                            <p className="text-gray-400 text-xs">
                              {subFamily.articles?.length || 0} articles
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <div className="text-white font-bold">
                              {formatCurrency(subFamily.totalRevenue || subFamily.total_revenue)}
                            </div>
                            <div className="text-xs text-gray-400">
                              {formatNumber(subFamily.totalQuantitySold || subFamily.total_quantity_sold)} items
                            </div>
                          </div>
                          <div className="text-gray-400">
                            {expandedSubFamilies[subFamily.id] ? '▼' : '▶'}
                          </div>
                        </div>
                      </div>
                    </button>

                    {/* Articles */}
                    {expandedSubFamilies[subFamily.id] && (
                      <div className="bg-gray-100 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 transition-colors">
                        {subFamily.articles?.map((article) => (
                          <div key={article.id} className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                            <div className="flex items-center gap-3">
                              <div className="w-6 h-6 bg-purple-600 rounded flex items-center justify-center text-white text-xs font-bold">
                                {article.name?.charAt(0).toUpperCase() || 'A'}
                              </div>
                              <div>
                                <p className="text-white font-medium">{article.name}</p>
                                <p className="text-gray-400 text-xs">
                                  {formatCurrency(article.price)} each
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-white font-bold">
                                {formatCurrency(article.totalRevenue || article.total_revenue)}
                              </div>
                              <div className="text-xs text-gray-400">
                                {formatNumber(article.totalQuantitySold || article.total_quantity_sold)} sold
                              </div>
                            </div>
                          </div>
                        )) || (
                          <div className="p-4 text-center text-gray-400 text-sm">
                            No articles found
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default FamilyBreakdownSection;
