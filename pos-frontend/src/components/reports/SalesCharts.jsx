import React from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>Axis, 
  <PERSON><PERSON><PERSON>s, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts";
import { IoBar<PERSON>hart, IoTrendingUp, IoPieChart } from "react-icons/io5";

const SalesCharts = ({ salesByHour, salesByDay, salesByFamily, isLoading, currency = "GH₵" }) => {
  const formatCurrency = (amount) => {
    // Handle custom currency symbols like GH₵
    if (currency === "GH₵" || currency.includes("₵")) {
      return `${currency} ${new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 0,
      }).format(amount || 0)}`;
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency === "GH₵" ? "USD" : currency,
      minimumFractionDigits: 0,
    }).format(amount || 0);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number || 0);
  };

  // Extract data arrays from API responses
  const hourDataArray = salesByHour?.data?.data || [];
  const dayDataArray = salesByDay?.data?.data || [];
  const familyDataArray = salesByFamily?.data?.data || [];

  // Process hour data
  const hourData = hourDataArray.map(item => ({
    hour: `${item.hour}:00`,
    revenue: item.totalRevenue || item.total_revenue || 0,
    orders: item.totalOrders || item.total_orders || 0
  }));

  // Process day data
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const dayData = dayDataArray.map(item => ({
    day: dayNames[item.dayOfWeek || item.day_of_week] || `Day ${item.dayOfWeek || item.day_of_week}`,
    revenue: item.totalRevenue || item.total_revenue || 0,
    orders: item.totalOrders || item.total_orders || 0
  }));

  // Process family data for pie chart
  const familyData = familyDataArray.slice(0, 8).map((item, index) => ({
    name: item.familyName || item.family_name || item.article__sub_family__family__name || 'Unknown',
    value: parseFloat(item.totalRevenue || item.total_revenue || 0),
    color: `hsl(${(index * 45) % 360}, 70%, 60%)`
  }));

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'];

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 animate-pulse border border-gray-200 dark:border-gray-700 transition-colors">
            <div className="w-48 h-6 bg-gray-300 dark:bg-gray-600 rounded mb-4"></div>
            <div className="w-full h-64 bg-gray-300 dark:bg-gray-600 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
        <IoBarChart className="text-2xl" />
        Sales Analytics Charts
      </h2>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales by Hour */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 transition-colors">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
            <IoTrendingUp className="text-xl" />
            Sales by Hour
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={hourData}>
              <CartesianGrid strokeDasharray="3 3" stroke="currentColor" className="text-gray-300 dark:text-gray-600" />
              <XAxis
                dataKey="hour"
                stroke="currentColor"
                className="text-gray-600 dark:text-gray-400"
                fontSize={12}
              />
              <YAxis
                stroke="currentColor"
                className="text-gray-600 dark:text-gray-400"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--tooltip-bg)',
                  border: '1px solid var(--tooltip-border)',
                  borderRadius: '8px',
                  color: 'var(--tooltip-text)'
                }}
                wrapperStyle={{
                  '--tooltip-bg': 'rgb(255 255 255)',
                  '--tooltip-border': 'rgb(229 231 235)',
                  '--tooltip-text': 'rgb(17 24 39)'
                }}
                className="dark:[--tooltip-bg:rgb(31_41_55)] dark:[--tooltip-border:rgb(75_85_99)] dark:[--tooltip-text:rgb(243_244_246)]"
                formatter={(value, name) => [
                  name === 'revenue' ? formatCurrency(value) : formatNumber(value),
                  name === 'revenue' ? 'Revenue' : 'Orders'
                ]}
              />
              <Legend />
              <Bar dataKey="revenue" fill="#0088FE" name="Revenue" />
              <Bar dataKey="orders" fill="#00C49F" name="Orders" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Sales by Day of Week */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 transition-colors">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
            <IoTrendingUp className="text-xl" />
            Sales by Day of Week
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={dayData}>
              <CartesianGrid strokeDasharray="3 3" stroke="currentColor" className="text-gray-300 dark:text-gray-600" />
              <XAxis
                dataKey="day"
                stroke="currentColor"
                className="text-gray-600 dark:text-gray-400"
                fontSize={12}
              />
              <YAxis
                stroke="currentColor"
                className="text-gray-600 dark:text-gray-400"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--tooltip-bg)',
                  border: '1px solid var(--tooltip-border)',
                  borderRadius: '8px',
                  color: 'var(--tooltip-text)'
                }}
                wrapperStyle={{
                  '--tooltip-bg': 'rgb(255 255 255)',
                  '--tooltip-border': 'rgb(229 231 235)',
                  '--tooltip-text': 'rgb(17 24 39)'
                }}
                className="dark:[--tooltip-bg:rgb(31_41_55)] dark:[--tooltip-border:rgb(75_85_99)] dark:[--tooltip-text:rgb(243_244_246)]"
                formatter={(value, name) => [
                  name === 'revenue' ? formatCurrency(value) : formatNumber(value),
                  name === 'revenue' ? 'Revenue' : 'Orders'
                ]}
              />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#0088FE" 
                strokeWidth={3}
                name="Revenue"
                dot={{ fill: '#0088FE', strokeWidth: 2, r: 4 }}
              />
              <Line 
                type="monotone" 
                dataKey="orders" 
                stroke="#00C49F" 
                strokeWidth={3}
                name="Orders"
                dot={{ fill: '#00C49F', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Sales by Family - Full Width */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 transition-colors">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
          <IoPieChart className="text-xl" />
          Sales by Product Family
        </h3>
        {familyData.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Pie Chart */}
            <div>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={familyData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {familyData.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1a1a1a',
                      border: '1px solid #444',
                      borderRadius: '8px',
                      color: '#f5f5f5'
                    }}
                    formatter={(value) => [formatCurrency(value), 'Revenue']}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Legend and Details */}
            <div className="space-y-3">
              <h4 className="text-md font-medium text-[#f5f5f5]">Family Breakdown</h4>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {familyData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-[#1a1a1a] rounded">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: COLORS[index % COLORS.length] }}
                      ></div>
                      <span className="text-sm text-[#f5f5f5]">{item.name}</span>
                    </div>
                    <span className="text-sm font-medium text-[#f5f5f5]">
                      {formatCurrency(item.value)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-[#ababab]">No family sales data available for the selected period</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SalesCharts;
