import React, { useState, useEffect } from "react";
import { IoCalendar, IoPeople, IoFilter, IoClose, IoSave, IoStar, IoStarOutline } from "react-icons/io5";
import { MdStore, MdDesktopWindows } from "react-icons/md";
import { getUserSettings, updateUserSettings } from "../../https/index";

const ReportFilters = ({
  filters,
  setFilters,
  users,
  revenueCenters,
  workstations,
  onClose
}) => {
  const [savedFilters, setSavedFilters] = useState([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [filterName, setFilterName] = useState("");
  const [loading, setLoading] = useState(false);
  const [tempFilters, setTempFilters] = useState(filters);
  const [useCustomDate, setUseCustomDate] = useState(false);

  const datePresets = [
    { value: "today", label: "Today" },
    { value: "yesterday", label: "Yesterday" },
    { value: "this_week", label: "This Week" },
    { value: "last_week", label: "Last Week" },
    { value: "this_month", label: "This Month" },
    { value: "last_month", label: "Last Month" },
    { value: "last_7_days", label: "Last 7 Days" },
    { value: "last_30_days", label: "Last 30 Days" },
    { value: "last_90_days", label: "Last 90 Days" },
    { value: "first_quarter", label: "Q1 (Jan-Mar)" },
    { value: "second_quarter", label: "Q2 (Apr-Jun)" },
    { value: "third_quarter", label: "Q3 (Jul-Sep)" },
    { value: "fourth_quarter", label: "Q4 (Oct-Dec)" },
    { value: "this_year", label: "This Year" }
  ];

  // Load saved filters on component mount and initialize states
  useEffect(() => {
    loadSavedFilters();
    setTempFilters(filters);
    // Check if custom dates are being used
    setUseCustomDate(!!(filters.startDate || filters.endDate));
  }, [filters]);

  const loadSavedFilters = async () => {
    try {
      const response = await getUserSettings();
      const userSettings = response.data?.data;
      const reportFilters = userSettings?.data?.reportFilters || [];
      setSavedFilters(reportFilters);
    } catch (error) {
      console.error("Error loading saved filters:", error);
    }
  };

  const saveCurrentFilters = async () => {
    if (!filterName.trim()) return;

    setLoading(true);
    try {
      const response = await getUserSettings();
      const userSettings = response.data?.data;
      const currentReportFilters = userSettings?.data?.reportFilters || [];

      const newFilter = {
        id: Date.now().toString(),
        name: filterName.trim(),
        filters: { ...tempFilters },
        createdAt: new Date().toISOString()
      };

      const updatedFilters = [...currentReportFilters, newFilter];

      await updateUserSettings({
        data: {
          ...userSettings?.data,
          reportFilters: updatedFilters
        }
      });

      setSavedFilters(updatedFilters);
      setFilterName("");
      setShowSaveDialog(false);
    } catch (error) {
      console.error("Error saving filters:", error);
    } finally {
      setLoading(false);
    }
  };

  const applySavedFilter = (savedFilter) => {
    // Apply saved filter immediately and close modal
    setFilters(savedFilter.filters);
    onClose();
  };

  const deleteSavedFilter = async (filterId) => {
    setLoading(true);
    try {
      const response = await getUserSettings();
      const userSettings = response.data?.data;
      const currentReportFilters = userSettings?.data?.reportFilters || [];

      const updatedFilters = currentReportFilters.filter(f => f.id !== filterId);

      await updateUserSettings({
        data: {
          ...userSettings?.data,
          reportFilters: updatedFilters
        }
      });

      setSavedFilters(updatedFilters);
    } catch (error) {
      console.error("Error deleting filter:", error);
    } finally {
      setLoading(false);
    }
  };



 
  const handleFilterChange = (key, value) => {
    setTempFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleCustomDateToggle = (checked) => {
    setUseCustomDate(checked);
    if (checked) {
      // Clear date preset when using custom dates
      setTempFilters(prev => ({ ...prev, dateRangePreset: "" }));
    } else {
      // Clear custom dates when using presets
      setTempFilters(prev => ({
        ...prev,
        startDate: null,
        endDate: null,
        dateRangePreset: "today"
      }));
    }
  };

  const handleApply = () => {
    // Apply the temporary filters to the actual filters
    const finalFilters = { ...tempFilters };

    // Ensure only one date method is used
    if (useCustomDate) {
      finalFilters.dateRangePreset = "";
    } else {
      finalFilters.startDate = null;
      finalFilters.endDate = null;
    }

    setFilters(finalFilters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters = {
      dateRangePreset: "today",
      startDate: null,
      endDate: null,
      status: "Paid",
      userId: "",
      revenueCenter: "",
      workstation: "",
      servingPeriod: "",
    };
    setTempFilters(resetFilters);
    setUseCustomDate(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <IoFilter className="text-lg text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Report Filters</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <IoClose className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Saved Filters Section */}
        {savedFilters.length > 0 && (
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-2">
                <IoStar className="text-yellow-500" />
                Saved Filters
              </h3>
              <button
                onClick={() => setShowSaveDialog(true)}
                className="flex items-center gap-1 px-3 py-1 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <IoSave className="w-3 h-3" />
                Save Current
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {savedFilters.map((savedFilter) => (
                <div key={savedFilter.id} className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-1">
                  <button
                    onClick={() => applySavedFilter(savedFilter)}
                    className="text-sm text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {savedFilter.name}
                  </button>
                  <button
                    onClick={() => deleteSavedFilter(savedFilter.id)}
                    className="text-red-500 hover:text-red-700 ml-1"
                    disabled={loading}
                  >
                    <IoClose className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Save Filter Dialog */}
        {showSaveDialog && (
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center gap-3">
              <input
                type="text"
                value={filterName}
                onChange={(e) => setFilterName(e.target.value)}
                placeholder="Enter filter name..."
                className="flex-1 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyDown={(e) => e.key === 'Enter' && saveCurrentFilters()}
              />
              <button
                onClick={saveCurrentFilters}
                disabled={!filterName.trim() || loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? "Saving..." : "Save"}
              </button>
              <button
                onClick={() => {
                  setShowSaveDialog(false);
                  setFilterName("");
                }}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Save Current Button for when no saved filters exist */}
        {savedFilters.length === 0 && (
          <div className="px-6 py-3 border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setShowSaveDialog(true)}
              className="flex items-center gap-2 px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <IoSave className="w-4 h-4" />
              Save Current Filters
            </button>
          </div>
        )}

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Date Range Filter */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <IoCalendar className="text-lg text-blue-600" />
                <label className="text-sm font-medium text-gray-900 dark:text-white">Date Range</label>
              </div>

              {/* Custom Date Toggle */}
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="customDate"
                  checked={useCustomDate}
                  onChange={(e) => handleCustomDateToggle(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="customDate" className="text-sm text-gray-700 dark:text-gray-300">
                  Custom Date Range
                </label>
              </div>

              {/* Date Presets - only show when not using custom dates */}
              {!useCustomDate && (
                <select
                  value={tempFilters.dateRangePreset}
                  onChange={(e) => handleFilterChange('dateRangePreset', e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {datePresets.map((preset) => (
                    <option key={preset.value} value={preset.value}>
                      {preset.label}
                    </option>
                  ))}
                </select>
              )}

              {/* Custom Date Inputs - only show when using custom dates */}
              {useCustomDate && (
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Start Date</label>
                    <input
                      type="date"
                      value={tempFilters.startDate || ''}
                      onChange={(e) => handleFilterChange('startDate', e.target.value)}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">End Date</label>
                    <input
                      type="date"
                      value={tempFilters.endDate || ''}
                      onChange={(e) => handleFilterChange('endDate', e.target.value)}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* User Filter */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <IoPeople className="text-lg text-blue-600" />
                <label className="text-sm font-medium text-gray-900 dark:text-white">User</label>
              </div>

              <select
                value={tempFilters.userId}
                onChange={(e) => handleFilterChange('userId', e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Users</option>

                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.firstName} {user.lastName}
                  </option>
                )

              )

                }
              </select>
            </div>

            {/* Revenue Center Filter */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <MdStore className="text-lg text-blue-600" />
                <label className="text-sm font-medium text-gray-900 dark:text-white">Revenue Center</label>
              </div>

              <select
                value={tempFilters.revenueCenter}
                onChange={(e) => handleFilterChange('revenueCenter', e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >

                <option value="">All Revenue Centers</option>
                {revenueCenters.map((center) => (
                  <option key={center.id} value={center.id}>
                    {center.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Workstation Filter */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <MdDesktopWindows className="text-lg text-blue-600" />
                <label className="text-sm font-medium text-gray-900 dark:text-white">Workstation</label>
              </div>

              <select
                value={tempFilters.workstation}
                onChange={(e) => handleFilterChange('workstation', e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Workstations</option>
                {workstations.map((workstation) => (
                  <option key={workstation.id} value={workstation.id}>
                    {workstation.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <IoFilter className="text-lg text-blue-600" />
                <label className="text-sm font-medium text-gray-900 dark:text-white">Status</label>
              </div>

              <select
                value={tempFilters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="Paid">Paid</option>
                <option value="Pending">Pending</option>
                <option value="Cancelled">Cancelled</option>
                <option value="">All Statuses</option>
              </select>
            </div>

            {/* Serving Period Filter */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <IoFilter className="text-lg text-blue-600" />
                <label className="text-sm font-medium text-gray-900 dark:text-white">Serving Period</label>
              </div>

              <select
                value={tempFilters.servingPeriod || ''}
                onChange={(e) => handleFilterChange('servingPeriod', e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Periods</option>
                <option value="Morning">Morning</option>
                <option value="Lunch">Lunch</option>
                <option value="Afternoon">Afternoon</option>
                <option value="Evening">Evening</option>
                <option value="Supper">Supper</option>
                <option value="Night">Night</option>
              </select>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleReset}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            Reset
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleApply}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReportFilters;
