import React from "react";
import { 
  IoStatsChart, 
  IoTime, 
  IoCash, 
  IoCard, 
  IoGift,
  IoTrash,
  IoCheckmarkCircle,
  IoCloseCircle
} from "react-icons/io5";

const AnalyticsSection = ({
  orderStatusAnalytics,
  servingPeriodAnalytics,
  tipsAnalytics,
  paymentAnalytics,
  discountAnalytics,
  voidAnalytics,
  isLoading,
  currency = "GH₵"
}) => {
  const formatCurrency = (amount) => {
    // Handle custom currency symbols like GH₵
    if (currency === "GH₵" || currency.includes("₵")) {
      return `${currency} ${new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
      }).format(amount || 0)}`;
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency === "GH₵" ? "USD" : currency,
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number || 0);
  };

  const formatPercentage = (percentage) => {
    return `${(percentage || 0).toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="w-64 h-6 bg-gray-600 rounded animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-[#262626] rounded-lg p-6 animate-pulse">
              <div className="w-48 h-6 bg-gray-600 rounded mb-4"></div>
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="w-full h-4 bg-gray-600 rounded"></div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-[#f5f5f5] flex items-center gap-2">
        <IoStatsChart className="text-2xl" />
        Detailed Analytics
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Order Status Analytics */}
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoCheckmarkCircle className="text-xl text-green-400" />
            Order Status
          </h3>
          <div className="space-y-3">
            {orderStatusAnalytics.length === 0 ? (
              <p className="text-[#ababab] text-center py-4">No status data</p>
            ) : (
              orderStatusAnalytics.map((status, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-[#1a1a1a] rounded">
                  <div>
                    <p className="text-[#f5f5f5] font-medium">{status.status}</p>
                    <p className="text-[#ababab] text-sm">{formatPercentage(status.percentage)}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-[#f5f5f5] font-bold">{formatNumber(status.count)}</p>
                    <p className="text-[#ababab] text-sm">{formatCurrency(status.total_revenue)}</p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Serving Period Analytics */}
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoTime className="text-xl text-blue-400" />
            Serving Periods
          </h3>
          <div className="space-y-3">
            {servingPeriodAnalytics.length === 0 ? (
              <p className="text-[#ababab] text-center py-4">No period data</p>
            ) : (
              servingPeriodAnalytics.map((period, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-[#1a1a1a] rounded">
                  <div>
                    <p className="text-[#f5f5f5] font-medium">{period.serving_period || 'Unknown'}</p>
                    <p className="text-[#ababab] text-sm">{formatNumber(period.total_orders)} orders</p>
                  </div>
                  <div className="text-right">
                    <p className="text-[#f5f5f5] font-bold">{formatCurrency(period.total_revenue)}</p>
                    <p className="text-[#ababab] text-sm">{formatCurrency(period.avg_order_value)} avg</p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Tips Analytics */}
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoCash className="text-xl text-green-400" />
            Tips Analytics
          </h3>
          {!tipsAnalytics ? (
            <p className="text-[#ababab] text-center py-4">No tips data</p>
          ) : (
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-400">
                  {formatCurrency(tipsAnalytics.total_tips)}
                </p>
                <p className="text-[#ababab] text-sm">Total Tips</p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-lg font-bold text-[#f5f5f5]">
                    {formatCurrency(tipsAnalytics.avg_tip_amount)}
                  </p>
                  <p className="text-[#ababab] text-xs">Average Tip</p>
                </div>
                <div>
                  <p className="text-lg font-bold text-[#f5f5f5]">
                    {formatPercentage(tipsAnalytics.tip_percentage)}
                  </p>
                  <p className="text-[#ababab] text-xs">Tip %</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Payment Analytics */}
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoCard className="text-xl text-purple-400" />
            Payment Methods
          </h3>
          {!paymentAnalytics || !paymentAnalytics.payment_methods ? (
            <p className="text-[#ababab] text-center py-4">No payment data</p>
          ) : (
            <div className="space-y-3">
              {paymentAnalytics.payment_methods.map((method, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-[#1a1a1a] rounded">
                  <div>
                    <p className="text-[#f5f5f5] font-medium">{method.payment_method}</p>
                    <p className="text-[#ababab] text-sm">{formatPercentage(method.percentage)}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-[#f5f5f5] font-bold">{formatNumber(method.count)}</p>
                    <p className="text-[#ababab] text-sm">{formatCurrency(method.total_amount)}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Discount Analytics */}
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoGift className="text-xl text-orange-400" />
            Discounts
          </h3>
          {!discountAnalytics ? (
            <p className="text-[#ababab] text-center py-4">No discount data</p>
          ) : (
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-400">
                  {formatCurrency(discountAnalytics.total_discount)}
                </p>
                <p className="text-[#ababab] text-sm">Total Discounts</p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-lg font-bold text-[#f5f5f5]">
                    {formatCurrency(discountAnalytics.avg_discount)}
                  </p>
                  <p className="text-[#ababab] text-xs">Average Discount</p>
                </div>
                <div>
                  <p className="text-lg font-bold text-[#f5f5f5]">
                    {formatPercentage(discountAnalytics.discount_percentage)}
                  </p>
                  <p className="text-[#ababab] text-xs">Discount %</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Void Analytics */}
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoTrash className="text-xl text-red-400" />
            Void Analytics
          </h3>
          {!voidAnalytics ? (
            <p className="text-[#ababab] text-center py-4">No void data</p>
          ) : (
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-red-400">
                  {formatNumber(voidAnalytics.total_voids)}
                </p>
                <p className="text-[#ababab] text-sm">Total Voids</p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-lg font-bold text-[#f5f5f5]">
                    {formatCurrency(voidAnalytics.total_void_amount)}
                  </p>
                  <p className="text-[#ababab] text-xs">Void Amount</p>
                </div>
                <div>
                  <p className="text-lg font-bold text-[#f5f5f5]">
                    {formatPercentage(voidAnalytics.void_percentage)}
                  </p>
                  <p className="text-[#ababab] text-xs">Void %</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalyticsSection;
