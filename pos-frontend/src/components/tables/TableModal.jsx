import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Modal from "../shared/Modal";
import VirtualKeyboard from '../shared/VirtualKeyboard'; 


const TableModal = ({ isOpen, onClose, onSave, table }) => {
    const [name, setName] = useState("");
    const [phone, setPhone] = useState("");
    const [guestCount, setGuestCount] = useState(0);

    const isEditing = !!table;

    useEffect(() => {
        if (isOpen) {
            if (isEditing) {
                setName(table.name || "");
                setPhone(table.phone || "");
                setGuestCount(table.guests || table.seats || 0);
            } else {
                setName("");
                setPhone("");
                setGuestCount(0);
            }
        }
    }, [isOpen, isEditing, table]);

    const increment = () => {
        if (guestCount >= 6) return;
        setGuestCount((prev) => prev + 1);
    };
    const decrement = () => {
        if (guestCount <= 0) return;
        setGuestCount((prev) => prev - 1);
    };

    const handleSave = () => {
        if (name.trim()) {
            onSave({ name: name.trim(), phone, guests: guestCount });
            onClose();
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            console.log("Enter pressed");
            handleSave();
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} title={isEditing ? "Edit Table" : "Create Table"} >
        <div className="space-y-8 p-6">
            <div >
                <label className="block text-[#ababab] mb-2 text-lg font-bold">Name <span className="text-red-500">*</span></label>
                <VirtualKeyboard
                    value={name}
                    onChange={setName}
                    placeholder="Table name"
                    inputClassName="bg-[#1f1f1f] rounded-xl p-5 px-6 text-white text-xl placeholder-gray-400 border-none focus:outline-none focus:ring-2 focus:ring-[#F6B100]"
                    keyboardClassName="bg-[#1f1f1f] border-gray-600"
                    className="w-full"
                    keySize="large"
                />
            </div>
            
            <div>
                <label className="block text-[#ababab] mb-2 mt-3 text-lg font-bold">Phone</label>
                <VirtualKeyboard
                    value={phone}
                    onChange={setPhone}
                    placeholder="optional"
                    inputClassName="bg-[#1f1f1f] rounded-xl p-5 px-6 text-white text-xl placeholder-gray-400 border-none focus:outline-none focus:ring-2 focus:ring-[#F6B100]"
                    keyboardClassName="bg-[#1f1f1f] border-gray-600"
                    className="w-full"
                    keySize="large"
                />
            </div>
            
            <div>
                <label className="block mb-2 mt-3 text-lg font-bold text-[#ababab]">Guest</label>
                <div className="flex items-center justify-between bg-[#1f1f1f] px-6 py-5 rounded-xl">
                    <button onClick={decrement} className="text-yellow-500 text-3xl">&minus;</button>
                    <span className="text-white text-xl">{guestCount} Person</span>
                    <button onClick={increment} className="text-yellow-500 text-3xl">&#43;</button>
                </div>
            </div>
            
            <button
                onClick={handleSave}
                onKeyPress={handleKeyPress}
                className={`w-full bg-[#F6B100] text-[#1a1a1a] rounded-xl py-5 mt-8 text-xl font-bold shadow-lg hover:bg-yellow-700 transition ${!name ? 'opacity-60 cursor-not-allowed' : ''}`}
                disabled={!name}
            >
                {isEditing ? "Save Changes" : "Create Table"}
            </button>
        </div>
    </Modal>
    );
};

TableModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onSave: PropTypes.func.isRequired,
    table: PropTypes.object,
};

export default TableModal; 