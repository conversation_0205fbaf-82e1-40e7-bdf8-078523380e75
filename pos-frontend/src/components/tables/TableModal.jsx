import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Modal from "../shared/Modal";

const TableModal = ({ isOpen, onClose, onSave, table }) => {
    const [name, setName] = useState("");
    const [phone, setPhone] = useState("");
    const [guestCount, setGuestCount] = useState(0);

    const isEditing = !!table;

    useEffect(() => {
        if (isOpen) {
            if (isEditing) {
                setName(table.name || "");
                setPhone(table.phone || "");
                setGuestCount(table.guests || table.seats || 0);
            } else {
                setName("");
                setPhone("");
                setGuestCount(0);
            }
        }
    }, [isOpen, isEditing, table]);

    const increment = () => {
        if (guestCount >= 6) return;
        setGuestCount((prev) => prev + 1);
    };
    const decrement = () => {
        if (guestCount <= 0) return;
        setGuestCount((prev) => prev - 1);
    };

    const handleSave = () => {
        if (name.trim()) {
            onSave({ name: name.trim(), phone, guests: guestCount });
            onClose();
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            handleSave();
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} title={isEditing ? "Edit Table" : "Create Table"}>
            <div className="space-y-6 p-4">
                <div>
                    <label className="block text-[#ababab] mb-2 text-lg font-bold">Name <span className="text-red-500">*</span></label>
                    <div className="flex items-center rounded-xl p-5 px-6 bg-[#1f1f1f]">
                        <input
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            onKeyPress={handleKeyPress}
                            type="text"
                            placeholder="Table name"
                            className="bg-transparent flex-1 text-white focus:outline-none text-xl placeholder-gray-400"
                            required
                            autoFocus
                        />
                    </div>
                </div>
                <div>
                    <label className="block text-[#ababab] mb-2 mt-3 text-lg font-bold">Phone</label>
                    <div className="flex items-center rounded-xl p-5 px-6 bg-[#1f1f1f]">
                        <input value={phone} onChange={(e) => setPhone(e.target.value)} type="number" placeholder="optional" className="bg-transparent flex-1 text-white focus:outline-none text-xl placeholder-gray-400" />
                    </div>
                </div>
                <div>
                    <label className="block mb-2 mt-3 text-lg font-bold text-[#ababab]">Guest</label>
                    <div className="flex items-center justify-between bg-[#1f1f1f] px-6 py-5 rounded-xl">
                        <button onClick={decrement} className="text-yellow-500 text-3xl">&minus;</button>
                        <span className="text-white text-xl">{guestCount} Person</span>
                        <button onClick={increment} className="text-yellow-500 text-3xl">&#43;</button>
                    </div>
                </div>
                <button
                    onClick={handleSave}
                    className={`w-full bg-[#F6B100] text-[#1a1a1a] rounded-xl py-5 mt-8 text-xl font-bold shadow-lg hover:bg-yellow-700 transition ${!name ? 'opacity-60 cursor-not-allowed' : ''}`}
                    disabled={!name}
                >
                    {isEditing ? "Save Changes" : "Create Table"}
                </button>
            </div>
        </Modal>
    );
};

TableModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onSave: PropTypes.func.isRequired,
    table: PropTypes.object,
};

export default TableModal; 