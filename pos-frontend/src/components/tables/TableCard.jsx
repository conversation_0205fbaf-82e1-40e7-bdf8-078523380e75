import React from "react";


const TableCard = ({ table }) => {


  return (
    <div

      className="bg-[#262626] p-3 rounded-lg cursor-pointer flex flex-col justify-between transition-colors duration-200 hover:bg-[#2c2c2c]"
    >
      <div className="flex items-center justify-between">
        <h1 className="text-[#f5f5f5] text-xl font-semibold">{table.name}</h1>
        <p
          className={`${table.status === "Available"
            ? "text-green-400 bg-green-500/20"
            : "bg-yellow-500/20 text-yellow-400"
            } px-3 py-1 rounded text-sm font-medium`}
        >
          {table.status}
        </p>
      </div>
      <div className="flex items-center justify-center">
        <div className="text-white rounded-lg px-3 py-2 text-base bg-[#383838] font-mono">
          {table.code}
        </div>
      </div>
      <p className="text-[#ababab] text-sm">
        Seats: <span className="text-[#f5f5f5] font-medium">{table.seats}</span>
      </p>
    </div>
  );
};

export default TableCard;


