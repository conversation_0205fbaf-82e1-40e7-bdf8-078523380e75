import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { IoClose, IoAdd, IoWarning, IoCalendar, IoSearch, IoChevronDown } from 'react-icons/io5';
import { axiosWrapper } from '../../https/axiosWrapper';

const AddInventoryItemModal = ({ onClose }) => {
  const [formData, setFormData] = useState({
    articleId: '',
    minimumStock: 0,
    maximumStock: 0,
    reorderPoint: 0,
    averageUnitCost: 0,
    trackExpiry: false,
    defaultShelfLifeDays: '',
    isActive: true
  });
  const [errors, setErrors] = useState({});
  const [articleSearch, setArticleSearch] = useState('');
  const [showArticleDropdown, setShowArticleDropdown] = useState(false);

  const dropdownRef = useRef(null);
  const queryClient = useQueryClient();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowArticleDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch articles for selection
  const { data: articlesResponse, isLoading: articlesLoading } = useQuery({
    queryKey: ['articles'],
    queryFn: () => axiosWrapper.get('/api/products/articles/?paginate=false'),
    staleTime: 300000, // 5 minutes
  });

  // Extract articles array from response (following the same pattern as MenuContainer.jsx)
  const articles = React.useMemo(() => {
    if (!articlesResponse) return [];

    // The standard pattern used in the codebase: response.data.data
    return articlesResponse.data?.data || [];
  }, [articlesResponse]);

  // Filter articles based on search term
  const filteredArticles = useMemo(() => {
    if (!articleSearch.trim()) return articles;

    const searchTerm = articleSearch.toLowerCase();
    return articles.filter(article =>
      article.name?.toLowerCase().includes(searchTerm) ||
      article.code?.toLowerCase().includes(searchTerm) ||
      article.description?.toLowerCase().includes(searchTerm)
    );
  }, [articles, articleSearch]);

  const createInventoryItemMutation = useMutation({
    mutationFn: (data) => axiosWrapper.post('/api/inventory/items/', data),
    onSuccess: () => {
      queryClient.invalidateQueries(['inventory-items']);
      queryClient.invalidateQueries(['inventory-dashboard']);
      onClose();
    },
    onError: (error) => {
      const errorData = error.response?.data;

      // Handle duplicate article error (409 Conflict)
      if (error.response?.status === 409 && errorData?.message) {
        setErrors({
          general: errorData.message,
          existing_item: errorData.existing_item
        });
      } else {
        setErrors(errorData || { general: 'Failed to create inventory item' });
      }
    }
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({});

    // Validation
    const newErrors = {};
    if (!formData.articleId) {
      newErrors.articleId = 'Please select an article';
    }
    if (formData.minimumStock < 0) {
      newErrors.minimumStock = 'Minimum stock cannot be negative';
    }
    if (formData.maximumStock < 0) {
      newErrors.maximumStock = 'Maximum stock cannot be negative';
    }
    if (formData.reorderPoint < 0) {
      newErrors.reorderPoint = 'Reorder point cannot be negative';
    }
    if (formData.averageUnitCost < 0) {
      newErrors.averageUnitCost = 'Unit cost cannot be negative';
    }
    if (formData.trackExpiry && (!formData.defaultShelfLifeDays || formData.defaultShelfLifeDays <= 0)) {
      newErrors.defaultShelfLifeDays = 'Shelf life days must be greater than 0 when expiry tracking is enabled';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Prepare data for submission
    const submitData = {
      ...formData,
      minimumStock: parseFloat(formData.minimumStock),
      maximumStock: parseFloat(formData.maximumStock),
      reorderPoint: parseFloat(formData.reorderPoint),
      averageUnitCost: parseFloat(formData.averageUnitCost),
      defaultShelfLifeDays: formData.trackExpiry ? parseInt(formData.defaultShelfLifeDays) : null
    };

    createInventoryItemMutation.mutate(submitData);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const handleArticleSelect = (article) => {
    setFormData(prev => ({
      ...prev,
      articleId: article.id
    }));
    setArticleSearch(article.name);
    setShowArticleDropdown(false);

    // Clear error
    if (errors.articleId) {
      setErrors(prev => ({
        ...prev,
        articleId: undefined
      }));
    }
  };

  const handleSearchChange = (value) => {
    setArticleSearch(value);
    setShowArticleDropdown(true);

    // If search is cleared, clear selection
    if (!value.trim()) {
      setFormData(prev => ({
        ...prev,
        articleId: ''
      }));
    }
  };

  const selectedArticle = Array.isArray(articles) ? articles.find(article => article.id === formData.articleId) : null;

  // Update search when selectedArticle changes (for initial load or external changes)
  useEffect(() => {
    if (selectedArticle && !articleSearch) {
      setArticleSearch(selectedArticle.name);
    }
  }, [selectedArticle, articleSearch]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-gray-700 transition-colors">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <IoAdd className="text-2xl text-blue-500" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Add Inventory Item
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
          >
            <IoClose className="text-2xl" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Article Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Article *
            </label>
            <div className="relative" ref={dropdownRef}>
              <div className="relative">
                <input
                  type="text"
                  value={articleSearch}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  onFocus={() => setShowArticleDropdown(true)}
                  placeholder={articlesLoading ? "Loading articles..." : "Search articles by name, code, or description..."}
                  className="w-full px-3 py-2 pr-10 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                  disabled={articlesLoading}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <IoSearch className="text-[#ababab]" />
                </div>
              </div>

              {showArticleDropdown && !articlesLoading && (
                <div className="absolute z-10 w-full mt-1 bg-[#1a1a1a] border border-[#404040] rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {filteredArticles.length > 0 ? (
                    filteredArticles.map((article) => (
                      <div
                        key={article.id}
                        onClick={() => handleArticleSelect(article)}
                        className="px-3 py-2 hover:bg-[#404040] cursor-pointer border-b border-[#404040] last:border-b-0"
                      >
                        <div className="text-[#f5f5f5] font-medium">{article.name}</div>
                        <div className="text-xs text-[#ababab]">
                          Code: {article.code} | {article.subFamily?.family?.name || article.sub_family?.family?.name} - {article.subFamily?.name || article.sub_family?.name}
                        </div>
                        <div className="text-xs text-[#ababab]">Price: ${article.price}</div>
                      </div>
                    ))
                  ) : (
                    <div className="px-3 py-2 text-gray-600 dark:text-gray-400 text-center">
                      No articles found matching "{articleSearch}"
                    </div>
                  )}
                </div>
              )}
            </div>
            {errors.articleId && (
              <p className="text-red-500 text-sm mt-1">{errors.articleId}</p>
            )}
            
            {selectedArticle && (
              <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600 transition-colors">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <strong>Selected:</strong> {selectedArticle.name}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <strong>Category:</strong> {selectedArticle.subFamily?.family?.name || selectedArticle.sub_family?.family?.name} - {selectedArticle.subFamily?.name || selectedArticle.sub_family?.name}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <strong>Price:</strong> ${selectedArticle.price}
                </p>
              </div>
            )}
          </div>

          {/* Stock Levels */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Minimum Stock
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.minimumStock}
                onChange={(e) => handleInputChange('minimumStock', e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                placeholder="0"
              />
              {errors.minimumStock && (
                <p className="text-red-500 text-sm mt-1">{errors.minimumStock}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Maximum Stock
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.maximumStock}
                onChange={(e) => handleInputChange('maximumStock', e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                placeholder="0"
              />
              {errors.maximumStock && (
                <p className="text-red-500 text-sm mt-1">{errors.maximumStock}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Reorder Point
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.reorderPoint}
                onChange={(e) => handleInputChange('reorderPoint', e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                placeholder="0"
              />
              {errors.reorderPoint && (
                <p className="text-red-500 text-sm mt-1">{errors.reorderPoint}</p>
              )}
            </div>
          </div>

          {/* Unit Cost */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Average Unit Cost
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.averageUnitCost}
              onChange={(e) => handleInputChange('averageUnitCost', e.target.value)}
              className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
              placeholder="0.00"
            />
            {errors.averageUnitCost && (
              <p className="text-red-500 text-sm mt-1">{errors.averageUnitCost}</p>
            )}
          </div>

          {/* Expiry Tracking */}
          <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 transition-colors">
            <div className="flex items-center gap-3 mb-3">
              <IoCalendar className="text-xl text-blue-500" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Expiry Tracking</h3>
            </div>

            <div className="flex items-center gap-3 mb-4">
              <input
                type="checkbox"
                id="trackExpiry"
                checked={formData.trackExpiry}
                onChange={(e) => handleInputChange('trackExpiry', e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
              />
              <label htmlFor="trackExpiry" className="text-sm text-gray-900 dark:text-gray-100">
                Enable expiry date tracking for this item
              </label>
            </div>

            {formData.trackExpiry && (
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Default Shelf Life (Days)
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.defaultShelfLifeDays}
                  onChange={(e) => handleInputChange('defaultShelfLifeDays', e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                  placeholder="Enter shelf life in days"
                />
                {errors.defaultShelfLifeDays && (
                  <p className="text-red-500 text-sm mt-1">{errors.defaultShelfLifeDays}</p>
                )}
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  This will be used to calculate expiry dates for new stock batches
                </p>
              </div>
            )}
          </div>

          {/* Active Status */}
          <div className="mb-6">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => handleInputChange('isActive', e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500"
              />
              <label htmlFor="isActive" className="text-sm text-gray-900 dark:text-gray-100">
                Active (item will be tracked in inventory)
              </label>
            </div>
          </div>

          {/* Error Message */}
          {errors.general && (
            <div className="mb-6 p-3 bg-red-500/10 border border-red-500 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <IoWarning className="text-red-500" />
                <p className="text-red-500 text-sm font-medium">{errors.general}</p>
              </div>
              {errors.existing_item && (
                <div className="text-sm text-gray-600 mt-2 p-2 bg-gray-50 rounded">
                  <p><strong>Existing Item Details:</strong></p>
                  <p>Current Stock: {errors.existing_item.currentStock || 0}</p>
                  <p>Minimum Stock: {errors.existing_item.minimumStock || 0}</p>
                  <p>Average Cost: ${errors.existing_item.averageUnitCost || 0}</p>
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-900 dark:text-gray-100 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={createInventoryItemMutation.isPending}
              className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white rounded-lg transition-colors"
            >
              {createInventoryItemMutation.isPending ? 'Creating...' : 'Create Item'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddInventoryItemModal;
