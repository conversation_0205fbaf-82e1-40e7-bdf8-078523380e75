import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { IoClose, IoWarning, IoCheckmarkCircle, IoTrendingUp, IoTrendingDown, IoSettings } from 'react-icons/io5';
import { axiosWrapper } from '../../https/axiosWrapper';

const StockAdjustmentModal = ({ item, onClose }) => {
  const [adjustmentType, setAdjustmentType] = useState('increase');
  const [quantity, setQuantity] = useState('');
  const [reason, setReason] = useState('');
  const [unitCost, setUnitCost] = useState(item.average_unit_cost || 0);
  const [errors, setErrors] = useState({});

  const queryClient = useQueryClient();

  const adjustStockMutation = useMutation({
    mutationFn: (data) => axiosWrapper.post(`/api/inventory/items/${item.id}/adjust_stock/`, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['inventory-items']);
      queryClient.invalidateQueries(['inventory-dashboard']);
      queryClient.invalidateQueries(['inventory-alerts']);
      onClose();
    },
    onError: (error) => {
      setErrors(error.response?.data || { general: 'Failed to adjust stock' });
    }
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({});

    // Validation
    const newErrors = {};
    if (!quantity || parseFloat(quantity) <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }
    if (!reason.trim()) {
      newErrors.reason = 'Reason is required';
    }
    if (adjustmentType === 'increase' && (!unitCost || parseFloat(unitCost) <= 0)) {
      newErrors.unit_cost = 'Unit cost must be greater than 0 for stock increases';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const adjustmentData = {
      adjustment_type: adjustmentType,
      quantity: parseFloat(quantity),
      reason: reason.trim(),
    };

    if (adjustmentType === 'increase') {
      adjustmentData.unit_cost = parseFloat(unitCost);
    }

    adjustStockMutation.mutate(adjustmentData);
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const getAdjustmentIcon = () => {
    switch (adjustmentType) {
      case 'increase':
        return <IoTrendingUp className="text-green-500" />;
      case 'decrease':
        return <IoTrendingDown className="text-red-500" />;
      case 'set':
        return <IoSettings className="text-blue-500" />;
      default:
        return <IoSettings className="text-gray-500" />;
    }
  };

  const getNewStockLevel = () => {
    const qty = parseFloat(quantity) || 0;
    const currentStock = item.current_stock || 0;
    
    switch (adjustmentType) {
      case 'increase':
        return currentStock + qty;
      case 'decrease':
        return Math.max(0, currentStock - qty);
      case 'set':
        return qty;
      default:
        return currentStock;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-gray-700 transition-colors">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            {getAdjustmentIcon()}
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Adjust Stock
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
          >
            <IoClose className="text-2xl" />
          </button>
        </div>

        {/* Item Info */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
            {item.article?.name || 'Unknown Item'}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {item.article?.sub_family?.family?.name} - {item.article?.sub_family?.name}
          </p>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Current Stock:</span>
              <p className="font-medium text-gray-900 dark:text-gray-100">{item.current_stock}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Reorder Point:</span>
              <p className="font-medium text-gray-900 dark:text-gray-100">{item.reorder_point}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Avg Cost:</span>
              <p className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(item.average_unit_cost)}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Stock Value:</span>
              <p className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(item.stock_value)}</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Adjustment Type */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Adjustment Type
            </label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: 'increase', label: 'Increase', icon: IoTrendingUp, color: 'text-green-500' },
                { value: 'decrease', label: 'Decrease', icon: IoTrendingDown, color: 'text-red-500' },
                { value: 'set', label: 'Set To', icon: IoSettings, color: 'text-blue-500' }
              ].map((type) => {
                const TypeIcon = type.icon;
                return (
                  <button
                    key={type.value}
                    type="button"
                    onClick={() => setAdjustmentType(type.value)}
                    className={`flex flex-col items-center gap-2 p-3 rounded-lg border transition-colors ${
                      adjustmentType === type.value
                        ? 'border-blue-500 bg-blue-500/10 text-blue-500'
                        : 'border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500'
                    }`}
                  >
                    <TypeIcon className={`text-xl ${adjustmentType === type.value ? 'text-blue-500' : type.color}`} />
                    <span className="text-xs">{type.label}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Quantity */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Quantity
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
              placeholder="Enter quantity"
            />
            {errors.quantity && (
              <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>
            )}
          </div>

          {/* Unit Cost (only for increase) */}
          {adjustmentType === 'increase' && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Unit Cost
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={unitCost}
                onChange={(e) => setUnitCost(e.target.value)}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                placeholder="Enter unit cost"
              />
              {errors.unit_cost && (
                <p className="text-red-500 text-sm mt-1">{errors.unit_cost}</p>
              )}
            </div>
          )}

          {/* Reason */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Reason
            </label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 resize-none transition-colors"
              placeholder="Enter reason for adjustment"
            />
            {errors.reason && (
              <p className="text-red-500 text-sm mt-1">{errors.reason}</p>
            )}
          </div>

          {/* Preview */}
          {quantity && (
            <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 transition-colors">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-2">
                <IoCheckmarkCircle className="text-green-500" />
                Adjustment Preview
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Current Stock:</span>
                  <p className="font-medium text-gray-900 dark:text-gray-100">{item.current_stock}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">New Stock:</span>
                  <p className="font-medium text-gray-900 dark:text-gray-100">{getNewStockLevel()}</p>
                </div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {errors.general && (
            <div className="mb-4 p-3 bg-red-500/10 border border-red-500 rounded-lg flex items-center gap-2">
              <IoWarning className="text-red-500" />
              <p className="text-red-500 text-sm">{errors.general}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-900 dark:text-gray-100 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={adjustStockMutation.isPending}
              className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white rounded-lg transition-colors"
            >
              {adjustStockMutation.isPending ? 'Adjusting...' : 'Adjust Stock'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StockAdjustmentModal;
