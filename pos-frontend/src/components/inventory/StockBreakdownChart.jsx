import React, { useState } from 'react';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend
} from 'recharts';
import {
  Io<PERSON>arning,
  Io<PERSON><PERSON>ding<PERSON>p,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>o<PERSON><PERSON>
} from 'react-icons/io5';

const StockBreakdownChart = ({ items, movements, analytics, loading, type = 'breakdown' }) => {
  const [chartType, setChartType] = useState('bar');
  const [groupBy, setGroupBy] = useState('family');

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-[#ababab] mt-4">Loading chart data...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'];

  // Process data based on type
  const processData = () => {
    if (type === 'breakdown' && items) {


      // Ensure items is an array (handle API response structure)
      // items should now be the data portion: {count: X, results: [...]}
      const itemsArray = items?.results || [];



      // Group items by family or category
      const grouped = {};

      itemsArray.forEach(item => {
        const key = groupBy === 'family'
          ? item.article?.subFamily?.family?.name || 'Unknown Family'
          : item.article?.subFamily?.name || 'Unknown Category';

        if (!grouped[key]) {
          grouped[key] = {
            name: key,
            totalItems: 0,
            totalValue: 0,
            lowStockItems: 0,
            outOfStockItems: 0,
            currentStock: 0
          };
        }

        grouped[key].totalItems += 1;
        grouped[key].totalValue += parseFloat(item.stockValue || item.stock_value || 0);
        grouped[key].currentStock += parseFloat(item.currentStock || item.current_stock || 0);

        const currentStock = item.currentStock || item.current_stock || 0;
        const isLowStock = item.isLowStock || item.is_low_stock || false;

        if (currentStock === 0) {
          grouped[key].outOfStockItems += 1;
        } else if (isLowStock) {
          grouped[key].lowStockItems += 1;
        }
      });
      
      return Object.values(grouped).sort((a, b) => b.totalValue - a.totalValue);
      
    } else if (type === 'movements' && movements) {
      // Ensure movements is an array (handle API response structure)
    
      const movementsArray = Array.isArray(movements)
        ? movements
        : movements?.data?.data || movements?.data || movements?.results || [];

      // Process movements data for trending
      const dailyMovements = {};

      movementsArray.forEach(movement => {
        const date = new Date(movement.createdAt || movement.created_at).toLocaleDateString();
        const quantity = parseFloat(movement.quantity);
        const value = parseFloat(movement.totalValue || movement.total_value);
        
        if (!dailyMovements[date]) {
          dailyMovements[date] = {
            date,
            inbound: 0,
            outbound: 0,
            inboundValue: 0,
            outboundValue: 0,
            net: 0
          };
        }
        
        if (quantity > 0) {
          dailyMovements[date].inbound += quantity;
          dailyMovements[date].inboundValue += value;
        } else {
          dailyMovements[date].outbound += Math.abs(quantity);
          dailyMovements[date].outboundValue += Math.abs(value);
        }
        
        dailyMovements[date].net += quantity;
      });
      
      return Object.values(dailyMovements)
        .sort((a, b) => new Date(a.date) - new Date(b.date))
        .slice(-14); // Last 14 days
    } else if (type === 'analytics' && analytics) {
     
      // Process analytics data for charts
      if (chartType === 'bar') {
        // Movement type breakdown
        return analytics.data.movementTypeStats?.map(stat => ({
          name: stat.label,
          count: stat.count,
          quantity: Math.abs(stat.totalQuantity),
          value: stat.totalValue
        })) || [];
      } else if (chartType === 'line') {
        // Daily trends
        console.log('line dailyTrends',analytics.data.dailyTrends)
        return analytics.data.dailyTrends?.map(trend => ({
          date: new Date(trend.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          total: trend.totalMovements,
          inbound: trend.inboundMovements,
          outbound: trend.outboundMovements
        })) || [];
      } else if (chartType === 'pie') {
        // Movement type distribution
        console.log('movementTypeStats',analytics.data.movementTypeStats)
       
        return analytics.data.movementTypeStats?.filter(stat => stat.count > 0).map(stat => ({
          name: stat.label,
          value: stat.count,
          totalValue: stat.totalValue
        })) || [];
      }
    }

    return [];
  };

  const chartData = processData();


  if (!chartData || chartData.length === 0) {
    return (
      <div className="p-6 text-center py-12">
        <IoWarning className="text-6xl text-yellow-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-[#f5f5f5] mb-2">
          No Data Available 
        </h3>
        <p className="text-[#ababab]">
          No data available for the selected view.
        </p>
      </div>
    );
  }

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height={400}>
      <BarChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" stroke="#444" />
        <XAxis 
          dataKey="name" 
          stroke="#ababab"
          fontSize={12}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis 
          stroke="#ababab"
          fontSize={12}
          tickFormatter={type === 'breakdown' ? formatCurrency : undefined}
        />
        <Tooltip 
          contentStyle={{
            backgroundColor: '#1a1a1a',
            border: '1px solid #444',
            borderRadius: '8px',
            color: '#f5f5f5'
          }}
          formatter={(value, name) => [
            type === 'breakdown' && name.includes('Value') ? formatCurrency(value) : value,
            name
          ]}
        />
        <Legend />
        {type === 'breakdown' ? (
          <>
            <Bar dataKey="totalValue" fill="#0088FE" name="Stock Value" />
            <Bar dataKey="totalItems" fill="#00C49F" name="Total Items" />
          </>
        ) : type === 'analytics' ? (
          <>
            <Bar dataKey="count" fill="#0088FE" name="Movement Count" />
            <Bar dataKey="value" fill="#00C49F" name="Total Value" />
          </>
        ) : (
          <>
            <Bar dataKey="inbound" fill="#00C49F" name="Stock In" />
            <Bar dataKey="outbound" fill="#FF8042" name="Stock Out" />
          </>
        )}
      </BarChart>
    </ResponsiveContainer>
  );

  const renderPieChart = () => {
    // Determine the correct dataKey based on chart type
    let dataKey;
    if (type === 'breakdown') {
      dataKey = 'totalValue';
    } else if (type === 'analytics') {
      dataKey = 'value'; // For analytics pie chart, use 'value' which contains the count
    } else {
      dataKey = 'inboundValue'; // For movements
    }

    return (
      <ResponsiveContainer width="100%" height={400}>
        <PieChart>
          <Pie
            data={chartData.slice(0, 8)} // Top 8 categories
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            outerRadius={120}
            fill="#8884d8"
            dataKey={dataKey}
          >
            {chartData.slice(0, 8).map((_, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip
            contentStyle={{
              backgroundColor: '#1a1a1a',
              border: '1px solid #444',
              borderRadius: '8px',
              color: '#f5f5f5'
            }}
            formatter={(value) => {
              if (type === 'analytics') {
                return [value, 'Count']; // Show count for analytics
              }
              return [formatCurrency(value), 'Value']; // Show currency for others
            }}
          />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height={400}>
      <LineChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" stroke="#444" />
        <XAxis
          dataKey="date"
          stroke="#ababab"
          fontSize={12}
        />
        <YAxis
          stroke="#ababab"
          fontSize={12}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: '#1a1a1a',
            border: '1px solid #444',
            borderRadius: '8px',
            color: '#f5f5f5'
          }}
        />
        <Legend />
        {type === 'analytics' ? (
          <>
            <Line
              type="monotone"
              dataKey="total"
              stroke="#0088FE"
              strokeWidth={2}
              name="Total Movements"
            />
            <Line
              type="monotone"
              dataKey="inbound"
              stroke="#00C49F"
              strokeWidth={2}
              name="Inbound"
            />
            <Line
              type="monotone"
              dataKey="outbound"
              stroke="#FF8042"
              strokeWidth={2}
              name="Outbound"
            />
          </>
        ) : (
          <>
            <Line
              type="monotone"
              dataKey="inbound"
              stroke="#00C49F"
              strokeWidth={2}
              name="Stock In"
            />
            <Line
              type="monotone"
              dataKey="outbound"
              stroke="#FF8042"
              strokeWidth={2}
              name="Stock Out"
            />
            <Line
              type="monotone"
              dataKey="net"
              stroke="#0088FE"
              strokeWidth={2}
              name="Net Change"
            />
          </>
        )}
      </LineChart>
    </ResponsiveContainer>
  );

  return (
    <div className="p-6">
      {/* Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
        <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 lg:mb-0">
          {type === 'breakdown' ? 'Stock Breakdown Analysis' :
           type === 'analytics' ? 'Stock Movement Analytics' : 'Stock Movement Trends'}
        </h3>
        
        <div className="flex items-center gap-4">
          {type === 'breakdown' && (
            <div className="flex items-center gap-2">
              <label className="text-sm text-[#ababab]">Group by:</label>
              <select
                value={groupBy}
                onChange={(e) => setGroupBy(e.target.value)}
                className="px-3 py-1 bg-[#1a1a1a] border border-[#404040] rounded text-[#f5f5f5] text-sm focus:outline-none focus:border-blue-500"
              >
                <option value="family">Family</option>
                <option value="category">Category</option>
              </select>
            </div>
          )}
          
          <div className="flex items-center gap-1 bg-gray-200 dark:bg-gray-700 rounded-lg p-1 transition-colors">
            <button
              onClick={() => setChartType('bar')}
              className={`p-2 rounded transition-colors ${
                chartType === 'bar' ? 'bg-blue-600 text-white' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              <IoBarChart />
            </button>
            <button
              onClick={() => setChartType('pie')}
              className={`p-2 rounded transition-colors ${
                chartType === 'pie' ? 'bg-blue-600 text-white' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              <IoPieChart />
            </button>
            {(type === 'movements' || type === 'analytics') && (
              <button
                onClick={() => setChartType('line')}
                className={`p-2 rounded transition-colors ${
                  chartType === 'line' ? 'bg-blue-600 text-white' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                <IoTrendingUp />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors">
        {chartType === 'bar' && renderBarChart()}
        {chartType === 'pie' && renderPieChart()}
        {chartType === 'line' && (type === 'movements' || type === 'analytics') && renderLineChart()}
      </div>

      {/* Summary Stats */}
      {type === 'breakdown' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 dark:text-gray-400 text-sm">Total Categories</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{chartData.length}</p>
              </div>
              <IoList className="text-3xl text-blue-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 dark:text-gray-400 text-sm">Total Stock Value</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {formatCurrency(chartData.reduce((sum, item) => sum + item.totalValue, 0))}
                </p>
              </div>
              <IoTrendingUp className="text-3xl text-green-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 dark:text-gray-400 text-sm">Alert Items</p>
                <p className="text-2xl font-bold text-red-500">
                  {chartData.reduce((sum, item) => sum + item.lowStockItems + item.outOfStockItems, 0)}
                </p>
              </div>
              <IoWarning className="text-3xl text-red-500" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockBreakdownChart;
