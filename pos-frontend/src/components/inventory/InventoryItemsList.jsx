import React from 'react';
import { 
  IoWarning, 
  IoAlert, 
  IoCheckmarkCircle, 
  IoTime,
  IoSettings,
  IoEye,
  IoCalendar
} from 'react-icons/io5';

const InventoryItemsList = ({ items, loading, viewMode, onStockAdjustment }) => {
  console.log('items', items);

  if (loading) {
    return (
      <div className="p-6">
        <div className={`grid gap-4 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 animate-pulse border border-gray-200 dark:border-gray-600 transition-colors">
              <div className="w-full h-6 bg-gray-600 rounded mb-2"></div>
              <div className="w-3/4 h-4 bg-gray-600 rounded mb-2"></div>
              <div className="w-1/2 h-4 bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const itemsArray = Array.isArray(items) 
  ? items 
  : items?.data?.data?.results || [];


  if (itemsArray.length === 0) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600 dark:text-gray-400">No inventory items found</p>
      </div>
    );
  }

  const getStockStatus = (item) => {
    const currentStock = item.currentStock || item.current_stock || 0;
    const isLowStock = item.isLowStock || item.is_low_stock || false;

    if (currentStock === 0) {
      return { status: 'out_of_stock', color: 'text-red-500', icon: IoAlert, label: 'Out of Stock' };
    } else if (isLowStock) {
      return { status: 'low_stock', color: 'text-yellow-500', icon: IoWarning, label: 'Low Stock' };
    } else {
      return { status: 'in_stock', color: 'text-green-500', icon: IoCheckmarkCircle, label: 'In Stock' };
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
      {itemsArray.map((item) => {
        const stockStatus = getStockStatus(item);
        const StatusIcon = stockStatus.icon;
        
        return (
          <div key={item.id} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-colors">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                  {item.article?.name || 'Unknown Item'}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {item.article?.subFamily?.family?.name || item.article?.sub_family?.family?.name} - {item.article?.subFamily?.name || item.article?.sub_family?.name}
                </p>
              </div>
              <div className={`flex items-center gap-1 ${stockStatus.color}`}>
                <StatusIcon className="text-lg" />
              </div>
            </div>

            <div className="space-y-2 mb-4">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400 text-sm">Current Stock:</span>
                <span className="text-gray-900 dark:text-gray-100 font-medium">{item.currentStock || item.current_stock}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400 text-sm">Reorder Point:</span>
                <span className="text-gray-900 dark:text-gray-100">{item.reorderPoint || item.reorder_point}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400 text-sm">Avg Cost:</span>
                <span className="text-gray-900 dark:text-gray-100">{formatCurrency(item.averageUnitCost || item.average_unit_cost)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400 text-sm">Stock Value:</span>
                <span className="text-gray-900 dark:text-gray-100 font-medium">{formatCurrency(item.stockValue || item.stock_value)}</span>
              </div>
            </div>

            {/* Expiry Information */}
            {(item.trackExpiry || item.track_expiry) && (
              <div className="mb-4 p-2 bg-gray-200 dark:bg-gray-600 rounded transition-colors">
                <div className="flex items-center gap-2 mb-1">
                  <IoCalendar className="text-sm text-blue-500" />
                  <span className="text-xs text-gray-600 dark:text-gray-400">Expiry Tracking</span>
                </div>
                {(item.expiringSoonStock || item.expiring_soon_stock || 0) > 0 && (
                  <p className="text-xs text-yellow-500">
                    {item.expiringSoonStock || item.expiring_soon_stock} units expiring soon
                  </p>
                )}
                {(item.expiredStock || item.expired_stock || 0) > 0 && (
                  <p className="text-xs text-red-500">
                    {item.expiredStock || item.expired_stock} units expired
                  </p>
                )}
              </div>
            )}

            <div className="flex items-center gap-2">
              <button
                onClick={() => onStockAdjustment(item)}
                className="flex-1 flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded text-sm transition-colors"
              >
                <IoSettings className="text-sm" />
                Adjust Stock
              </button>
              <button className="flex items-center justify-center gap-2 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 px-3 py-2 rounded text-sm transition-colors text-gray-900 dark:text-gray-100">
                <IoEye className="text-sm" />
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );

  const ListView = () => (
    <div className="p-6">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-300 dark:border-gray-600">
              <th className="text-left py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Item</th>
              <th className="text-left py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Category</th>
              <th className="text-right py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Current Stock</th>
              <th className="text-right py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Reorder Point</th>
              <th className="text-right py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Avg Cost</th>
              <th className="text-right py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Stock Value</th>
              <th className="text-center py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Status</th>
              <th className="text-center py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {itemsArray.map((item) => {
              const stockStatus = getStockStatus(item);
              const StatusIcon = stockStatus.icon;
              
              return (
                <tr key={item.id} className="border-b border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="py-3 px-4">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">{item.article?.name || 'Unknown Item'}</p>
                      {(item.trackExpiry || item.track_expiry) && (
                        <div className="flex items-center gap-2 mt-1">
                          <IoCalendar className="text-xs text-blue-500" />
                          <span className="text-xs text-gray-600 dark:text-gray-400">Expiry tracked</span>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                    {item.article?.subFamily?.family?.name || item.article?.sub_family?.family?.name} - {item.article?.subFamily?.name || item.article?.sub_family?.name}
                  </td>
                  <td className="py-3 px-4 text-right text-gray-900 dark:text-gray-100 font-medium">
                    {item.currentStock || item.current_stock}
                  </td>
                  <td className="py-3 px-4 text-right text-gray-600 dark:text-gray-400">
                    {item.reorderPoint || item.reorder_point}
                  </td>
                  <td className="py-3 px-4 text-right text-gray-600 dark:text-gray-400">
                    {formatCurrency(item.averageUnitCost || item.average_unit_cost)}
                  </td>
                  <td className="py-3 px-4 text-right text-gray-900 dark:text-gray-100 font-medium">
                    {formatCurrency(item.stockValue || item.stock_value)}
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className={`flex items-center justify-center gap-1 ${stockStatus.color}`}>
                      <StatusIcon className="text-sm" />
                      <span className="text-xs">{stockStatus.label}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center gap-2">
                      <button
                        onClick={() => onStockAdjustment(item)}
                        className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs transition-colors"
                      >
                        <IoSettings className="text-xs" />
                        Adjust
                      </button>
                      <button className="flex items-center gap-1 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 px-2 py-1 rounded text-xs transition-colors text-gray-900 dark:text-gray-100">
                        <IoEye className="text-xs" />
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );

  return viewMode === 'grid' ? <GridView /> : <ListView />;
};

export default InventoryItemsList;
