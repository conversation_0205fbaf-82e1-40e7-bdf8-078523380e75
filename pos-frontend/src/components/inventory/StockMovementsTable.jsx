import React, { useState } from 'react';
import { 
  IoTrendingUp, 
  IoTrendingDown, 
  IoSettings, 
  IoReceipt,
  IoWarning,
  IoSkull,
  IoSwapHorizontal,
  IoChevronDown,
  IoChevronUp,
  IoEye
} from 'react-icons/io5';

const StockMovementsTable = ({ movements, loading }) => {
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');

  if (loading) {
    return (
      <div className="p-6">
        <div className="space-y-4">
          {[...Array(10)].map((_, index) => (
            <div key={index} className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 animate-pulse border border-gray-200 dark:border-gray-600 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-gray-600 rounded"></div>
                  <div>
                    <div className="w-48 h-4 bg-gray-600 rounded mb-2"></div>
                    <div className="w-32 h-3 bg-gray-600 rounded"></div>
                  </div>
                </div>
                <div className="w-24 h-4 bg-gray-600 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Handle camelCase API response - movements might be in results array or direct array
  const movementsArray = Array.isArray(movements)
    ? movements
    : movements?.results
    ? movements.results
    : [];

  if (movementsArray.length === 0) {
    return (
      <div className="p-6 text-center">
        <IoWarning className="text-6xl text-yellow-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
          No Stock Movements Found
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          No stock movements match your current filters.
        </p>
      </div>
    );
  }

  const getMovementIcon = (type) => {
    switch (type) {
      case 'Purchase':
        return <IoTrendingUp className="text-green-500" />;
      case 'Sale':
        return <IoTrendingDown className="text-red-500" />;
      case 'Adjustment':
        return <IoSettings className="text-blue-500" />;
      case 'Waste':
        return <IoWarning className="text-yellow-500" />;
      case 'Transfer':
        return <IoSwapHorizontal className="text-purple-500" />;
      case 'Expiry':
        return <IoSkull className="text-red-600" />;
      default:
        return <IoReceipt className="text-gray-500" />;
    }
  };

  const getMovementColor = (type, quantity) => {
    if (quantity > 0) return 'text-green-500';
    if (quantity < 0) return 'text-red-500';
    return 'text-gray-500';
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatQuantity = (quantity) => {
    const num = parseFloat(quantity);
    return num >= 0 ? `+${num}` : `${num}`;
  };

  const toggleRowExpansion = (movementId) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(movementId)) {
      newExpanded.delete(movementId);
    } else {
      newExpanded.add(movementId);
    }
    setExpandedRows(newExpanded);
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const sortedMovements = [...movementsArray].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];

    if (sortField === 'created_at' || sortField === 'createdAt') {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    } else if (sortField === 'quantity' || sortField === 'unit_cost' || sortField === 'unitCost' || sortField === 'total_value' || sortField === 'totalValue') {
      aValue = parseFloat(aValue) || 0;
      bValue = parseFloat(bValue) || 0;
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return (
    <div className="p-6">
      {/* Desktop Table View */}
      <div className="hidden lg:block overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-300 dark:border-gray-600">
              <th className="text-left py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Type</th>
              <th
                className="text-left py-3 px-4 text-gray-600 dark:text-gray-400 font-medium cursor-pointer hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                onClick={() => handleSort('inventoryItem')}
              >
                Item
              </th>
              <th
                className="text-right py-3 px-4 text-gray-600 dark:text-gray-400 font-medium cursor-pointer hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                onClick={() => handleSort('quantity')}
              >
                Quantity
              </th>
              <th
                className="text-right py-3 px-4 text-gray-600 dark:text-gray-400 font-medium cursor-pointer hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                onClick={() => handleSort('unitCost')}
              >
                Unit Cost
              </th>
              <th
                className="text-right py-3 px-4 text-gray-600 dark:text-gray-400 font-medium cursor-pointer hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                onClick={() => handleSort('totalValue')}
              >
                Total Value
              </th>
              <th
                className="text-left py-3 px-4 text-gray-600 dark:text-gray-400 font-medium cursor-pointer hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                onClick={() => handleSort('createdAt')}
              >
                Date
              </th>
              <th className="text-center py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedMovements.map((movement) => (
              <React.Fragment key={movement.id}>
                <tr className="border-b border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      {getMovementIcon(movement.movementType || movement.movement_type)}
                      <span className="text-sm text-gray-900 dark:text-gray-100">{movement.movementType || movement.movement_type}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {movement.inventoryItem?.article?.name || movement.inventory_item?.article?.name || 'Unknown Item'}
                      </p>
                      {(movement.batch?.batchNumber || movement.batch?.batch_number) && (
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          Batch: {movement.batch.batchNumber || movement.batch.batch_number}
                        </p>
                      )}
                    </div>
                  </td>
                  <td className={`py-3 px-4 text-right font-medium ${getMovementColor(movement.movementType || movement.movement_type, movement.quantity)}`}>
                    {formatQuantity(movement.quantity)}
                  </td>
                  <td className="py-3 px-4 text-right text-gray-600 dark:text-gray-400">
                    {formatCurrency(movement.unitCost || movement.unit_cost)}
                  </td>
                  <td className="py-3 px-4 text-right text-gray-900 dark:text-gray-100 font-medium">
                    {formatCurrency(movement.totalValue || movement.total_value)}
                  </td>
                  <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                    {formatDate(movement.createdAt || movement.created_at)}
                  </td>
                  <td className="py-3 px-4 text-center">
                    <button
                      onClick={() => toggleRowExpansion(movement.id)}
                      className="flex items-center gap-1 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 px-2 py-1 rounded text-xs transition-colors mx-auto text-gray-900 dark:text-gray-100"
                    >
                      <IoEye className="text-xs" />
                      {expandedRows.has(movement.id) ? <IoChevronUp /> : <IoChevronDown />}
                    </button>
                  </td>
                </tr>
                
                {/* Expanded Row Details */}
                {expandedRows.has(movement.id) && (
                  <tr className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 transition-colors">
                    <td colSpan="7" className="py-4 px-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-[#ababab]">Created By:</span>
                          <p className="text-[#f5f5f5]">
                            {(movement.createdBy?.firstName || movement.created_by?.first_name)} {(movement.createdBy?.lastName || movement.created_by?.last_name)}
                          </p>
                        </div>
                        {(movement.referenceNumber || movement.reference_number) && (
                          <div>
                            <span className="text-[#ababab]">Reference:</span>
                            <p className="text-[#f5f5f5]">{movement.referenceNumber || movement.reference_number}</p>
                          </div>
                        )}
                        {(movement.orderReference || movement.order_reference) && (
                          <div>
                            <span className="text-[#ababab]">Order:</span>
                            <p className="text-[#f5f5f5]">{movement.orderReference || movement.order_reference}</p>
                          </div>
                        )}
                        {movement.batch && (
                          <div>
                            <span className="text-[#ababab]">Batch Info:</span>
                            <p className="text-[#f5f5f5]">
                              {movement.batch.batchNumber || movement.batch.batch_number}
                              {(movement.batch.expiryDate || movement.batch.expiry_date) && (
                                <span className="text-xs text-[#ababab] ml-2">
                                  Exp: {new Date(movement.batch.expiryDate || movement.batch.expiry_date).toLocaleDateString()}
                                </span>
                              )}
                            </p>
                          </div>
                        )}
                        {movement.notes && (
                          <div className="md:col-span-2 lg:col-span-3">
                            <span className="text-[#ababab]">Notes:</span>
                            <p className="text-[#f5f5f5]">{movement.notes}</p>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden space-y-4">
        {sortedMovements.map((movement) => (
          <div key={movement.id} className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 transition-colors">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                {getMovementIcon(movement.movementType || movement.movement_type)}
                <div>
                  <h3 className="font-medium text-[#f5f5f5]">
                    {movement.inventoryItem?.article?.name || movement.inventory_item?.article?.name || 'Unknown Item'}
                  </h3>
                  <p className="text-sm text-[#ababab]">{movement.movementType || movement.movement_type}</p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-medium ${getMovementColor(movement.movementType || movement.movement_type, movement.quantity)}`}>
                  {formatQuantity(movement.quantity)}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">{formatCurrency(movement.totalValue || movement.total_value)}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2 text-sm mb-3">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Unit Cost:</span>
                <p className="text-gray-900 dark:text-gray-100">{formatCurrency(movement.unitCost || movement.unit_cost)}</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Date:</span>
                <p className="text-gray-900 dark:text-gray-100">{formatDate(movement.createdAt || movement.created_at)}</p>
              </div>
            </div>

            {movement.notes && (
              <div className="text-sm">
                <span className="text-[#ababab]">Notes:</span>
                <p className="text-[#f5f5f5]">{movement.notes}</p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StockMovementsTable;
