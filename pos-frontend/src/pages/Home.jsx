import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import Greetings from "../components/home/<USER>";
import { BsCashCoin } from "react-icons/bs";
import { GrInProgress } from "react-icons/gr";
import { IoStatsChart } from "react-icons/io5";
import MiniCard from "../components/home/<USER>";
import RecentOrders from "../components/home/<USER>";
import PopularDishes from "../components/home/<USER>";
import { initializeCart } from "../redux/slices/cartSlice";
import { initializeTableFromOrder } from "../redux/slices/tableSlice";

const Home = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const orderData = location.state;

  useEffect(() => {
    document.title = "POS | Home";

    // Initialize with order data if available
    if (orderData) {
      dispatch(initializeTableFromOrder({
        orderId: orderData.orderId,
        table: orderData.table
      }));

      if (orderData.orderItems) {
        dispatch(initializeCart(orderData));
      }
    }
  }, [orderData, dispatch]);

 

  return (
    <section className="bg-gray-100 dark:bg-gray-900 h-[calc(100vh-5rem)] overflow-hidden flex gap-3 transition-colors">
      {/* Left Div */}
      <div className="flex-[3]">
        <Greetings />
        <div className="flex items-center w-full gap-3 px-8 mt-8">
          <MiniCard title="Total Earnings" icon={<BsCashCoin />} number={512} footerNum={1.6} />
          <MiniCard title="In Progress" icon={<GrInProgress />} number={16} footerNum={3.6} />
          <button
            onClick={() => navigate('/reports')}
            className="flex items-center gap-2 px-6 py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-lg"
          >
            <IoStatsChart className="text-xl" />
            <div className="text-left">
              <p className="font-semibold">View Reports</p>
              <p className="text-xs opacity-90">Analytics Dashboard</p>
            </div>
          </button>
        </div>
        <RecentOrders />
      </div>
      {/* Right Div */}
      <div className="flex-[2]">
        <PopularDishes isUpdating={!!orderData} orderId={orderData?.orderId} />
      </div>
    </section>
  );
};

export default Home;
