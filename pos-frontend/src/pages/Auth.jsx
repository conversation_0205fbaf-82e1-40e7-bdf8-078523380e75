import React, { useEffect, useState } from "react";
import restaurant from "../assets/images/restaurant-img.jpg"
import logo from "../assets/images/logo.png"
import Register from "../components/auth/Register";
import Login from "../components/auth/Login";
import EmailLogin from "../components/auth/EmailLogin";

const Auth = () => {

  useEffect(() => {
    document.title = "POS | Auth"
  }, [])

  const [isRegister, setIsRegister] = useState(false);
  const [loginMethod, setLoginMethod] = useState("code"); // "code" or "email"

  return (
    <div className="flex min-h-screen w-full">
      {/* Left Section */}
      <div className="w-1/2 relative flex items-center justify-center bg-cover">
        {/* BG Image */}
        <img className="w-full h-full object-cover" src={restaurant} alt="Restaurant Image" />

        {/* Black Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-80"></div>

        {/* Quote at bottom */}
        <blockquote className="absolute bottom-10 px-8 mb-10 text-2xl italic text-white">
          {/* "Serve customers the best food with prompt and friendly service in a
          welcoming atmosphere, and they'll keep coming back." */}
          <br />
          {/* <span className="block mt-4 text-yellow-400">- Founder of Restro</span> */}
        </blockquote>
      </div>

      {/* Right Section */}
      <div className="w-1/2 min-h-screen bg-white dark:bg-gray-900 p-10 relative transition-colors">
        <div className="flex flex-col items-center gap-2">
          {/* <img src={logo} alt="Restro Logo" className="h-14 w-14 border-2 rounded-full p-1" /> */}
          {/* <h1 className="text-lg font-semibold text-[#f5f5f5] tracking-wide">Restro</h1> */}
        </div>

        {/* Login Method Toggle - Fixed position at top */}
        {!isRegister && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-xl p-1 flex shadow-lg border border-gray-300 dark:border-gray-600 transition-colors">
              <button
                onClick={() => setLoginMethod("code")}
                className={`px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-200 ${loginMethod === "code"
                  ? "bg-blue-600 text-white shadow-lg"
                  : "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-300 dark:hover:bg-gray-600"
                  }`}
              >
                Code Login
              </button>
              <button
                onClick={() => setLoginMethod("email")}
                className={`px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-200 ${loginMethod === "email"
                  ? "bg-blue-600 text-white shadow-lg"
                  : "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-300 dark:hover:bg-gray-600"
                  }`}
              >
                Email Login
              </button>
            </div>
          </div>
        )}

        {/* <h2 className="text-4xl text-center mt-10 font-semibold text-yellow-400 mb-10">
          {isRegister ? "Employee Registration" : "Employee Login"}
        </h2> */}

        {/* Components */}
        {isRegister ? (
          <Register setIsRegister={setIsRegister} />
        ) : loginMethod === "code" ? (
          <Login />
        ) : (
          <EmailLogin />
        )}

        {/* <div className="flex justify-center mt-6">
          <p className="text-sm text-[#ababab]">
            {isRegister ? "Already have an account?" : "Don't have an account?"}
            <a onClick={() => setIsRegister(!isRegister)} className="text-yellow-400 font-semibold hover:underline" href="#">
              {isRegister ? "Sign in" : "Sign up"}
            </a>
          </p>
        </div> */}

      </div>
    </div>
  );
};

export default Auth;
