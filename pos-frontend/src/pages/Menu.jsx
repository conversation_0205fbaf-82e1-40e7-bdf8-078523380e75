import React, { useEffect, useState } from "react";
import <PERSON><PERSON>BottomNav from "../components/shared/POSBottomNav";
import MenuContainer from "../components/menu/MenuContainer";
import CartInfo from "../components/menu/CartInfo";
import { useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { initializeCart, removeItem, updateQuantity, exitEditMode } from "../redux/slices/cartSlice";
import { initializeTableFromOrder, setTable } from "../redux/slices/tableSlice";
import { clearCart } from "../redux/slices/cartSlice";
import { clearTable } from "../redux/slices/tableSlice";
import { removeUser } from "../redux/slices/userSlice";
import { enqueueSnackbar } from "notistack";
import { createOrder, updateOrder, logout, splitOrder, fetchVoidReasons } from "../https";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Modal from "../components/shared/Modal";
import { useNavigate } from "react-router-dom";
import SplitCheckModal from "../components/menu/SplitCheckModal";
import { axiosWrapper } from "../https/axiosWrapper";
import CustomerGreeting from "../components/shared/CustomerGreeting";

// VoidModal component for entering void quantity
const VoidModal = ({ open, onClose, item, onConfirm, cartItems }) => {
  const [input, setInput] = useState("");

  // Calculate total items in cart
  const totalItems = cartItems?.reduce((sum, cartItem) => sum + cartItem.quantity, 0) || 0;

  // Keyboard support (must always be called)
  React.useEffect(() => {
    if (!open) return;
    const handleKey = (e) => {
      if (e.key >= '0' && e.key <= '9') {
        if (canAddNum(e.key)) setInput(prev => String(Number(prev + e.key)));
      } else if (e.key === 'Backspace') {
        setInput(input => input.slice(0, -1));
      } else if (e.key === 'c' || e.key === 'C') {
        setInput("");
      } else if (e.key === 'Enter') {
        handleConfirm();
      }
    };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [input, open]);

  if (!open) return null;

  // If no specific item is selected, void entire cart
  const isVoidingEntireCart = !item;
  const maxQty = isVoidingEntireCart ? totalItems : item.quantity;

  // Prevent entering a value greater than maxQty
  const canAddNum = (num) => {
    const next = Number(String(input) + String(num));
    return next <= maxQty && String(next).length <= 3;
  };

  const handleNum = (num) => {
    if (canAddNum(num)) setInput(prev => String(Number(prev + num)));
  };

  const handleBack = () => setInput(input.slice(0, -1));
  const handleClear = () => setInput("");

  const handleConfirm = () => {
    const qty = Number(input);
    if (qty > 0 && qty <= maxQty) {
      onConfirm(qty);
      setInput("");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl p-8 w-[28rem] flex flex-col items-center transition-colors">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          {isVoidingEntireCart ? "Void Check" : "Void Item"}
        </h2>
        <div className="text-white mb-4 text-lg">
          {isVoidingEntireCart
            ? `Entire Check (${totalItems} items total)`
            : `${item.article.name} (In Cart: ${item.quantity})`
          }
        </div>
        <input
          className="w-48 h-20 text-center text-5xl font-bold p-6 rounded bg-[#181818] text-white mb-8 border-2 border-[#444]"
          value={input}
          readOnly
        />
        <div className="grid grid-cols-3 gap-4 mb-6">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 0].map(num => (
            <button
              key={num}
              className={`w-20 h-16 bg-[#333] text-white rounded-lg font-bold text-2xl hover:bg-[#444] ${!canAddNum(num) ? 'opacity-40 cursor-not-allowed' : ''}`}
              onClick={() => handleNum(num)}
              disabled={!canAddNum(num)}
            >
              {num}
            </button>
          ))}
          <button className="w-20 h-16 bg-[#444] text-white rounded-lg font-bold text-2xl" onClick={handleBack}>&larr;</button>
          <button className="w-20 h-16 bg-[#c0392b] text-white rounded-lg font-bold text-2xl" onClick={handleClear}>C</button>
        </div>
        <div className="flex gap-6">
          <button
            className="bg-gray-500 text-white px-8 py-3 rounded-lg font-bold text-lg"
            onClick={onClose}
          >Cancel</button>
          <button
            className={`bg-[#c0392b] text-white px-8 py-3 rounded-lg font-bold text-lg ${Number(input) > 0 && Number(input) <= maxQty ? '' : 'opacity-60 cursor-not-allowed'}`}
            disabled={!(Number(input) > 0 && Number(input) <= maxQty)}
            onClick={handleConfirm}
          >Void</button>
        </div>
      </div>
    </div>
  );
};

const Menu = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const orderData = location.state;
  const orderId = useSelector((state) => state.table.orderId);
  const cart = useSelector((state) => state.cart);
  const table = useSelector((state) => state.table.table);
  const isEditMode = useSelector((state) => state.cart.isEditMode);

  // Void logic state
  const [selectedCartItemId, setSelectedCartItemId] = useState(null);
  const [showVoidModal, setShowVoidModal] = useState(false);
  const [showSplitModal, setShowSplitModal] = useState(false);
  // New for void reason
  const [voidReasons, setVoidReasons] = useState([]);
  const [isVoidReasonModalOpen, setIsVoidReasonModalOpen] = useState(false);
  const [selectedVoidReason, setSelectedVoidReason] = useState(null);
  const [pendingVoidQty, setPendingVoidQty] = useState(null); // for single item void
  const [pendingVoidItem, setPendingVoidItem] = useState(null); // for single item void

  // Modal state for table creation/editing
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalName, setModalName] = useState("");
  const [modalPhone, setModalPhone] = useState("");
  const [modalGuestCount, setModalGuestCount] = useState(1);
  const [isEditingTable, setIsEditingTable] = useState(false);

  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  const [messageDestination, setMessageDestination] = useState(null); // 'restaurant' or 'bar'
  const [messageInput, setMessageInput] = useState("");
  const [messagesToSend, setMessagesToSend] = useState([]); // Array of { destination, message }

  const handleBack = () => {
    dispatch(exitEditMode());
    dispatch(clearTable());
    navigate("/orders", { replace: true, state: null });
  };

  const splitOrderMutation = useMutation({
    mutationFn: (data) => splitOrder(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orders-page"] });
      dispatch(exitEditMode());
      dispatch(clearTable());
      enqueueSnackbar("Check split successfully!", { variant: "success" });
      navigate("/orders");
    },
    onError: (error) => {
      const message = error.response?.data?.message || "Failed to split check";
      enqueueSnackbar(message, { variant: "error" });
    },
  });

  const orderMutation = useMutation({
    mutationFn: (data) => {
      if (orderId) {
        return updateOrder(orderId, data.orderData);
      }
      return createOrder(data.orderData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orders-page"] });
      dispatch(clearCart());
      dispatch(clearTable());
      enqueueSnackbar("Order sent successfully!", { variant: "success" });
      navigate("/orders");
    },
    onError: (error) => {
      const message = error.response?.data?.message || "Failed to send order";
      enqueueSnackbar(message, { variant: "error" });
    },
  });

  useEffect(() => {
    document.title = "POS | Menu";

    // Initialize with order data if available
    if (orderData) {
      dispatch(initializeTableFromOrder({
        orderId: orderData.orderId,
        table: orderData.table
      }));

      if (orderData.orderItems) {
        dispatch(initializeCart({
          orderItems: orderData.orderItems,
          orderId: orderData.orderId
        }));
      }
    }
  }, [orderData, dispatch]);

  const handleCancelEdit = () => {
    if (isEditMode) {
      dispatch(exitEditMode());
      dispatch(clearTable());
      navigate("/orders");
      return;
    }
    // Check if cart is empty
    if (cart.items.length === 0) {
      // Log out the user
      logout().then(() => {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        dispatch(removeUser());
        dispatch(clearCart());
        dispatch(clearTable());
        enqueueSnackbar("Logged out successfully!", { variant: "success" });
        navigate("/auth");
      }).catch((error) => {
        // Even if logout API fails, still log out locally
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        dispatch(removeUser());
        dispatch(clearCart());
        dispatch(clearTable());
        enqueueSnackbar("Logged out successfully!", { variant: "success" });
        navigate("/auth");
      });
    } else {
      // Cart is not empty, just clear cart and table
      dispatch(clearCart());
      dispatch(clearTable());
      enqueueSnackbar("Edit cancelled. Ready for new order.", { variant: "info" });
    }
  };

  const handlePrint = async () => {
    if (!table) {
      enqueueSnackbar("Please select a table first!", { variant: "warning" });
      return;
    }
    if (cart.items.length === 0) {
      enqueueSnackbar("Cart is empty!", { variant: "warning" });
      return;
    }
    // Get revenue center from localStorage
    let revenueCenter = null;
    try {
      revenueCenter = JSON.parse(localStorage.getItem("revenueCenter"));
    } catch { }
    const orderData = {
      table: {
        name: table.name,
        code: table.code,
        seats: modalGuestCount,
        phone: table.phone,
      },
      orderItems: cart.items.map((item) => ({
        article: item.article.id,
        quantity: item.quantity,
        price: item.article.price,
      })),
      totalAmount: cart.total,
      status: "Printed",
      servingPeriod: CustomerGreeting(),
      ...(revenueCenter?.id ? { revenueCenter: revenueCenter.id } : {}),
      orderMessage: messagesToSend,
    };

    try {
      // Check if we're in edit mode
      if (orderId) {
        // Update existing order
        await updateOrder(orderId, orderData);
        enqueueSnackbar("Order updated and sent for printing!", { variant: "success" });
      } else {
        // Create new order
        await createOrder(orderData);
        enqueueSnackbar("Order sent for printing!", { variant: "success" });
      }
      queryClient.invalidateQueries({ queryKey: ["orders-page"] });
      dispatch(clearCart());
      dispatch(clearTable());
      navigate("/orders");
    } catch (error) {
      enqueueSnackbar("Failed to print receipt!", { variant: "error" });
    }
  };

  const handleSendOrder = () => {
    if (!table) {
      enqueueSnackbar("Please select a table first!", { variant: "warning" });
      return;
    }
    if (cart.items.length === 0) {
      enqueueSnackbar("Cart is empty!", { variant: "warning" });
      return;
    }
    // Get revenue center from localStorage
    let revenueCenter = null;
    try {
      revenueCenter = JSON.parse(localStorage.getItem("revenueCenter"));
    } catch { }
    const orderData = {
      table: {
        name: table.name,
        code: table.code,
      },
      orderItems: cart.items.map((item) => ({
        article: item.article.id,
        quantity: item.quantity,
        price: item.article.price,
      })),
      totalAmount: cart.total,
      status: "Sent",
      servingPeriod: CustomerGreeting(),
      ...(revenueCenter?.id ? { revenueCenter: revenueCenter.id } : {}),
      orderMessage: messagesToSend,
    };

    orderMutation.mutate({ orderData });
    setMessagesToSend([]); // Clear after sending
  };




  const handlePay = () => {
    if (!table) {
      enqueueSnackbar("Please select a table first!", { variant: "warning" });
      return;
    }
    if (cart.items.length === 0) {
      enqueueSnackbar("Cart is empty!", { variant: "warning" });
      return;
    }

    // If in edit mode, don't send order again - just navigate to payment
    if (isEditMode) {
      // Remove new items (without orderItemId) before navigating to payment
      const itemsWithOrderId = cart.items.filter(item => item.orderItemId);
      if (itemsWithOrderId.length !== cart.items.length) {
        dispatch({ type: 'cart/replaceItems', payload: itemsWithOrderId });
      }

      // Navigate to payment page
      navigate("/payment", {
        state: {
          isEditMode,
          voidEnabled: itemsWithOrderId.length > 0,
          voidQuantity: itemsWithOrderId.find(i => i.article.id === selectedCartItemId)?.quantity || 0,
          cartIsEmpty: itemsWithOrderId.length === 0,
          selectedCartItemId,
        }
      });
      return;
    }

    // Not in edit mode - send order first, then navigate to payment
    // Get revenue center from localStorage
    let revenueCenter = null;
    try {
      revenueCenter = JSON.parse(localStorage.getItem("revenueCenter"));
    } catch { }

    const orderData = {
      table: {
        name: table.name,
        code: table.code,
      },
      orderItems: cart.items.map((item) => ({
        article: item.article.id,
        quantity: item.quantity,
        price: item.article.price,
      })),
      totalAmount: cart.total,
      status: "Sent",
      servingPeriod: CustomerGreeting(),
      ...(revenueCenter?.id ? { revenueCenter: revenueCenter.id } : {}),
      orderMessage: messagesToSend,
    };

    // Create a custom mutation for pay that navigates to payment page on success
    const payMutation = {
      mutate: async (data) => {
        try {
          let createdOrder;
          if (orderId) {
            createdOrder = await updateOrder(orderId, data.orderData);
          } else {
            createdOrder = await createOrder(data.orderData);
          }

          queryClient.invalidateQueries({ queryKey: ["orders-page"] });
          enqueueSnackbar("Order sent successfully!", { variant: "success" });
          setMessagesToSend([]); // Clear after sending

          // Put in edit mode with the created/updated order
          dispatch(initializeTableFromOrder({
            orderId: createdOrder.data.id,
            table: table
          }));

          // Initialize cart with order items that have orderItemId
          const orderItemsWithId = createdOrder.data.orderItems.map(item => ({
            ...item,
            orderItemId: item.id,
            article: item.article
          }));

          dispatch(initializeCart({
            orderItems: orderItemsWithId,
            orderId: createdOrder.data.id
          }));

          // Navigate to payment page in edit mode
          navigate("/payment", {
            state: {
              isEditMode: true,
              voidEnabled: orderItemsWithId.length > 0,
              voidQuantity: orderItemsWithId.find(i => i.article.id === selectedCartItemId)?.quantity || 0,
              cartIsEmpty: orderItemsWithId.length === 0,
              selectedCartItemId,
            }
          });
        } catch (error) {
          const message = error.response?.data?.message || "Failed to send order";
          enqueueSnackbar(message, { variant: "error" });
        }
      }
    };

    payMutation.mutate({ orderData });
  };

  
  // Fetch void reasons
  const openVoidReasonModal = async (item = null, qty = null) => {
    try {
      const res = await fetchVoidReasons();
      setVoidReasons(res.data.data);
      setIsVoidReasonModalOpen(true);
      setPendingVoidItem(item);
      setPendingVoidQty(qty);
    } catch (e) {
      enqueueSnackbar("Failed to fetch void reasons", { variant: "error" });
    }
  };

  // Handle void (open modal or reason modal)
  const handleVoid = () => {
    if (selectedCartItemId) {
      // Single item: show quantity modal as before
      setShowVoidModal(true);
    } else {
      // Entire cart: show void reason modal
      openVoidReasonModal();
    }
  };

  // Confirm void logic from modal (single item)
  const handleVoidConfirm = (qty) => {
    const item = cart.items.find(i => i.article.id === selectedCartItemId);
    if (!item) return;
    // After entering quantity, show void reason modal
    openVoidReasonModal(item, qty);
    setShowVoidModal(false);
  };

  // Handle void reason selection (entire cart or single item)
  const handleVoidReasonSelect = async (reasonId) => {
    setSelectedVoidReason(reasonId);
    let payload;
    if (pendingVoidItem) {
      // Single item void
      payload = {
        data: [
          {
            orderItemId: pendingVoidItem.orderItemId,
            reason: reasonId,
            originalQuantity: pendingVoidItem.quantity,
            voidedQuantity: pendingVoidQty,
          },
        ],
      };
    } else {
      // Entire cart void
      payload = {
        data: cart.items.map(item => ({
          orderItemId: item.orderItemId,
          reason: reasonId,
          originalQuantity: item.quantity,
          voidedQuantity: item.quantity,
        })),
      };
    }

    try {
      await axiosWrapper.post("/api/orders/ordered-items/void/", payload);
      if (pendingVoidItem) {
        // Update cart for single item
        if (pendingVoidQty >= pendingVoidItem.quantity) {
          dispatch(removeItem(pendingVoidItem.article.id));
          enqueueSnackbar("Item voided.", { variant: "info" });
        } else {
          dispatch(updateQuantity({ articleId: pendingVoidItem.article.id, quantity: pendingVoidItem.quantity - pendingVoidQty }));
          enqueueSnackbar(`Voided ${pendingVoidQty} from item.`, { variant: "info" });
        }
        setSelectedCartItemId(null);
        queryClient.invalidateQueries({ queryKey: ["orders-page"] });
        // dispatch(clearCart());
        // dispatch(clearTable());
        // navigate("/orders");
      } else {
        // Entire cart
        dispatch(clearCart());
        enqueueSnackbar("Check voided successfully!", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["orders-page"] });
        dispatch(clearCart());
        dispatch(clearTable());
        navigate("/orders");
      }
    } catch (e) {
      enqueueSnackbar("Failed to void item(s)", { variant: "error" });
    } finally {
      setIsVoidReasonModalOpen(false);
      setPendingVoidItem(null);
      setPendingVoidQty(null);
      setSelectedVoidReason(null);
    }
  };

  // Modal control functions
  const openModal = (editing = false) => {
    setIsEditingTable(editing);
    if (editing && table) {
      setModalName(table.name || "");
      setModalPhone(table.phone || "");
      setModalGuestCount(table.guests || 1);
    } else {
      setModalName("");
      setModalPhone("");
      setModalGuestCount(1);
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsEditingTable(false);
  };

  const increment = () => {
    if (modalGuestCount >= 6) return;
    setModalGuestCount((prev) => prev + 1);
  };

  const decrement = () => {
    if (modalGuestCount <= 1) return;
    setModalGuestCount((prev) => prev - 1);
  };

  const handleCreateTable = () => {
    if (!modalName || modalName.trim().length === 0) return;

    const tableId = isEditingTable ? table.tableId : `table_${Date.now()}`;
    const tableCode = isEditingTable ? table.code : `CHK_${Math.random().toString(36).slice(2, 10).toUpperCase()}`;

    dispatch(setTable({
      name: modalName,
      code: tableCode,
      phone: modalPhone,
      guests: modalGuestCount,
      tableId
    }));

    enqueueSnackbar(isEditingTable ? "Table updated successfully!" : "Table created successfully!", { variant: "success" });
    closeModal();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleCreateTable();
    }
  };

  const handleFunctionChecks = (actionId) => {
    switch (actionId) {
      case "split_check":
        if (isEditMode && cart.items.length > 0) {
          setShowSplitModal(true);
        }
        break;
      case "merge_check":
        enqueueSnackbar("Merge check functionality coming soon!", { variant: "info" });
        break;
      case "void_check":
        handleVoid();
        break;
      default:
        enqueueSnackbar("Function checks coming soon!", { variant: "info" });
    }
  };

  const handleSplitCheck = (splitData) => {
    splitOrderMutation.mutate(splitData);
  };

  // Open message modal from MenuContainer
  const handleOpenMessageModal = (destination) => {
    setMessageDestination(destination);
    setIsMessageModalOpen(true);
    setMessageInput("");
  };

  // Handle message modal submit
  const handleSendMessage = () => {
    if (messageInput.trim()) {
      setMessagesToSend((prev) => [
        ...prev,
        { destination: messageDestination, message: messageInput.trim() },
      ]);
      setIsMessageModalOpen(false);
      setMessageInput("");
      setMessageDestination(null);
    }
  };

  return (
    <section className="bg-gray-100 dark:bg-gray-900 h-[calc(100vh-5rem)] overflow-hidden flex gap-3 transition-colors">
      {/* Left Div */}
      <div className="flex-[3]">
        {/* No header bar, just the menu grid */}
        <MenuContainer
          isUpdating={!!orderData}
          selectedCartItemId={selectedCartItemId}
          openModal={() => openModal(false)}
          onOpenMessageModal={handleOpenMessageModal}
        />
      </div>
      {/* Right Div */}
      <div className="flex-[1] bg-white dark:bg-gray-800 mt-4 mr-3 h-[780px] rounded-lg pt-2 border border-gray-200 dark:border-gray-700 transition-colors">
        {/* Cart Items */}
        <CartInfo
          selectedCartItemId={selectedCartItemId}
          onSelectCartItem={setSelectedCartItemId}
          onEditTableName={openModal}
          isEditMode={isEditMode}
        />
      </div>
      {/* Message Modal */}
      {isMessageModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
          <div className="bg-[#232b2b] rounded-2xl p-10 w-[40rem] h-[28rem] flex flex-col items-center justify-center shadow-2xl border-4 border-blue-700">
            <h2 className="text-4xl font-extrabold text-white mb-8 text-center">
              {messageDestination === 'bar' ? 'INFO KITCHEN' : 'INFO BAR'}
            </h2>
            <textarea
              className="w-full h-40 p-6 rounded-xl bg-[#181818] text-white mb-8 border-2 border-[#444] resize-none text-2xl focus:outline-none focus:border-blue-400"
              value={messageInput}
              onChange={e => setMessageInput(e.target.value)}
              placeholder="Type your message here..."
              autoFocus
              style={{ minHeight: '8rem' }}
            />
            <div className="flex gap-10 w-full justify-center mt-4">
              <button
                className="bg-gray-500 text-white px-12 py-5 rounded-xl font-bold text-2xl shadow-lg hover:bg-gray-600 transition-all"
                onClick={() => setIsMessageModalOpen(false)}
              >Cancel</button>
              <button
                className={`bg-blue-600 text-white px-12 py-5 rounded-xl font-bold text-2xl shadow-lg hover:bg-blue-700 transition-all ${!messageInput.trim() ? 'opacity-60 cursor-not-allowed' : ''}`}
                disabled={!messageInput.trim()}
                onClick={handleSendMessage}
              >Send</button>
            </div>
          </div>
        </div>
      )}

      <POSBottomNav
        onBack={handleBack}
        onClear={() => {
          dispatch(clearCart());
          dispatch(clearTable());
          // If cart was already empty, redirect to orders page
          if (cart.items.length === 0) {
            navigate("/orders");
          }
        }}
        onTransCkl={handleCancelEdit}
        isEditMode={isEditMode}
        onPrint={handlePrint}
        onVoid={handleVoid}
        voidEnabled={cart.items.length > 0}
        voidQuantity={cart.items.find(i => i.article.id === selectedCartItemId)?.quantity || 0}
        onSendOrder={handleSendOrder}
        onPay={handlePay}
        onFunctionChecks={handleFunctionChecks}
        cartIsEmpty={cart.items.length === 0}
        // payButtonDisabled={!isEditMode}
        payButtonDisabled={cart.items.length === 0}
        printButtonDisabled={cart.items.length === 0}
        sendOrderButtonDisabled={cart.items.length === 0}
      />
      <VoidModal
        open={showVoidModal}
        onClose={() => { setShowVoidModal(false); }}
        item={cart.items.find(i => i.article.id === selectedCartItemId)}
        onConfirm={handleVoidConfirm}
        cartItems={cart.items}
      />
      <SplitCheckModal
        isOpen={showSplitModal}
        onClose={() => setShowSplitModal(false)}
        cartItems={cart.items}
        onSplit={handleSplitCheck}
      />
      <Modal isOpen={isModalOpen} onClose={closeModal} title={isEditingTable ? "Edit Table" : "Create Table"}>
        <div className="space-y-6">
          <div>
            <label className="block text-[#ababab] mb-2 text-lg font-bold">Name <span className="text-red-500">*</span></label>
            <div className="flex items-center rounded-xl p-5 px-6 bg-[#1f1f1f]">
              <input
                value={modalName}
                onChange={(e) => setModalName(e.target.value)}
                onKeyPress={handleKeyPress}
                type="text"
                name=""
                placeholder="Table name"
                id=""
                className="bg-transparent flex-1 text-white focus:outline-none text-xl placeholder-gray-400"
                required
                autoFocus
              />
            </div>
          </div>
          <div>
            <label className="block text-[#ababab] mb-2 mt-3 text-lg font-bold">Phone</label>
            <div className="flex items-center rounded-xl p-5 px-6 bg-[#1f1f1f]">
              <input
                value={modalPhone}
                onChange={(e) => setModalPhone(e.target.value)}
                type="number"
                name=""
                placeholder="optional"
                id=""
                className="bg-transparent flex-1 text-white focus:outline-none text-xl placeholder-gray-400"
              />
            </div>
          </div>
          <div>
            <label className="block mb-2 mt-3 text-lg font-bold text-[#ababab]">Guest</label>
            <div className="flex items-center justify-between bg-[#1f1f1f] px-6 py-5 rounded-xl">
              <button onClick={decrement} className="text-yellow-500 text-3xl">&minus;</button>
              <span className="text-white text-xl">{modalGuestCount} Person</span>
              <button onClick={increment} className="text-yellow-500 text-3xl">&#43;</button>
            </div>
          </div>
          <button
            onClick={handleCreateTable}
            className={`w-full bg-[#F6B100] text-[#1a1a1a] rounded-xl py-5 mt-8 text-xl font-bold shadow-lg hover:bg-yellow-700 transition ${!modalName ? 'opacity-60 cursor-not-allowed' : ''}`}
            disabled={!modalName}
          >
            {isEditingTable ? "Update Table" : "Create Table"}
          </button>

        </div>
      </Modal>
      {/* Void Reason Modal */}
      {isVoidReasonModalOpen && (
        <Modal isOpen={isVoidReasonModalOpen} onClose={() => setIsVoidReasonModalOpen(false)} title="Select Void Reason">
          <ul className="space-y-2">
            {voidReasons.map(reason => (
              <li key={reason.id}>
                <button
                  className="w-full text-left px-4 py-3 rounded bg-[#232b2b] text-white hover:bg-[#444]"
                  onClick={() => handleVoidReasonSelect(reason.id)}
                >
                  <div className="font-bold">{reason.name}</div>
                  <div className="text-sm text-gray-400">{reason.description}</div>
                </button>
              </li>
            ))}
          </ul>
        </Modal>
      )}
    </section>
  );
};

export default Menu;