import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Io<PERSON><PERSON>ning, 
  Io<PERSON><PERSON>t, 
  IoCheckmarkCircle, 
  IoTime,
  IoAdd,
  IoSearch,
  IoFilter,
  IoGrid,
  IoList
} from 'react-icons/io5';
import { axiosWrapper } from '../https/axiosWrapper';
import InventoryDashboard from '../components/inventory/InventoryDashboard';
import InventoryItemsList from '../components/inventory/InventoryItemsList';
import InventoryAlerts from '../components/inventory/InventoryAlerts';
import AddInventoryItemModal from '../components/inventory/AddInventoryItemModal';
import StockAdjustmentModal from '../components/inventory/StockAdjustmentModal';

const Inventory = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [viewMode, setViewMode] = useState('grid');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Fetch inventory dashboard data
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery({
    queryKey: ['inventory-dashboard'],
    queryFn: () => axiosWrapper.get('/api/inventory/items/dashboard/'),
    staleTime: 30000,
  });

  // Fetch inventory items
  const { data: inventoryItems, isLoading: itemsLoading, refetch: refetchItems } = useQuery({
    queryKey: ['inventory-items', searchTerm, filterType],
    queryFn: () => {
      const params = {};
      if (searchTerm) params.search = searchTerm;
      if (filterType === 'low_stock') params.low_stock = 'true';
      if (filterType === 'out_of_stock') params.out_of_stock = 'true';
      return axiosWrapper.get('/api/inventory/items/', { params });
    },
    staleTime: 30000,
  });

  // Fetch alerts
  const { data: alerts, isLoading: alertsLoading } = useQuery({
    queryKey: ['inventory-alerts'],
    queryFn: () => axiosWrapper.get('/api/inventory/items/alerts/'),
    staleTime: 30000,
  });

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: IoGrid },
    { id: 'items', label: 'Items', icon: IoList },
    { id: 'alerts', label: 'Alerts', icon: IoWarning },
  ];

  const filterOptions = [
    { value: 'all', label: 'All Items' },
    { value: 'low_stock', label: 'Low Stock' },
    { value: 'out_of_stock', label: 'Out of Stock' },
    { value: 'active', label: 'Active' },
  ];

  const handleStockAdjustment = (item) => {
    setSelectedItem(item);
    setShowAdjustModal(true);
  };

  const handleModalClose = () => {
    setShowAddModal(false);
    setShowAdjustModal(false);
    setSelectedItem(null);
    refetchItems();
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Inventory Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Track stock levels, manage batches, and monitor expiry dates
            </p>
          </div>
          
          <div className="flex items-center gap-3 mt-4 lg:mt-0">
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors"
            >
              <IoAdd className="text-lg" />
              Add Item
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        {dashboardData?.data && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Total Items</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {dashboardData.data.total_items}
                  </p>
                </div>
                <IoCheckmarkCircle className="text-3xl text-green-500" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Low Stock</p>
                  <p className="text-2xl font-bold text-yellow-500">
                    {dashboardData.data.low_stock_items}
                  </p>
                </div>
                <IoWarning className="text-3xl text-yellow-500" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Out of Stock</p>
                  <p className="text-2xl font-bold text-red-500">
                    {dashboardData.data.out_of_stock_items}
                  </p>
                </div>
                <IoAlert className="text-3xl text-red-500" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Stock Value</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    ${parseFloat(dashboardData.data.total_stock_value || 0).toFixed(2)}
                  </p>
                </div>
                <IoTime className="text-3xl text-blue-500" />
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="flex items-center gap-1 mb-6 bg-gray-200 dark:bg-gray-700 rounded-lg p-1 transition-colors">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <Icon className="text-lg" />
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Search and Filters */}
        {activeTab === 'items' && (
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <IoSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400" />
              <input
                type="text"
                placeholder="Search inventory items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
              />
            </div>
            
            <div className="flex items-center gap-3">
              <div className="relative">
                <IoFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400" />
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="pl-10 pr-8 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-gray-100 focus:outline-none focus:border-blue-500 appearance-none transition-colors"
                >
                  {filterOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center gap-1 bg-gray-200 dark:bg-gray-700 rounded-lg p-1 transition-colors">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'grid' ? 'bg-blue-600 text-white' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                  }`}
                >
                  <IoGrid />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'list' ? 'bg-blue-600 text-white' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                  }`}
                >
                  <IoList />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
          {activeTab === 'dashboard' && (
            <InventoryDashboard
              data={dashboardData?.data}
              loading={dashboardLoading}
            />
          )}
          
          {activeTab === 'items' && (
            <InventoryItemsList
              items={inventoryItems}
              loading={itemsLoading}
              viewMode={viewMode}
              onStockAdjustment={handleStockAdjustment}
            />
          )}
          
          {activeTab === 'alerts' && (
            <InventoryAlerts
              alerts={alerts}
              loading={alertsLoading}
            />
          )}
        </div>
      </div>

      {/* Modals */}
      {showAddModal && (
        <AddInventoryItemModal
          onClose={handleModalClose}
        />
      )}

      {showAdjustModal && selectedItem && (
        <StockAdjustmentModal
          item={selectedItem}
          onClose={handleModalClose}
        />
      )}
    </div>
  );
};

export default Inventory;
