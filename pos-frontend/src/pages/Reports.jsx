import React, { useState } from "react";
import { useSelector } from "react-redux";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { IoFilter, IoRefresh } from "react-icons/io5";
import { axiosWrapper } from "../https/axiosWrapper";
import ReportFilters from "../components/reports/ReportFilters";
import SalesSummaryCard from "../components/reports/SalesSummaryCard";
import SalesCharts from "../components/reports/SalesCharts";
import PopularItemsSection from "../components/reports/PopularItemsSection";
import PerformanceSection from "../components/reports/PerformanceSection";
import FamilyBreakdownSection from "../components/reports/FamilyBreakdownSection";


const Reports = () => {
  const { defaultCurrency } = useSelector(state => state.general);
  const queryClient = useQueryClient();
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [filters, setFilters] = useState({
    dateRangePreset: "today",
    startDate: null,
    endDate: null,
    status: "Paid",
    userId: "",
    revenueCenter: "",
    workstation: "",
    servingPeriod: "",
  });
  const [expandedFamilies, setExpandedFamilies] = useState({});
  const [expandedSubFamilies, setExpandedSubFamilies] = useState({});
  const [expandAll, setExpandAll] = useState(false);

  const analyticsParams = {
    status: filters.status,
    // Use either date preset OR custom dates, not both
    ...(filters.startDate || filters.endDate
      ? {
          ...(filters.startDate && { start_date: filters.startDate }),
          ...(filters.endDate && { end_date: filters.endDate })
        }
      : { date_range_preset: filters.dateRangePreset }
    ),
    ...(filters.userId && { user_id: filters.userId }),
    ...(filters.revenueCenter && { revenue_center: filters.revenueCenter }),
    ...(filters.workstation && { workstation: filters.workstation }),
    ...(filters.servingPeriod && { serving_period: filters.servingPeriod }),
  };

  // Fetch filter data
  const { data: users } = useQuery({
    queryKey: ["users"],
    queryFn: () => axiosWrapper.get("/api/accounts/?paginate=false"),
    staleTime: 300000,
  });

  const { data: literals } = useQuery({
    queryKey: ["literals"],
    queryFn: () => axiosWrapper.get("/api/literals/all/"),
    staleTime: 300000,
  });

  // Fetch analytics data
  const { data: salesSummary, isLoading: summaryLoading } = useQuery({
    queryKey: ["sales-summary", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/sales-summary/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: salesByFamily, isLoading: familyLoading } = useQuery({
    queryKey: ["sales-by-family", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/sales-by-family/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: salesByHour } = useQuery({
    queryKey: ["sales-by-hour", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/sales-by-hour/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: salesByDay } = useQuery({
    queryKey: ["sales-by-day", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/sales-by-day/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: popularItemsByQuantity } = useQuery({
    queryKey: ["popular-items-quantity", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/popular-items/quantity/", { params: { ...analyticsParams, limit: 10 } }),
    staleTime: 30000,
  });

  const { data: popularItemsByRevenue } = useQuery({
    queryKey: ["popular-items-revenue", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/popular-items/revenue/", { params: { ...analyticsParams, limit: 10 } }),
    staleTime: 30000,
  });

  const { data: userPerformance } = useQuery({
    queryKey: ["user-performance", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/user-performance/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: tablePerformance } = useQuery({
    queryKey: ["table-performance", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/table-performance/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: workstationPerformance } = useQuery({
    queryKey: ["workstation-performance", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/workstation-performance/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: orderStatus } = useQuery({
    queryKey: ["order-status", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/order-status/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: servingPeriod } = useQuery({
    queryKey: ["serving-period", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/serving-period/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: tips } = useQuery({
    queryKey: ["tips", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/tips/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: payment } = useQuery({
    queryKey: ["payment", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/payment/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: discount } = useQuery({
    queryKey: ["discount", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/discount/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: voids } = useQuery({
    queryKey: ["voids", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/voids/", { params: analyticsParams }),
    staleTime: 30000,
  });

  const { data: salesHierarchy } = useQuery({
    queryKey: ["sales-hierarchy", analyticsParams],
    queryFn: () => axiosWrapper.get("/api/analytics/sales-hierarchy/", { 
      params: { ...analyticsParams, includeArticles: true, includeSubFamily: true } 
    }),
    staleTime: 30000,
  });

  const handleRefresh = () => {
    // Invalidate all analytics queries to refetch with current filters
    queryClient.invalidateQueries({ queryKey: ["sales-summary"] });
    queryClient.invalidateQueries({ queryKey: ["sales-by-family"] });
    queryClient.invalidateQueries({ queryKey: ["sales-by-hour"] });
    queryClient.invalidateQueries({ queryKey: ["sales-by-day"] });
    queryClient.invalidateQueries({ queryKey: ["popular-items-quantity"] });
    queryClient.invalidateQueries({ queryKey: ["popular-items-revenue"] });
    queryClient.invalidateQueries({ queryKey: ["user-performance"] });
    queryClient.invalidateQueries({ queryKey: ["table-performance"] });
    queryClient.invalidateQueries({ queryKey: ["workstation-performance"] });
    queryClient.invalidateQueries({ queryKey: ["order-status"] });
    queryClient.invalidateQueries({ queryKey: ["serving-period"] });
    queryClient.invalidateQueries({ queryKey: ["tips"] });
    queryClient.invalidateQueries({ queryKey: ["payment"] });
    queryClient.invalidateQueries({ queryKey: ["discount"] });
    queryClient.invalidateQueries({ queryKey: ["voids"] });
    queryClient.invalidateQueries({ queryKey: ["sales-hierarchy"] });
  };

  const usersData = users?.data?.data || [];
  const literalsData = literals?.data?.data || {};
  const workstationsData = literalsData.workstations || [];
  const revenueCentersData = literalsData.revenueCenter || [];



  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="mx-4 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Reports Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive analytics and insights
            </p>
          </div>
          <div className="flex gap-3 mt-4 sm:mt-0">
            <button
              onClick={() => setShowFiltersModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <IoFilter className="w-4 h-4" />
              Filters
            </button>
            <button
              onClick={handleRefresh}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <IoRefresh className="w-4 h-4" />
              Refresh
            </button>
          </div>
        </div>

        {/* Sales Summary Cards */}
        <SalesSummaryCard
          data={salesSummary?.data?.data}
          isLoading={summaryLoading}
          currency={defaultCurrency}
        />

        {/* Charts Section */}
        <SalesCharts
          salesByHour={salesByHour}
          salesByDay={salesByDay}
          salesByFamily={salesByFamily}
          isLoading={familyLoading}
          currency={defaultCurrency}
        />

        {/* Popular Items */}
        {/* <PopularItemsSection
          popularByQuantity={popularItemsByQuantity}
          popularByRevenue={popularItemsByRevenue}
          currency={defaultCurrency}
        /> */}

        {/* Performance Section */}
        <PerformanceSection
          userPerformance={userPerformance}
          tablePerformance={tablePerformance}
          workstationPerformance={workstationPerformance}
          currency={defaultCurrency}
        />

        {/* Family Breakdown */}
        <FamilyBreakdownSection
          salesHierarchy={salesHierarchy}
          expandedFamilies={expandedFamilies}
          setExpandedFamilies={setExpandedFamilies}
          expandedSubFamilies={expandedSubFamilies}
          setExpandedSubFamilies={setExpandedSubFamilies}
          expandAll={expandAll}
          setExpandAll={setExpandAll}
          currency={defaultCurrency}
        />

        {/* Filters Modal */}
        {showFiltersModal && (
          <ReportFilters
            filters={filters}
            setFilters={setFilters}
            users={usersData}
            workstations={workstationsData}
            revenueCenters={revenueCentersData}
            onClose={() => setShowFiltersModal(false)}
          />
        )}
      </div>
    </div>
  );
};

export default Reports;
