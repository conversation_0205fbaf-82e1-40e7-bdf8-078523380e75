import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getSalesHierarchy, getUserPerformance, getSalesSummary, getSalesByFamily, getUsers, getOrders } from "../https/index";
import { enqueueSnackbar } from "notistack";
import { IoArrowBack, IoCalendar, IoPeople, IoStatsChart, IoReceipt, IoPrint } from "react-icons/io5";
import { MdDashboard, MdCategory, MdPerson, MdTrendingUp, MdReceipt } from "react-icons/md";

const ChecksReport = () => {
    const navigate = useNavigate();
    const { defaultCurrency } = useSelector((state) => state.general);
    const [currentTime, setCurrentTime] = useState(new Date());
    const { _id: userId } = useSelector((state) => state.user);

    // Report configuration
    const [reportType, setReportType] = useState("sales-summary");
    const [selectedUser, setSelectedUser] = useState("");
    const [dateFilter, setDateFilter] = useState("today");
    const [start_date, setStartDate] = useState("");
    const [end_date, setEndDate] = useState("");
    const [useCustomDate, setUseCustomDate] = useState(false);

    // Collapsible state for sales hierarchy
    const [expandedFamilies, setExpandedFamilies] = useState({});
    const [expandedSubFamilies, setExpandedSubFamilies] = useState({});
    const [expandAll, setExpandAll] = useState(false);

    useEffect(() => {
        document.title = "POS | Checks Report";
    }, []);

    // Live clock update
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);
        return () => clearInterval(timer);
    }, []);

    // Fetch users for filter
    const { data: usersData, error: usersError } = useQuery({
        queryKey: ["users"],
        queryFn: () => getUsers({ paginate: false }),
        enabled: !!userId,
        onSuccess: (data) => {
            // removed user logs
        },
        onError: (error) => {
            // removed user logs
        }
    });

    // Build analytics params
    const analyticsParams = {
        status: "Paid",
        ...(selectedUser ? { user: selectedUser } : {}),
        ...(useCustomDate
            ? {
                start_date,
                end_date
            }
            : {
                date_range_preset: dateFilter
            }
        )
    };

    // Fetch analytics data based on report type
    const { data: salesSummary, isLoading: summaryLoading } = useQuery({
        queryKey: ["sales-summary", selectedUser, dateFilter, start_date, end_date, useCustomDate],
        queryFn: () => getSalesSummary(analyticsParams),
        enabled: reportType === "sales-summary"
    });

    const { data: salesByFamily, isLoading: familyLoading } = useQuery({
        queryKey: ["sales-by-family", selectedUser, dateFilter, start_date, end_date, useCustomDate],
        queryFn: () => getSalesByFamily(analyticsParams),
        enabled: reportType === "sales-by-family"
    });

    const { data: userPerformance, isLoading: performanceLoading } = useQuery({
        queryKey: ["user-performance", selectedUser, dateFilter, start_date, end_date, useCustomDate],
        queryFn: () => getUserPerformance(analyticsParams),
        enabled: reportType === "user-performance"
    });

    const { data: salesHierarchy, isLoading: hierarchyLoading } = useQuery({
        queryKey: ["sales-hierarchy", selectedUser, dateFilter, start_date, end_date, useCustomDate],
        queryFn: () => getSalesHierarchy(analyticsParams),
        enabled: reportType === "sales-hierarchy"
    });

    // Closed Orders state and query
    const { data: closedOrders, isLoading: closedOrdersLoading } = useQuery({
        queryKey: ["closed-orders", dateFilter, useCustomDate, start_date, end_date],
        queryFn: async () => {
            // Always fetch Paid orders for today only
            const params = { status: "Paid", date_range_preset: "today", paginate: false };
            // You may need to adjust the endpoint if not getOrders
            const res = await getOrders(params);
            return res?.data?.data || [];
        },
        enabled: reportType === "closed-orders"
    });

    const isLoading = summaryLoading || familyLoading || performanceLoading || hierarchyLoading || closedOrdersLoading;

    const handleBackToOrders = () => {
        navigate("/orders");
    };

    const formatCurrency = (amount) => {
        return `${defaultCurrency}${Number(amount || 0).toFixed(2)}`;
    };

    const formatNumber = (num) => {
        return Number(num || 0).toLocaleString();
    };

    const toggleFamily = (familyId) => {
        setExpandedFamilies((prev) => ({ ...prev, [familyId]: !prev[familyId] }));
    };
    const toggleSubFamily = (subFamilyId) => {
        setExpandedSubFamilies((prev) => ({ ...prev, [subFamilyId]: !prev[subFamilyId] }));
    };

    // Expand/collapse all logic
    const handleExpandAll = () => {
        const hierarchy = salesHierarchy?.data?.data?.[0]?.hierarchy || {};
        const allFamilyIds = Object.values(hierarchy).map(({ family }) => family.id);
        const allSubFamilyIds = Object.values(hierarchy)
            .flatMap(({ subFamilies }) => Object.values(subFamilies).map((sf) => sf.id));
        if (!expandAll) {
            // Expand all
            setExpandedFamilies(Object.fromEntries(allFamilyIds.map((id) => [id, true])));
            setExpandedSubFamilies(Object.fromEntries(allSubFamilyIds.map((id) => [id, true])));
        } else {
            // Collapse all
            setExpandedFamilies({});
            setExpandedSubFamilies({});
        }
        setExpandAll((prev) => !prev);
    };

    // Print handler for closed orders
    const handlePrintOrder = (order) => {
        enqueueSnackbar('Print me', { variant: 'info' });
    };

    // Report type options with icons
    const reportOptions = [
        { value: "sales-summary", label: "Sales Summary", icon: <MdDashboard className="w-5 h-5" />, description: "Overview of total sales and orders" },
        { value: "sales-by-family", label: "Sales by Category", icon: <MdCategory className="w-5 h-5" />, description: "Sales breakdown by menu categories" },
        { value: "user-performance", label: "Staff Performance", icon: <MdPerson className="w-5 h-5" />, description: "How each staff member performed" },
        { value: "sales-hierarchy", label: "Detailed Sales", icon: <MdTrendingUp className="w-5 h-5" />, description: "Complete sales breakdown by items" },
        { value: "closed-orders", label: "Today's Orders", icon: <MdReceipt className="w-5 h-5" />, description: "All completed orders for today" }
    ];

    return (
        <section className="bg-[#1f1f1f] min-h-screen flex flex-col">
            {/* Header */}
            <div className="bg-[#232323] border-b border-[#333] px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <button
                            onClick={handleBackToOrders}
                            className="flex items-center gap-2 bg-[#383838] hover:bg-[#404040] text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                        >
                            <IoArrowBack className="w-5 h-5" />
                            Back to Orders
                        </button>
                        <div className="text-white text-xl font-bold">📊 Reports & Analytics</div>
                    </div>
                    <div className="text-gray-400 text-sm">
                        {new Intl.DateTimeFormat('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: true,
                        }).format(currentTime)}
                    </div>
                </div>
            </div>

            {/* Controls Section */}
            <div className="bg-[#2a2a2a] border-b border-[#333] px-6 py-4">
                <div className="flex flex-wrap items-center gap-4">
                    {/* Report Type Selection */}
                    <div className="flex-1 min-w-[300px]">
                        <label className="block text-gray-300 text-sm font-medium mb-2">📋 Report Type</label>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-2">
                            {reportOptions.map((option) => (
                                <button
                                    key={option.value}
                                    onClick={() => setReportType(option.value)}
                                    className={`flex items-center gap-2 p-3 rounded-lg text-left transition-all ${reportType === option.value
                                        ? 'bg-blue-600 text-white shadow-lg'
                                        : 'bg-[#383838] text-gray-300 hover:bg-[#404040]'
                                        }`}
                                >
                                    {option.icon}
                                    <div>
                                        <div className="font-semibold text-sm">{option.label}</div>
                                        <div className="text-xs opacity-80">{option.description}</div>
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Filters */}
                    <div className="flex flex-wrap items-end gap-4">
                        {/* User Filter */}
                        <div>
                            <label className="block text-gray-300 text-sm font-medium mb-2">👥 Staff Member</label>
                            <select
                                value={selectedUser || ""}
                                onChange={(e) => setSelectedUser(e.target.value)}
                                className="bg-[#383838] text-white px-4 py-2 rounded-lg text-sm border border-[#444] focus:border-blue-500 focus:outline-none min-w-[200px]"
                            >
                                <option value="">All Staff Members</option>
                                {Array.isArray(usersData?.data?.data) && usersData.data.data.length > 0 &&
                                    usersData.data.data.map((user) => (
                                        <option key={user.id} value={user.id}>
                                            {user.firstName} {user.lastName}
                                        </option>
                                    ))}
                            </select>
                        </div>

                        {/* Date Range Toggle */}
                        <div>
                            <label className="block text-gray-300 text-sm font-medium mb-2">📅 Date Range</label>
                            <div className="flex items-center gap-2 bg-[#383838] px-4 py-2 rounded-lg border border-[#444]">
                                <input
                                    type="checkbox"
                                    checked={useCustomDate}
                                    onChange={(e) => setUseCustomDate(e.target.checked)}
                                    className="w-4 h-4 text-blue-600"
                                />
                                <span className="text-white text-sm">Custom Date Range</span>
                            </div>
                        </div>

                        {/* Date Selection */}
                        {useCustomDate ? (
                            <div className="flex items-end gap-2">
                                <div>
                                    <label className="block text-gray-300 text-sm font-medium mb-2">From</label>
                                    <input
                                        type="date"
                                        value={start_date}
                                        onChange={(e) => setStartDate(e.target.value)}
                                        className="bg-[#383838] text-white px-4 py-2 rounded-lg text-sm border border-[#444] focus:border-blue-500 focus:outline-none"
                                    />
                                </div>
                                <div className="text-gray-400 mb-2">to</div>
                                <div>
                                    <label className="block text-gray-300 text-sm font-medium mb-2">To</label>
                                    <input
                                        type="date"
                                        value={end_date}
                                        onChange={(e) => setEndDate(e.target.value)}
                                        className="bg-[#383838] text-white px-4 py-2 rounded-lg text-sm border border-[#444] focus:border-blue-500 focus:outline-none"
                                    />
                                </div>
                            </div>
                        ) : (
                            <div>
                                <label className="block text-gray-300 text-sm font-medium mb-2">Quick Date</label>
                                <select
                                    value={dateFilter}
                                    onChange={(e) => setDateFilter(e.target.value)}
                                    className="bg-[#383838] text-white px-4 py-2 rounded-lg text-sm border border-[#444] focus:border-blue-500 focus:outline-none"
                                >
                                    <option value="today">Today</option>
                                    <option value="yesterday">Yesterday</option>
                                    <option value="this_week">This Week</option>
                                    <option value="last_week">Last Week</option>
                                    <option value="this_month">This Month</option>
                                    <option value="last_month">Last Month</option>
                                </select>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Loading State */}
            {isLoading ? (
                <div className="flex-1 flex items-center justify-center">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
                        <div className="text-white text-lg font-semibold">Loading report data...</div>
                        <div className="text-gray-400 text-sm mt-2">Please wait while we gather your information</div>
                    </div>
                </div>
            ) : (
                <div className="flex-1 w-full overflow-y-auto px-6 py-6">
                    <div className="max-w-7xl w-full mx-auto space-y-6">
                        {/* Report Title */}
                        <div className="bg-[#232323] rounded-xl p-6 border border-[#333]">
                            <div className="flex items-center gap-3 mb-2">
                                {reportOptions.find(opt => opt.value === reportType)?.icon}
                                <h1 className="text-2xl font-bold text-white">
                                    {reportOptions.find(opt => opt.value === reportType)?.label}
                                </h1>
                            </div>
                            <p className="text-gray-400 flex items-center gap-2">
                                <IoCalendar className="w-4 h-4" />
                                {useCustomDate
                                    ? `From ${start_date} to ${end_date}`
                                    : `${dateFilter.replace('_', ' ').toUpperCase()}`
                                }
                            </p>
                        </div>

                        {/* Sales Summary Report */}
                        {reportType === "sales-summary" && salesSummary && (
                            <div className="bg-[#232323] rounded-xl p-6 border border-[#333]">
                                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                                    <MdDashboard className="w-6 h-6" />
                                    Sales Overview
                                </h2>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="bg-gradient-to-br from-green-600 to-green-700 p-6 rounded-xl text-white">
                                        <div className="flex items-center gap-3 mb-2">
                                            <div className="bg-white bg-opacity-20 p-2 rounded-lg">
                                                <IoStatsChart className="w-6 h-6" />
                                            </div>
                                            <div>
                                                <div className="text-green-100 text-sm font-medium">Total Sales</div>
                                                <div className="text-3xl font-bold">{formatCurrency(salesSummary.data.data.totalRevenue)}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="bg-gradient-to-br from-blue-600 to-blue-700 p-6 rounded-xl text-white">
                                        <div className="flex items-center gap-3 mb-2">
                                            <div className="bg-white bg-opacity-20 p-2 rounded-lg">
                                                <IoReceipt className="w-6 h-6" />
                                            </div>
                                            <div>
                                                <div className="text-blue-100 text-sm font-medium">Total Orders</div>
                                                <div className="text-3xl font-bold">{formatNumber(salesSummary.data.data.totalOrders)}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="bg-gradient-to-br from-purple-600 to-purple-700 p-6 rounded-xl text-white">
                                        <div className="flex items-center gap-3 mb-2">
                                            <div className="bg-white bg-opacity-20 p-2 rounded-lg">
                                                <MdTrendingUp className="w-6 h-6" />
                                            </div>
                                            <div>
                                                <div className="text-purple-100 text-sm font-medium">Average Order</div>
                                                <div className="text-3xl font-bold">{formatCurrency(salesSummary.data.data.avgOrderValue)}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Sales by Family Report */}
                        {reportType === "sales-by-family" && salesByFamily?.data?.data && salesByFamily.data?.data.length > 0 && (
                            <div className="bg-[#232323] rounded-xl p-6 border border-[#333]">
                                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                                    <MdCategory className="w-6 h-6" />
                                    Sales by Menu Categories
                                </h2>
                                <div className="space-y-4">
                                    {salesByFamily.data.data.map((family, index) => (
                                        <div key={family.familyId} className="bg-[#18181b] p-6 rounded-xl border border-[#333] hover:border-[#444] transition-colors">
                                            <div className="flex justify-between items-center">
                                                <div className="flex items-center gap-4">
                                                    <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                                                        {index + 1}
                                                    </div>
                                                    <div>
                                                        <div className="text-white font-semibold text-lg">{family.familyName}</div>
                                                        <div className="text-gray-400 text-sm">{formatNumber(family.totalQuantitySold)} items sold</div>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-white font-bold text-xl">{formatCurrency(family.totalRevenue)}</div>
                                                    <div className="text-gray-400 text-sm">Revenue</div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* User Performance Report */}
                        {reportType === "user-performance" && userPerformance?.data?.data && userPerformance.data.data.length > 0 && (
                            <div className="bg-[#232323] rounded-xl p-6 border border-[#333]">
                                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                                    <MdPerson className="w-6 h-6" />
                                    Staff Performance
                                </h2>
                                <div className="space-y-4">
                                    {userPerformance.data.data.map((performance, index) => (
                                        <div key={performance.userId} className="bg-[#18181b] p-6 rounded-xl border border-[#333] hover:border-[#444] transition-colors">
                                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                                                <div className="flex items-center gap-4">
                                                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                                                        {performance.firstName?.[0]}{performance.lastName?.[0]}
                                                    </div>
                                                    <div>
                                                        <div className="text-lg font-semibold text-white">{performance.firstName} {performance.lastName}</div>
                                                        <div className="text-xs text-gray-400">Username: {performance.username}</div>
                                                    </div>
                                                </div>
                                                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                                                    <div className="text-center">
                                                        <div className="text-gray-400 text-xs font-medium">Orders</div>
                                                        <div className="text-white text-xl font-bold">{formatNumber(performance.totalOrders)}</div>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="text-gray-400 text-xs font-medium">Items Sold</div>
                                                        <div className="text-white text-xl font-bold">{formatNumber(performance.totalItems)}</div>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="text-gray-400 text-xs font-medium">Revenue</div>
                                                        <div className="text-green-400 text-xl font-bold">{formatCurrency(performance.totalRevenue)}</div>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="text-gray-400 text-xs font-medium">Avg Order</div>
                                                        <div className="text-blue-400 text-xl font-bold">{formatCurrency(performance.avgOrderValue)}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Sales Hierarchy Report */}
                        {reportType === "sales-hierarchy" && salesHierarchy?.data?.data && salesHierarchy.data.data.length > 0 && (
                            <div className="bg-[#232323] rounded-xl p-6 border border-[#333]">
                                {/* Summary Card */}
                                {(() => {
                                    const summary = salesHierarchy.data.data[0]?.summary;
                                    if (!summary) return null;
                                    return (
                                        <div className="mb-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
                                            <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                                                <MdTrendingUp className="w-5 h-5" />
                                                Summary Overview
                                            </h3>
                                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                                                <div className="text-center">
                                                    <div className="text-blue-100 text-xs font-medium">Categories</div>
                                                    <div className="text-2xl font-bold">{formatNumber(summary.totalFamilies)}</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="text-blue-100 text-xs font-medium">Sub-Categories</div>
                                                    <div className="text-2xl font-bold">{formatNumber(summary.totalSubFamilies)}</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="text-blue-100 text-xs font-medium">Menu Items</div>
                                                    <div className="text-2xl font-bold">{formatNumber(summary.totalArticles)}</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="text-blue-100 text-xs font-medium">Items Sold</div>
                                                    <div className="text-2xl font-bold">{formatNumber(summary.totalQuantitySold)}</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="text-blue-100 text-xs font-medium">Orders</div>
                                                    <div className="text-2xl font-bold">{formatNumber(summary.totalOrders)}</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="text-blue-100 text-xs font-medium">Revenue</div>
                                                    <div className="text-2xl font-bold">{formatCurrency(summary.totalRevenue)}</div>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })()}

                                {/* Expand/Collapse All Button */}
                                <div className="flex items-center justify-between mb-6">
                                    <h2 className="text-xl font-bold text-white flex items-center gap-2">
                                        <MdTrendingUp className="w-6 h-6" />
                                        Detailed Sales Breakdown
                                    </h2>
                                    <button
                                        onClick={handleExpandAll}
                                        className={`px-6 py-3 rounded-lg font-semibold transition-all text-sm flex items-center gap-2 ${expandAll
                                            ? 'bg-blue-600 text-white shadow-lg'
                                            : 'bg-[#383838] text-gray-300 hover:bg-[#404040] border border-[#444]'
                                            }`}
                                    >
                                        {expandAll ? 'Collapse All' : 'Expand All'}
                                    </button>
                                </div>

                                {(() => {
                                    const hierarchy = salesHierarchy.data.data[0]?.hierarchy || {};
                                    return Object.values(hierarchy).map(({ family, subFamilies }) => (
                                        <div key={family.id} className="mb-6 border border-[#333] rounded-xl overflow-hidden">
                                            <button
                                                className="w-full text-left p-4 bg-[#18181b] hover:bg-[#232323] focus:outline-none transition-colors"
                                                onClick={() => toggleFamily(family.id)}
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center gap-4">
                                                        <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
                                                            {family.name?.[0]}
                                                        </div>
                                                        <div>
                                                            <div className="font-semibold text-lg text-white">{family.name}</div>
                                                            <div className="text-sm text-gray-400">Category</div>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-4">
                                                        <div className="text-right">
                                                            <div className="text-white font-bold">{formatCurrency(family.totalRevenue)}</div>
                                                            <div className="text-xs text-gray-400">{formatNumber(family.totalQuantitySold)} items</div>
                                                        </div>
                                                        <div className="text-gray-400 text-xl">
                                                            {expandedFamilies[family.id] ? '▼' : '▶'}
                                                        </div>
                                                    </div>
                                                </div>
                                            </button>
                                            {expandedFamilies[family.id] && (
                                                <div className="bg-[#232323] border-t border-[#333]">
                                                    {Object.values(subFamilies).map((subFamily) => (
                                                        <div key={subFamily.id} className="border-b border-[#444] last:border-b-0">
                                                            <button
                                                                className="w-full text-left p-4 hover:bg-[#292929] focus:outline-none transition-colors"
                                                                onClick={() => toggleSubFamily(subFamily.id)}
                                                            >
                                                                <div className="flex items-center justify-between">
                                                                    <div className="flex items-center gap-3">
                                                                        <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                                                            {subFamily.name?.[0]}
                                                                        </div>
                                                                        <div>
                                                                            <div className="font-semibold text-white">{subFamily.name}</div>
                                                                            <div className="text-xs text-gray-400">Sub-Category</div>
                                                                        </div>
                                                                    </div>
                                                                    <div className="flex items-center gap-4">
                                                                        <div className="text-right">
                                                                            <div className="text-white font-bold">{formatCurrency(subFamily.totalRevenue)}</div>
                                                                            <div className="text-xs text-gray-400">{formatNumber(subFamily.totalQuantitySold)} items</div>
                                                                        </div>
                                                                        <div className="text-gray-400">
                                                                            {expandedSubFamilies[subFamily.id] ? '▼' : '▶'}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </button>
                                                            {expandedSubFamilies[subFamily.id] && (
                                                                <div className="bg-[#292929] border-t border-[#444]">
                                                                    {subFamily.articles.map((article) => (
                                                                        <div key={article.id} className="flex items-center justify-between p-4 border-b border-[#555] last:border-b-0">
                                                                            <div className="flex items-center gap-3">
                                                                                <div className="w-6 h-6 bg-purple-600 rounded flex items-center justify-center text-white text-xs font-bold">
                                                                                    {article.name?.[0]}
                                                                                </div>
                                                                                <div>
                                                                                    <div className="text-white font-medium">{article.name}</div>
                                                                                    <div className="text-xs text-gray-400">Menu Item</div>
                                                                                </div>
                                                                            </div>
                                                                            <div className="text-right">
                                                                                <div className="text-white font-bold">{formatCurrency(article.totalRevenue)}</div>
                                                                                <div className="text-xs text-gray-400">{formatNumber(article.totalQuantitySold)} sold</div>
                                                                            </div>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    ));
                                })()}
                            </div>
                        )}

                        {/* Closed Orders Report */}
                        {reportType === "closed-orders" && (
                            <div className="bg-[#232323] rounded-xl p-6 border border-[#333]">
                                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                                    <MdReceipt className="w-6 h-6" />
                                    Today's Completed Orders
                                </h2>
                                {closedOrdersLoading ? (
                                    <div className="text-center py-8">
                                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                                        <div className="text-gray-400">Loading orders...</div>
                                    </div>
                                ) : closedOrders && closedOrders.length > 0 ? (
                                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                        {closedOrders.map((order) => (
                                            <div
                                                key={order.id}
                                                className="bg-[#18181b] rounded-xl p-5 border border-[#333] hover:border-[#444] transition-all cursor-pointer hover:shadow-lg group"
                                                onClick={() => handlePrintOrder(order)}
                                            >
                                                <div className="flex items-center justify-between mb-3">
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                                            {order.table?.name?.[0] || '#'}
                                                        </div>
                                                        <span className="text-white font-semibold text-lg">Order #{order.table?.name}</span>
                                                    </div>
                                                    <div className="text-xs text-gray-400">
                                                        {order.createdAt ? new Date(order.createdAt).toLocaleTimeString() : ""}
                                                    </div>
                                                </div>
                                                <div className="space-y-2">
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-xs text-gray-400">Check #:</span>
                                                        <span className="text-white font-medium">{order.table?.code || "-"}</span>
                                                    </div>
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-xs text-gray-400">Total:</span>
                                                        <span className="text-green-400 font-bold text-lg">{formatCurrency(order.grandTotal)}</span>
                                                    </div>
                                                </div>
                                                <div className="mt-4 pt-3 border-t border-[#333] flex items-center justify-center">
                                                    <button className="flex items-center gap-2 text-blue-400 hover:text-blue-300 text-sm font-medium group-hover:scale-105 transition-transform">
                                                        <IoPrint className="w-4 h-4" />
                                                        Print Receipt
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-12">
                                        <div className="text-6xl mb-4">📋</div>
                                        <div className="text-gray-400 text-lg font-semibold">No completed orders for today</div>
                                        <div className="text-gray-500 text-sm mt-2">Orders will appear here once they are completed</div>
                                    </div>
                                )}
                            </div>
                        )}

                        {/* No Data Message */}
                        {!isLoading && (
                            (reportType === "sales-summary" && !salesSummary) ||
                            (reportType === "sales-by-family" && (!salesByFamily?.data || salesByFamily.data.length === 0)) ||
                            (reportType === "user-performance" && (!userPerformance?.data || userPerformance.data.length === 0)) ||
                            (reportType === "sales-hierarchy" && (!salesHierarchy?.data || salesHierarchy.data.length === 0)) ||
                            (reportType === "closed-orders" && (!closedOrders || closedOrders.length === 0))
                        ) && (
                                <div className="bg-[#232323] rounded-xl p-12 text-center border border-[#333]">
                                    <div className="text-6xl mb-4">📊</div>
                                    <div className="text-gray-400 text-xl font-semibold mb-2">No data available</div>
                                    <div className="text-gray-500">Try adjusting your filters or date range</div>
                                </div>
                            )}
                    </div>
                </div>
            )}
        </section>
    );
};

export default ChecksReport; 