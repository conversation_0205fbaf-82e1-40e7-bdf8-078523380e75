import React, { useState, useEffect } from "react";
import POSBottomNav from "../components/shared/POSBottomNav";
import BackButton from "../components/shared/BackButton";
import TableCard from "../components/tables/TableCard";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getTables } from "../https";
import { enqueueSnackbar } from "notistack";

const Tables = () => {
  const [status, setStatus] = useState("opened");

  useEffect(() => {
    document.title = "POS | Tables"
  }, [])

  const { data: resData, isError } = useQuery({
    queryKey: ["tables", status],
    queryFn: async () => {
      const params = {
        paginate: false
      };

      if (status === "opened") {
        params.status = "Opened";
      } else if (status === "closed") {
        params.status = "Closed";
        params.date_range_preset = "today";
      }

      return await getTables(params);
    },
    placeholderData: keepPreviousData,
  });

  const tableCount = resData?.data?.data?.length || 0;

  if (isError) {
    enqueueSnackbar("Something went wrong!", { variant: "error" })
  }

  return (
    <section className="bg-[#1f1f1f] h-[calc(100vh-5rem)] flex flex-col">
      <div className="flex items-center justify-between px-4 sm:px-8 py-4 w-full">
        <div className="flex items-center gap-4">
          <BackButton />
          <h1 className="text-[#f5f5f5] text-2xl font-bold tracking-wider">
            Tables
          </h1>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={() => setStatus("opened")}
            className={`text-[#ababab] text-lg ${status === "opened" && "bg-[#383838] rounded-lg px-5 py-2"}  rounded-lg px-5 py-2 font-semibold`}
          >
            Opened
          </button>
          <button
            onClick={() => setStatus("closed")}
            className={`text-[#ababab] text-lg ${status === "closed" && "bg-[#383838] rounded-lg px-5 py-2"}  rounded-lg px-5 py-2 font-semibold`}
          >
            Closed
          </button>
          <div className="text-[#ababab] text-lg ml-4">Tables: {tableCount}</div>
        </div>
      </div>

      <div className="flex-1 w-full flex flex-col items-center">
        <div className="w-full flex-1 overflow-y-auto py-4 px-2 sm:px-4 bg-[#1f1f1f]">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4 bg-[#1f1f1f]">
            {resData?.data?.data?.map((table) => (
              <TableCard key={table.id} table={table} />
            ))}
          </div>
        </div>
      </div>

      <POSBottomNav />
    </section>
  );
};

export default Tables;
