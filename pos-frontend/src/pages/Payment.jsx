import React, { useEffect, useState } from 'react';
import CartInfo from '../components/menu/CartInfo';
import { axiosWrapper } from '../https/axiosWrapper';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import POSBottomNav from '../components/shared/POSBottomNav';
import { exitEditMode, clearCart } from '../redux/slices/cartSlice';
import { clearTable, setTips, clearTips } from '../redux/slices/tableSlice';
import { enqueueSnackbar } from 'notistack';
import Modal from '../components/shared/Modal';
import useInvalidateAndNavigate from '../hooks/useInvalidateAndNavigate';


const Payment = () => {
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [loading, setLoading] = useState(true);
    const cart = useSelector((state) => state.cart);
    const table = useSelector((state) => state.table.table);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const location = useLocation();
    const navState = location.state || {};
    const isEditMode = navState.isEditMode || false;
    const voidEnabled = navState.voidEnabled || false;
    const voidQuantity = navState.voidQuantity || 0;
    const cartIsEmpty = navState.cartIsEmpty || false;
    const [showCashModal, setShowCashModal] = useState(false);
    const [cashAmount, setCashAmount] = useState('');
    const [showTipsModal, setShowTipsModal] = useState(false);
    const [tipsAmount, setTipsAmount] = useState('');
    const [customerName, setCustomerName] = useState('');
    const orderId = useSelector((state) => state.table.orderId);
    const tips = useSelector((state) => state.table.tips);
    const [quantity, setQuantity] = useState(1);
    const { defaultCurrency } = useSelector((state) => state.general);


    // New state for multiple payments
    const [currentAmount, setCurrentAmount] = useState('');
    const [payments, setPayments] = useState([]);
    const [showBalanceModal, setShowBalanceModal] = useState(false);
    const [balanceAmount, setBalanceAmount] = useState(0);
    const [isProcessingPayment, setIsProcessingPayment] = useState(false);
    const [pendingPaymentMethod, setPendingPaymentMethod] = useState(null);
    const [cashAmountEntered, setCashAmountEntered] = useState('');

    const [invalidateAndGoToOrders, loadingGoToOrders] = useInvalidateAndNavigate(['orders-page'], '/orders');

    // New: State for fetched order data
    const [orderData, setOrderData] = useState(null);
    const [orderLoading, setOrderLoading] = useState(false);
    const [orderError, setOrderError] = useState(null);

    // Fetch order data if in edit mode
    useEffect(() => {
        if (isEditMode && orderId) {
            setOrderLoading(true);
            axiosWrapper.get(`/api/orders/${orderId}/`)
                .then(res => {
                    setOrderData(res.data.data || null);
                    setOrderError(null);
                })
                .catch(err => {
                    setOrderError('Failed to fetch order data');
                    setOrderData(null);
                })
                .finally(() => setOrderLoading(false));
        }
    }, [isEditMode, orderId]);

    // Fill the cart with order items from API in edit mode
    useEffect(() => {
        if (isEditMode && orderData && Array.isArray(orderData.orderItems)) {
            // Convert orderItems to cart item format
            const cartItems = orderData.orderItems.map(item => ({
                orderItemId: item.id,
                article: {
                    id: item.article?.id,
                    name: item.article?.name,
                    code: item.article?.code,
                    price: parseFloat(item.price) || 0
                },
                quantity: item.quantity
            }));
            dispatch({ type: 'cart/replaceItems', payload: cartItems });
        }
    }, [isEditMode, orderData, dispatch]);

    // Calculate total paid amount and group payments by method
    const totalPaid = payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
    // Use orderData for total if in edit mode, else cart
    const paymentTotal = isEditMode && orderData ? Number(orderData.totalAmount) : cart.total;
    const remainingAmount = paymentTotal - totalPaid;

    // Group payments by payment method
    const groupedPayments = payments.reduce((groups, payment) => {
        const methodId = payment.paymentMethod;
        if (!groups[methodId]) {
            groups[methodId] = {
                methodId,
                methodName: paymentMethods.find(m => m.id === methodId)?.name || 'Unknown',
                totalAmount: 0,
                payments: []
            };
        }
        groups[methodId].totalAmount += Number(payment.amount);
        groups[methodId].payments.push(payment);
        return groups;
    }, {});

    useEffect(() => {
        setLoading(true);
        axiosWrapper.get('/api/payment-methods/?is_active=true')
            .then(res => {
                setPaymentMethods(res.data.data || []);
            })
            .catch(() => setPaymentMethods([]))
            .finally(() => setLoading(false));
    }, []);



    const handleNumberInput = (num) => {
        const numStr = num.toString();

        // Handle decimal point
        if (num === ".") {
            if (currentAmount.includes(".")) return;
            setCurrentAmount(currentAmount === "" ? "0." : currentAmount + ".");
            return;
        }

        // Prevent leading zeros
        if (currentAmount === "" && (num === 0 || num === "00")) return;
        if (currentAmount === "0" && (num === 0 || num === "00")) return;

        // Limit to 2 decimal places
        if (currentAmount.includes(".")) {
            const decimalPart = currentAmount.split(".")[1];
            if (decimalPart && decimalPart.length >= 2) return;
        }

        setCurrentAmount(prev => prev + numStr);
    };

    // Clear current amount
    const handleClearQuantity = () => {
        setCurrentAmount('');
    };

    // Handle payment method selection
    const handlePaymentMethod = async (method) => {
        if (!orderId) {
            enqueueSnackbar("No order selected.", { variant: "error" });
            return;
        }
        if (orderLoading || (isEditMode && !orderData)) {
            enqueueSnackbar("Order data not loaded yet.", { variant: "error" });
            return;
        }
        // Use cash amount if entered, otherwise use current amount or remaining amount
        let paymentAmount;
        if (cashAmountEntered) {
            paymentAmount = Number(cashAmountEntered);
        } else {
            paymentAmount = currentAmount ? Number(currentAmount) : remainingAmount;
        }
        if (paymentAmount <= 0) {
            enqueueSnackbar("Invalid payment amount.", { variant: "error" });
            return;
        }
        // Check if payment amount exceeds remaining amount
        if (paymentAmount > remainingAmount) {
            setBalanceAmount(paymentAmount - remainingAmount);
            setPendingPaymentMethod(method);
            setShowBalanceModal(true);
            return;
        }
        // Add payment to the list
        const newPayment = {
            amount: paymentAmount.toString(),
            paymentMethod: method.id
        };
        const updatedPayments = [...payments, newPayment];
        setPayments(updatedPayments);
        setCurrentAmount('');
        setCashAmountEntered(''); // Clear cash amount after use
        // If this completes the payment (total paid >= paymentTotal)
        if (totalPaid + paymentAmount >= paymentTotal) {
            await processPayment(updatedPayments);
        }
    };

    // Process payment to API
    const processPayment = async (finalPayments, remainingBalance = undefined) => {
        if (!orderId) {
            enqueueSnackbar("No order selected.", { variant: "error" });
            return;
        }
        if (orderLoading || (isEditMode && !orderData)) {
            enqueueSnackbar("Order data not loaded yet.", { variant: "error" });
            return;
        }
        setIsProcessingPayment(true);
        try {
            const payload = {
                payments: finalPayments,
                remainingBalance: remainingBalance !== undefined ? remainingBalance : 0
            };

            // Add tips to payload if tips exist
            if (tips) {
                payload.tips = {
                    customer: tips.customer || '',
                    amount: tips.amount
                };
            }

            await axiosWrapper.post(`/api/orders/${orderId}/payments/`, payload);
            enqueueSnackbar("Payment processed successfully!", { variant: "success" });
            // Clear payments and tips, then navigate to orders
            setPayments([]);
            setCurrentAmount('');
            dispatch(clearTips());
            await invalidateAndGoToOrders();
        } catch (error) {
            // Show more specific error message
            const errorMessage = error.response?.data?.message || error.response?.data?.detail || "Failed to process payment.";
            enqueueSnackbar(errorMessage, { variant: "error" });
        } finally {
            setIsProcessingPayment(false);
        }
    };

    // Handle balance modal confirmation
    const handleBalanceConfirm = async () => {
        setShowBalanceModal(false);

        // Use the remaining amount for the last payment with the correct payment method
        const lastPayment = {
            amount: remainingAmount.toString(),
            paymentMethod: pendingPaymentMethod.id
        };

        const finalPayments = [...payments, lastPayment];
        setPendingPaymentMethod(null);
        setCashAmountEntered(''); // Clear cash amount after use
        await processPayment(finalPayments, balanceAmount);
    };

    // Handle balance modal cancel
    const handleBalanceCancel = () => {
        setShowBalanceModal(false);
        setPendingPaymentMethod(null);
        setCurrentAmount('');
        setCashAmountEntered(''); // Clear cash amount on cancel
    };

    const handleVoid = () => {
        // Implement void logic or show a modal as needed
        enqueueSnackbar("Void functionality coming soon!", { variant: "info" });
    };
    const handleTransCkl = () => {
        dispatch(exitEditMode());
        dispatch(clearTable());
        dispatch(clearCart());
        navigate("/orders");
    };
    const handlePrint = () => {
        enqueueSnackbar("Print functionality coming soon!", { variant: "info" });
    };
    const handleSendOrder = () => {
        enqueueSnackbar("Send order functionality coming soon!", { variant: "info" });
    };
    const handleFunctionChecks = () => {
        enqueueSnackbar("Function checks coming soon!", { variant: "info" });
    };
    const handleClear = () => {
        setPayments([]);
        setCurrentAmount('');
    };

    // Handler for Pay 0 from API
    const handlePayZero = async () => {
        if (!orderId) {
            enqueueSnackbar("No order selected.", { variant: "error" });
            return;
        }

        // Find the Pay 0 payment method
        const payZeroMethod = paymentMethods.find(m => m.name?.toLowerCase() === 'pay 0');
        if (!payZeroMethod) {
            enqueueSnackbar("Pay 0 payment method not found.", { variant: "error" });
            return;
        }

        try {
            await axiosWrapper.patch(`/api/orders/${orderId}/`, {
                paymentId: payZeroMethod.id
            });
            enqueueSnackbar("done.", { variant: "success" });
            await invalidateAndGoToOrders();
        } catch (e) {
            enqueueSnackbar("Failed to void order.", { variant: "error" });
        }
    };

    return (
        <div className="flex h-[calc(100vh-5rem)] bg-gray-100 dark:bg-gray-900 p-4 gap-4 relative transition-colors">

            {/* Payment Section (left) */}
            <div className="flex-[2.5] flex flex-col bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 relative border border-gray-200 dark:border-gray-700 transition-colors">

                {/* Number Pad */}
                <div className="flex gap-3 justify-center py-4 bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-600 mb-4 transition-colors">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 0, "00", "."].map(num => (
                        <button
                            key={num}
                            className={`w-16 h-14 rounded-lg font-bold text-2xl transition-all flex items-center justify-center ${quantity === num ? "bg-blue-600 text-white" : "bg-gray-300 dark:bg-gray-600 text-gray-900 dark:text-gray-100 hover:bg-gray-400 dark:hover:bg-gray-500"}`}
                            onClick={() => handleNumberInput(num)}
                        >
                            {num}
                        </button>
                    ))}
                    <button
                        className="w-16 h-14 rounded-lg font-bold text-xl transition-all flex items-center justify-center bg-white hover:bg-gray-100 text-black border border-gray-300"
                        onClick={handleClearQuantity}>X
                    </button>
                </div>

                {/* Other content (if any) */}
                <div className="flex-1 flex flex-col justify-end">
                    {/* Payment Methods Grid with variable sizes for UX */}
                    <div className="grid grid-cols-4 grid-rows-3 gap-4 mb-4 min-h-[520px]">
                        {/* CASH AMOUNT button (always first) */}
                        <button
                            className="col-span-1 row-span-2 py-20 rounded-lg text-3xl font-bold text-white shadow-lg transition bg-yellow-700 hover:bg-yellow-600 flex items-center justify-center"
                            style={{ gridColumn: '1', gridRow: '2 / span 2' }}
                            onClick={() => setShowCashModal(true)}
                        >
                            CASH AMOUNT
                        </button>
                        {/* TIPS button (static) */}
                        <button
                            className="col-span-1 row-span-1 py-10 rounded-lg text-2xl font-bold text-white shadow-lg transition bg-purple-600 hover:bg-purple-500 flex items-center justify-center"
                            style={{ gridColumn: '4', gridRow: '1' }}
                            onClick={() => setShowTipsModal(true)}
                        >
                            TIPS
                        </button>
                        {/* Render payment methods from API except CASH AMOUNT */}
                        {paymentMethods.filter(m => m.name?.toLowerCase() !== 'cash amount').map((method, idx) => {
                            const name = method.name?.toLowerCase();
                            let className = "py-20 rounded-lg text-3xl font-bold text-white shadow-lg transition flex items-center justify-center";
                            let style = { backgroundColor: method.generatedColor || '#2563eb' };
                            if (name === 'cash') {
                                className += " col-span-2 row-span-2";
                                style = { ...style, gridColumn: '2 / span 2', gridRow: '2 / span 2' };
                            } else if (name === 'room charge') {
                                className += " col-span-1 row-span-2 text-black";
                                style = { ...style, gridColumn: '4', gridRow: '2 / span 2', backgroundColor: method.generatedColor || '#a7f3d0' };
                            } else if (name === 'pay') {
                                className += " col-span-2 row-span-1";
                                style = { ...style, gridColumn: 'span 2', gridRow: 'span 1' };
                            } else {
                                className += " col-span-1 row-span-1";
                            }
                            // Special handler for PAY 0
                            const onClick = name === 'pay 0' ? handlePayZero : () => handlePaymentMethod(method);
                            return (
                                <button
                                    key={method.id}
                                    className={className}
                                    style={style}
                                    onClick={onClick}
                                    disabled={(loadingGoToOrders && name === 'pay 0') || isProcessingPayment}
                                >
                                    {method.name}
                                </button>
                            );
                        })}
                    </div>
                </div>

                {/* Cash Amount Modal */}
                <Modal isOpen={showCashModal} onClose={() => setShowCashModal(false)} title="Enter Cash Amount">
                    <div className="flex flex-col gap-4">
                        <input
                            type="number"
                            className="p-6 rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 text-3xl w-full text-center transition-colors"
                            placeholder="Enter amount"
                            value={cashAmount}
                            onChange={e => setCashAmount(e.target.value)}
                            autoFocus
                        />
                        <div className="text-center text-gray-300 text-sm">
                            Order Total: {defaultCurrency}{paymentTotal}
                        </div>
                        <div className="flex gap-4 justify-end">
                            <button
                                className="bg-gray-500 text-white px-6 py-2 rounded-lg font-bold"
                                onClick={() => setShowCashModal(false)}
                            >Cancel</button>
                            <button
                                className="bg-green-600 text-white px-6 py-2 rounded-lg font-bold"
                                onClick={() => {
                                    if (Number(cashAmount) >= paymentTotal) {
                                        setCashAmountEntered(cashAmount);
                                        setShowCashModal(false);
                                        enqueueSnackbar("Cash amount set. Click a payment method to process.", { variant: "info" });
                                    } else {
                                        enqueueSnackbar("Cash amount must be >= order total.", { variant: "error" });
                                    }
                                }}
                                disabled={!cashAmount || isNaN(Number(cashAmount)) || Number(cashAmount) <= 0}
                            >Confirm</button>
                        </div>
                    </div>
                </Modal>

                {/* Balance Modal */}
                <Modal isOpen={showBalanceModal} onClose={handleBalanceCancel} title="Payment">
                    <div className="flex flex-col items-center justify-center p-12 min-w-[400px] max-w-[600px]">

                        <div className="text-gray-300 text-5xl font-extrabold mb-6">
                            Change
                        </div>

                        <div className="text-white text-6xl font-extrabold mb-4">
                            {defaultCurrency}{balanceAmount.toFixed(2)}
                        </div>

                        <div className="flex gap-6 justify-center mt-6">
                            <button
                                className="bg-green-600 text-white px-10 py-4 rounded-xl font-bold text-2xl"
                                onClick={handleBalanceConfirm}
                            >
                                OK
                            </button>
                        </div>


                    </div>
                </Modal>

                {/* Tips Modal */}
                <Modal isOpen={showTipsModal} onClose={() => setShowTipsModal(false)} title="Add Tips">
                    <div className="flex flex-col gap-4">

                        <div>
                            <label className="block text-gray-300 text-sm font-bold mb-2">
                                Tips Amount
                            </label>
                            <input
                                type="number"
                                className="p-4 rounded bg-[#232323] text-white border border-[#444] text-lg w-full text-center"
                                placeholder="Enter tip amount"
                                value={tipsAmount}
                                onChange={e => setTipsAmount(e.target.value)}
                                autoFocus
                            />
                        </div>

                        <div>
                            <label className="block text-gray-300 text-sm font-bold mb-2">
                                Customer Name
                            </label>
                            <input
                                type="text"
                                className="p-4 rounded bg-[#232323] text-white border border-[#444] text-lg w-full"
                                placeholder="Enter customer name (optional)"
                                value={customerName}
                                onChange={e => setCustomerName(e.target.value)}

                            />
                        </div>
                        <div className="flex gap-4 justify-end">
                            <button
                                className="bg-gray-500 text-white px-6 py-2 rounded-lg font-bold"
                                onClick={() => {
                                    setShowTipsModal(false);
                                    setTipsAmount('');
                                    setCustomerName('');
                                }}
                            >
                                Cancel
                            </button>
                            <button
                                className={`px-6 py-2 rounded-lg font-bold transition-colors ${tipsAmount && !isNaN(Number(tipsAmount)) && Number(tipsAmount) > 0
                                    ? 'bg-green-600 text-white hover:bg-green-700'
                                    : 'bg-gray-500 text-gray-300 cursor-not-allowed'
                                    }`}
                                onClick={() => {
                                    if (tipsAmount && !isNaN(Number(tipsAmount)) && Number(tipsAmount) > 0) {
                                        // Save tips to Redux store
                                        dispatch(setTips({
                                            customer: customerName || '',
                                            amount: tipsAmount
                                        }));
                                        enqueueSnackbar("Tips added successfully!", { variant: "success" });
                                        setShowTipsModal(false);
                                        setTipsAmount('');
                                        setCustomerName('');
                                    } else {
                                        enqueueSnackbar("Please enter a valid tips amount.", { variant: "error" });
                                    }
                                }}
                                disabled={!tipsAmount || isNaN(Number(tipsAmount)) || Number(tipsAmount) <= 0}
                            >
                                Add Tips
                            </button>
                        </div>
                    </div>
                </Modal>

            </div>

            {/* Cart Info (right) */}
            <div className="flex-[1.2] bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden flex flex-col border border-gray-200 dark:border-gray-700 transition-colors">
                <CartInfo isEditMode={true} />
                {/* Payment Summary below cart total price */}
                {payments.length > 0 && (
                    <div className="mb-4 p-4 bg-[#2d2d2d] rounded-lg mx-4">
                        <div className="flex items-center justify-between mb-3">
                            <h4 className="text-gray-300 text-sm font-bold">Payment Summary</h4>
                            <button
                                onClick={handleClear}
                                className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-bold transition-colors"
                            >
                                Clear Payments
                            </button>
                        </div>
                        <div className="text-gray-300 text-sm mb-2">
                            Paid: {defaultCurrency}{totalPaid}
                        </div>
                        <div className="text-gray-300 text-sm mb-2">
                            Remaining: {defaultCurrency}{remainingAmount}
                        </div>
                        {currentAmount && (
                            <div className="text-blue-400 text-sm mb-2">
                                Current: {defaultCurrency}{currentAmount}
                            </div>
                        )}
                        {Object.keys(groupedPayments).length > 0 && (
                            <div className="mt-2">
                                <div className="text-gray-300 text-xs mb-1">Payments:</div>
                                {Object.values(groupedPayments).map((group, index) => (
                                    <div key={index} className="text-xs text-gray-400">
                                        {group.methodName}: {defaultCurrency}{group.totalAmount}
                                    </div>
                                ))}
                            </div>
                        )}
                        {tips && (
                            <div className="mt-2">
                                <div className="text-gray-300 text-xs mb-1">Tips:</div>
                                <div className="text-xs text-purple-400">
                                    {tips.customer ? `${tips.customer}: ` : ''}{defaultCurrency}{tips.amount}
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* POS Bottom Nav - floating at the bottom */}
            <div className="fixed bottom-0 left-0 right-0 z-50">
                <POSBottomNav
                    onBack={() => navigate('/menu')}
                    onVoid={handleVoid}
                    onTransCkl={handleTransCkl}
                    onPrint={handlePrint}
                    onSendOrder={handleSendOrder}
                    onPay={() => { }}
                    onFunctionChecks={handleFunctionChecks}
                    onClear={handleClear}
                    isEditMode={isEditMode}
                    voidEnabled={voidEnabled}
                    voidQuantity={voidQuantity}
                    cartIsEmpty={cartIsEmpty}
                    payLabelAsBack={false}
                    payButtonDisabled={true}
                    voidButtonDisabled={true}
                    printButtonDisabled={true}
                    sendOrderButtonDisabled={true}
                    functionChecksButtonDisabled={true}
                />
            </div>
        </div>
    );
};

export default Payment; 