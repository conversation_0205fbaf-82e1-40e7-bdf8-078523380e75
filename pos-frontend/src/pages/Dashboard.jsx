import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { IoStatsChart, IoArrowForward } from "react-icons/io5";

const Dashboard = () => {
  const navigate = useNavigate();

  useEffect(() => {
    document.title = "POS | Dashboard - Redirecting to Reports";
    // Automatically redirect to reports page after a short delay
    const timer = setTimeout(() => {
      navigate('/reports');
    }, 2000);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <div className="bg-gray-100 dark:bg-gray-900 h-[calc(100vh-5rem)] flex items-center justify-center transition-colors">
      <div className="text-center space-y-6">
        <div className="flex justify-center">
          <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center">
            <IoStatsChart className="text-4xl text-white" />
          </div>
        </div>

        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Dashboard Upgraded!</h1>
          <p className="text-gray-600 dark:text-gray-400 text-lg">
            We've moved to a comprehensive reports dashboard with advanced analytics
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
          <p className="text-gray-600 dark:text-gray-400">Redirecting to Reports Dashboard...</p>
        </div>

        <button
          onClick={() => navigate('/reports')}
          className="flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors mx-auto"
        >
          Go to Reports Now
          <IoArrowForward className="text-lg" />
        </button>

        <div className="text-xs text-gray-600 dark:text-gray-400 max-w-md">
          <p>
            The new reports dashboard includes comprehensive analytics, real-time charts,
            advanced filtering by revenue center, workstation, and user, plus detailed
            performance metrics.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
