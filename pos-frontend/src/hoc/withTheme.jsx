/**
 * ===================================================================
 * AUTOMATIC THEME INHERITANCE HOC
 * ===================================================================
 * 
 * Higher-Order Component that automatically injects theme utilities
 * into any component, enabling automatic dark mode support.
 */

import React from 'react';
import { useTheme } from '../hooks/useTheme';

/**
 * Higher-Order Component that provides theme utilities to any component
 * @param {React.Component} WrappedComponent - Component to enhance with theme
 * @param {Object} options - Configuration options
 * @returns {React.Component} Enhanced component with theme utilities
 */
export const withTheme = (WrappedComponent, options = {}) => {
  const {
    // Whether to automatically apply theme classes to the root element
    autoApplyTheme = false,
    // Default theme classes to apply
    defaultClasses = '',
    // Whether to pass theme utilities as props
    injectThemeProps = true
  } = options;

  const ThemedComponent = React.forwardRef((props, ref) => {
    const theme = useTheme();
    
    // Prepare theme props to inject
    const themeProps = injectThemeProps ? {
      theme,
      // Convenience shortcuts
      isDark: theme.isDark,
      isLight: theme.isLight,
      adaptive: theme.adaptive,
      cn: theme.cn,
      getComponentClasses: theme.getComponentClasses,
      getStatusClasses: theme.getStatusClasses
    } : {};

    // If auto-applying theme, wrap component with themed container
    if (autoApplyTheme) {
      return (
        <div className={theme.cn(theme.adaptive('card'), defaultClasses)}>
          <WrappedComponent 
            ref={ref}
            {...props} 
            {...themeProps}
          />
        </div>
      );
    }

    // Otherwise, just pass theme props
    return (
      <WrappedComponent 
        ref={ref}
        {...props} 
        {...themeProps}
      />
    );
  });

  // Set display name for debugging
  ThemedComponent.displayName = `withTheme(${WrappedComponent.displayName || WrappedComponent.name})`;

  return ThemedComponent;
};

/**
 * HOC specifically for page components
 * Automatically applies page-level theming
 */
export const withPageTheme = (WrappedComponent) => {
  return withTheme(WrappedComponent, {
    autoApplyTheme: true,
    defaultClasses: 'min-h-screen bg-gray-100 dark:bg-gray-900 transition-colors'
  });
};

/**
 * HOC specifically for card components
 * Automatically applies card theming
 */
export const withCardTheme = (WrappedComponent) => {
  return withTheme(WrappedComponent, {
    autoApplyTheme: true,
    defaultClasses: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-6'
  });
};

/**
 * HOC specifically for modal components
 * Automatically applies modal theming
 */
export const withModalTheme = (WrappedComponent) => {
  return withTheme(WrappedComponent, {
    autoApplyTheme: true,
    defaultClasses: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg'
  });
};

/**
 * Decorator function for class components
 * Usage: @withThemeDecorator
 */
export const withThemeDecorator = (options = {}) => {
  return (WrappedComponent) => withTheme(WrappedComponent, options);
};

/**
 * Hook-based theme injection for functional components
 * Alternative to HOC pattern
 */
export const useThemedProps = (baseProps = {}) => {
  const theme = useTheme();
  
  return {
    ...baseProps,
    theme,
    isDark: theme.isDark,
    isLight: theme.isLight,
    adaptive: theme.adaptive,
    cn: theme.cn,
    getComponentClasses: theme.getComponentClasses,
    getStatusClasses: theme.getStatusClasses
  };
};

/**
 * Utility to create themed variants of existing components
 */
export const createThemedVariant = (BaseComponent, themeConfig = {}) => {
  const {
    baseClasses = '',
    variants = {},
    defaultVariant = 'default'
  } = themeConfig;

  return React.forwardRef(({ variant = defaultVariant, className = '', ...props }, ref) => {
    const { cn, adaptive } = useTheme();
    
    const variantClasses = variants[variant] || '';
    const combinedClasses = cn(
      baseClasses,
      variantClasses,
      className
    );

    return (
      <BaseComponent 
        ref={ref}
        className={combinedClasses}
        {...props}
      />
    );
  });
};

/**
 * Factory function to create themed components
 */
export const createThemedComponent = (element, defaultClasses = '') => {
  return React.forwardRef(({ className = '', ...props }, ref) => {
    const { cn, adaptive } = useTheme();
    
    const Component = typeof element === 'string' ? element : element;
    
    return (
      <Component 
        ref={ref}
        className={cn(defaultClasses, className)}
        {...props}
      />
    );
  });
};

/**
 * Pre-built themed HTML elements
 */
export const ThemedDiv = createThemedComponent('div', 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-colors');
export const ThemedSection = createThemedComponent('section', 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-colors');
export const ThemedHeader = createThemedComponent('header', 'bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100 transition-colors');
export const ThemedFooter = createThemedComponent('footer', 'bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100 transition-colors');
export const ThemedMain = createThemedComponent('main', 'bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors');

/**
 * Utility to automatically theme any component
 */
export const autoTheme = (Component, options = {}) => {
  if (React.isValidElement(Component)) {
    // If it's a React element, clone it with theme props
    const theme = useTheme();
    return React.cloneElement(Component, {
      ...Component.props,
      className: theme.cn(
        Component.props.className || '',
        options.additionalClasses || ''
      )
    });
  }
  
  // If it's a component, wrap it with theme HOC
  return withTheme(Component, options);
};

export default withTheme;
