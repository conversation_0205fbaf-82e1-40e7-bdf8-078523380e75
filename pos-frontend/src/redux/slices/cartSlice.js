import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  items: [],
  total: 0,
  isEditMode: false,
  orderId: null
};

const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    addItems: (state, action) => {
      const { article, quantity } = action.payload;
      const existingItem = state.items.find(item => item.article.id === article.id);
      
      if (existingItem) {
        existingItem.quantity += quantity;
      } else {
        state.items.push({ 
          article: {
            ...article,
            price: parseFloat(article.price) || 0
          }, 
          quantity 
        });
      }
      
      // Recalculate total
      state.total = state.items.reduce((sum, item) => {
        const price = parseFloat(item.article.price) || 0;
        return sum + (price * item.quantity);
      }, 0);
    },
    removeItem: (state, action) => {
      const articleId = action.payload;
      state.items = state.items.filter(item => item.article.id !== articleId);
      
      // Recalculate total
      state.total = state.items.reduce((sum, item) => {
        const price = parseFloat(item.article.price) || 0;
        return sum + (price * item.quantity);
      }, 0);
    },
    updateQuantity: (state, action) => {
      const { articleId, quantity } = action.payload;
      const item = state.items.find(item => item.article.id === articleId);
      
      if (item) {
        item.quantity = quantity;
        // Recalculate total
        state.total = state.items.reduce((sum, item) => {
          const price = parseFloat(item.article.price) || 0;
          return sum + (price * item.quantity);
        }, 0);
      }
    },
    clearCart: (state) => {
      state.items = [];
      state.total = 0;
    },
    exitEditMode: (state) => {
      state.items = [];
      state.total = 0;
      state.isEditMode = false;
      state.orderId = null;
    },
    initializeCart: (state, action) => {
      const { orderItems, orderId } = action.payload;
      state.items = orderItems.map(item => ({
        orderItemId: item.id,
        article: {
          id: item.article.id,
          name: item.article.name,
          code: item.article.code,
          price: parseFloat(item.price) || 0
        },
        quantity: item.quantity
      }));
      state.total = state.items.reduce((sum, item) => {
        const price = parseFloat(item.article.price) || 0;
        const quantity = item.quantity || 0;
        return sum + (price * quantity);
      }, 0);
      state.isEditMode = true;
      state.orderId = orderId;
    },
    replaceItems: (state, action) => {
      state.items = action.payload;
      state.total = state.items.reduce((sum, item) => {
        const price = parseFloat(item.article.price) || 0;
        return sum + (price * item.quantity);
      }, 0);
    }
  },
});

export const { addItems, removeItem, updateQuantity, clearCart, initializeCart, exitEditMode, replaceItems } = cartSlice.actions;
export default cartSlice.reducer;