import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    table: null,
    orderId: null,
    tips: null
}

const tableSlice = createSlice({
    name: "table",
    initialState,
    reducers: {
        setTable: (state, action) => {
            state.table = action.payload;
        },
        clearTable: (state) => {
            state.table = null;
            state.orderId = null;
            state.tips = null;
        },
        initializeTableFromOrder: (state, action) => {
            const { orderId, table } = action.payload;
            state.orderId = orderId;
            state.table = table;
        },
        setTips: (state, action) => {
            state.tips = action.payload;
        },
        clearTips: (state) => {
            state.tips = null;
        }
    }
})

export const { setTable, clearTable, initializeTableFromOrder, setTips, clearTips } = tableSlice.actions;
export default tableSlice.reducer;