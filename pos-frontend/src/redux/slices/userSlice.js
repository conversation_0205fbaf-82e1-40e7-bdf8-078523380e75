import { createSlice } from "@reduxjs/toolkit";

const getInitialUser = () => {
    try {
        const stored = localStorage.getItem('user');
        if (stored) return JSON.parse(stored);
    } catch (e) {}
    return {
        _id: "",
        name: "",
        email: "",
        phone: "",
        role: "",
        isAuth: false
    };
};

const initialState = getInitialUser();

const userSlice = createSlice({
    name: "user",
    initialState,
    reducers: {
        setUser: (state, action) => {
            const { _id, name, phone, email, role  } = action.payload;
            state._id = _id;
            state.name = name;
            state.phone = phone;
            state.email = email;
            state.role = role;
            state.isAuth = true;
            localStorage.setItem('user', JSON.stringify({ _id, name, phone, email, role, isAuth: true }));
        },

        removeUser: (state) => {
            state._id = "";
            state.email = "";
            state.name = "";
            state.phone = "";
            state.role = "";
            state.isAuth = false;
            localStorage.removeItem('user');
        }
    }
})

export const { setUser, removeUser } = userSlice.actions;
export default userSlice.reducer;