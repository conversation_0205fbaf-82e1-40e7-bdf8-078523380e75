import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  defaultCurrency: "GH₵",
  backgroundImage: "",
  companyName: "",
  numberOfOrdersPerPage: 15,
  trackStock: false,
  darkMode: false,
  // Add other general settings as needed
};

const generalSlice = createSlice({
  name: "general",
  initialState,
  reducers: {
    setGeneralSettings: (state, action) => {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
});

export const { setGeneralSettings } = generalSlice.actions;
export default generalSlice.reducer;
