import { configureStore } from "@reduxjs/toolkit";
import tableSlice from "./slices/tableSlice"
import cartSlice from "./slices/cartSlice";
import userSlice from "./slices/userSlice";
import generalSlice from "./slices/generalSlice";

const store = configureStore({
    reducer: {
        table: tableSlice,
        cart: cartSlice,
        user: userSlice,
        general: generalSlice
    },
    devTools: import.meta.env.NODE_ENV !== "production",
});

export default store;
