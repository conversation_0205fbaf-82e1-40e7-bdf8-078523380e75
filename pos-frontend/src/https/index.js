import { axiosWrapper } from "./axiosWrapper";

// Helper function to convert params object to URLSearchParams string
const createQueryString = (params) => {
  if (!params) return "";
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        value.forEach((v) => searchParams.append(key, v));
      } else {
        searchParams.append(key, value);
      }
    }
  });
  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : "";
};

// API Endpoints

// Auth Endpoints
export const login = (data) => axiosWrapper.post("/api/user/login", data);
export const register = (data) => axiosWrapper.post("/api/user/register", data);
export const getUserData = () => axiosWrapper.get("/api/user");
export const codeLogin = (data) =>
  axiosWrapper.post("/api/auth/code-login/", data);
export const emailLogin = (data) =>
  axiosWrapper.post("/api/auth/email-password-login/", data);
export const getGeneralSettings = () =>
  axiosWrapper.get("/api/general/settings/");

export const getUserSettings = () =>
  axiosWrapper.get("/api/general/user-settings/");

export const updateUserSettings = (data) =>
  axiosWrapper.patch("/api/general/user-settings/", data);

export const logout = () => {
  const refresh = localStorage.getItem("refreshToken");
  return axiosWrapper.post("/api/auth/logout/", { refresh });
};

// Table Endpoints
export const addTable = (data) => axiosWrapper.post("/api/tables/", data);
export const getTables = (params) =>
  axiosWrapper.get(`/api/tables/${createQueryString(params)}`);
export const updateTable = ({ tableId, ...tableData }) =>
  axiosWrapper.put(`/api/tables/${tableId}/`, tableData);
export const createTable = (data) => axiosWrapper.post("/api/tables/", data);

// Payment Endpoints
export const createOrderRazorpay = (data) =>
  axiosWrapper.post("/api/orders/create-order/", data);
export const verifyPaymentRazorpay = (data) =>
  axiosWrapper.post("/api/orders/verify-payment/", data);

// Order Endpoints
export const getOrders = (params) =>
  axiosWrapper.get(`/api/orders/${createQueryString(params)}`);
export const createOrder = (data) => axiosWrapper.post("/api/orders/", data);
export const updateOrder = (orderId, data) =>
  axiosWrapper.patch(`/api/orders/${orderId}/`, data);
export const splitOrder = (data) =>
  axiosWrapper.post("/api/orders/ordered-items/split-quantity/", data);

// Payment Method Endpoints
export const getPaymentMethods = () =>
  axiosWrapper.get("/api/payment-methods/?is_active=true");

// Void Reason Endpoint
export const fetchVoidReasons = () =>
  axiosWrapper.get("api/literals/category-list/?category=void_reason");

// Analytics Endpoints
export const getSalesHierarchy = (params) =>
  axiosWrapper.get(
    `/api/analytics/sales-hierarchy/?includeArticles=true&includeSubFamily=true/${createQueryString(
      params
    )}`
  );
export const getUserPerformance = (params) =>
  axiosWrapper.get(
    `/api/analytics/user-performance/${createQueryString(params)}`
  );
export const getSalesSummary = (params) =>
  axiosWrapper.get(`/api/analytics/sales-summary/${createQueryString(params)}`);
export const getSalesByFamily = (params) =>
  axiosWrapper.get(
    `/api/analytics/sales-by-family/${createQueryString(params)}`
  );
export const getSalesByHour = (params) =>
  axiosWrapper.get(`/api/analytics/sales-by-hour/${createQueryString(params)}`);
export const getSalesByDay = (params) =>
  axiosWrapper.get(`/api/analytics/sales-by-day/${createQueryString(params)}`);
export const getPopularItemsByQuantity = (params) =>
  axiosWrapper.get(
    `/api/analytics/popular-items/quantity/${createQueryString(params)}`
  );
export const getPopularItemsByRevenue = (params) =>
  axiosWrapper.get(
    `/api/analytics/popular-items/revenue/${createQueryString(params)}`
  );
export const getTablePerformance = (params) =>
  axiosWrapper.get(
    `/api/analytics/table-performance/${createQueryString(params)}`
  );
export const getDailySalesTrend = (params) =>
  axiosWrapper.get(`/api/analytics/daily-trend/${createQueryString(params)}`);
export const getAdvancedSummary = (params) =>
  axiosWrapper.get(
    `/api/analytics/advanced-summary/${createQueryString(params)}`
  );
export const getOrderStatusAnalytics = (params) =>
  axiosWrapper.get(`/api/analytics/order-status/${createQueryString(params)}`);
export const getServingPeriodAnalytics = (params) =>
  axiosWrapper.get(
    `/api/analytics/serving-period/${createQueryString(params)}`
  );
export const getWorkstationPerformance = (params) =>
  axiosWrapper.get(
    `/api/analytics/workstation-performance/${createQueryString(params)}`
  );
export const getTipsAnalytics = (params) =>
  axiosWrapper.get(`/api/analytics/tips/${createQueryString(params)}`);
export const getPaymentAnalytics = (params) =>
  axiosWrapper.get(`/api/analytics/payment/${createQueryString(params)}`);
export const getDiscountAnalytics = (params) =>
  axiosWrapper.get(`/api/analytics/discount/${createQueryString(params)}`);
export const getSplitOrderAnalytics = (params) =>
  axiosWrapper.get(`/api/analytics/split-orders/${createQueryString(params)}`);
export const getVoidAnalytics = (params) =>
  axiosWrapper.get(`/api/analytics/voids/${createQueryString(params)}`);
export const getComprehensiveDashboard = (params) =>
  axiosWrapper.get(`/api/analytics/dashboard/${createQueryString(params)}`);
export const getArticleSalesStats = (articleId, params) =>
  axiosWrapper.get(
    `/api/analytics/articles/${articleId}/stats/${createQueryString(params)}`
  );

// Literals Endpoints
export const getAllLiterals = () => axiosWrapper.get("/api/literals/all/");
export const getLiteralsByCategory = (category) =>
  axiosWrapper.get(`/api/literals/category-list/?category=${category}`);

// User Endpoints
export const getUsers = (params) =>
  axiosWrapper.get(`/api/accounts/${createQueryString(params)}`);
