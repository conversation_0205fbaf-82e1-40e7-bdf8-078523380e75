/* ===================================================================
   COMPREHENSIVE DARK/LIGHT MODE THEMING SYSTEM
   ================================================================== */

/* CSS Custom Properties for Theme Variables */
:root {
  /* Light Mode Colors */
  --color-bg-primary: rgb(255 255 255);
  --color-bg-secondary: rgb(249 250 251);
  --color-bg-tertiary: rgb(243 244 246);
  --color-bg-accent: rgb(229 231 235);
  
  --color-text-primary: rgb(17 24 39);
  --color-text-secondary: rgb(75 85 99);
  --color-text-tertiary: rgb(107 114 128);
  --color-text-muted: rgb(156 163 175);
  
  --color-border-primary: rgb(229 231 235);
  --color-border-secondary: rgb(209 213 219);
  --color-border-accent: rgb(156 163 175);
  
  --color-surface-hover: rgb(243 244 246);
  --color-surface-active: rgb(229 231 235);
  
  /* Interactive Colors */
  --color-primary: rgb(59 130 246);
  --color-primary-hover: rgb(37 99 235);
  --color-success: rgb(34 197 94);
  --color-warning: rgb(245 158 11);
  --color-error: rgb(239 68 68);
  
  /* Shadow Variables */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* Transition Variables */
  --transition-colors: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
  --transition-all: all 0.2s ease;
}

/* Dark Mode Colors */
.dark {
  --color-bg-primary: rgb(31 41 55);
  --color-bg-secondary: rgb(17 24 39);
  --color-bg-tertiary: rgb(55 65 81);
  --color-bg-accent: rgb(75 85 99);
  
  --color-text-primary: rgb(243 244 246);
  --color-text-secondary: rgb(209 213 219);
  --color-text-tertiary: rgb(156 163 175);
  --color-text-muted: rgb(107 114 128);
  
  --color-border-primary: rgb(75 85 99);
  --color-border-secondary: rgb(55 65 81);
  --color-border-accent: rgb(107 114 128);
  
  --color-surface-hover: rgb(55 65 81);
  --color-surface-active: rgb(75 85 99);
  
  /* Shadows in dark mode */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

/* ===================================================================
   UTILITY CLASSES FOR CONSISTENT THEMING
   ================================================================== */

/* Background Utilities */
.theme-bg-primary { background-color: var(--color-bg-primary); }
.theme-bg-secondary { background-color: var(--color-bg-secondary); }
.theme-bg-tertiary { background-color: var(--color-bg-tertiary); }
.theme-bg-accent { background-color: var(--color-bg-accent); }

/* Text Utilities */
.theme-text-primary { color: var(--color-text-primary); }
.theme-text-secondary { color: var(--color-text-secondary); }
.theme-text-tertiary { color: var(--color-text-tertiary); }
.theme-text-muted { color: var(--color-text-muted); }

/* Border Utilities */
.theme-border-primary { border-color: var(--color-border-primary); }
.theme-border-secondary { border-color: var(--color-border-secondary); }
.theme-border-accent { border-color: var(--color-border-accent); }

/* Interactive Utilities */
.theme-hover:hover { background-color: var(--color-surface-hover); }
.theme-active:active { background-color: var(--color-surface-active); }

/* Transition Utilities */
.theme-transition { transition: var(--transition-colors); }
.theme-transition-all { transition: var(--transition-all); }

/* Shadow Utilities */
.theme-shadow-sm { box-shadow: var(--shadow-sm); }
.theme-shadow-md { box-shadow: var(--shadow-md); }
.theme-shadow-lg { box-shadow: var(--shadow-lg); }

/* ===================================================================
   COMPONENT BASE CLASSES
   ================================================================== */

/* Card Component */
.theme-card {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-colors);
}

.theme-card:hover {
  box-shadow: var(--shadow-md);
}

/* Button Components */
.theme-button-primary {
  background-color: var(--color-primary);
  color: white;
  border: 1px solid var(--color-primary);
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: var(--transition-colors);
}

.theme-button-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.theme-button-secondary {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-primary);
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: var(--transition-colors);
}

.theme-button-secondary:hover {
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-secondary);
}

/* Input Components */
.theme-input {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-primary);
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  transition: var(--transition-colors);
}

.theme-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.theme-input::placeholder {
  color: var(--color-text-muted);
}

/* Table Components */
.theme-table {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: 0.5rem;
  overflow: hidden;
}

.theme-table-header {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  border-bottom: 1px solid var(--color-border-primary);
}

.theme-table-row {
  border-bottom: 1px solid var(--color-border-primary);
  transition: var(--transition-colors);
}

.theme-table-row:hover {
  background-color: var(--color-surface-hover);
}

/* Modal Components */
.theme-modal {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-lg);
}

.theme-modal-header {
  border-bottom: 1px solid var(--color-border-primary);
  padding: 1.5rem;
}

.theme-modal-body {
  padding: 1.5rem;
}

.theme-modal-footer {
  border-top: 1px solid var(--color-border-primary);
  padding: 1.5rem;
  background-color: var(--color-bg-secondary);
}

/* Status Indicators */
.theme-status-success {
  background-color: rgb(34 197 94 / 0.1);
  color: var(--color-success);
  border: 1px solid rgb(34 197 94 / 0.2);
}

.theme-status-warning {
  background-color: rgb(245 158 11 / 0.1);
  color: var(--color-warning);
  border: 1px solid rgb(245 158 11 / 0.2);
}

.theme-status-error {
  background-color: rgb(239 68 68 / 0.1);
  color: var(--color-error);
  border: 1px solid rgb(239 68 68 / 0.2);
}

/* ===================================================================
   RESPONSIVE DESIGN HELPERS
   ================================================================== */

/* Ensure smooth transitions on theme changes */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Prevent transition on initial load */
.no-transition * {
  transition: none !important;
}

/* ===================================================================
   CHART AND VISUALIZATION THEMING
   ================================================================== */

/* Chart container theming */
.theme-chart-container {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: 0.5rem;
  padding: 1.5rem;
}

/* Chart text colors for libraries like Recharts */
.recharts-text {
  fill: var(--color-text-secondary) !important;
}

.recharts-cartesian-grid line {
  stroke: var(--color-border-primary) !important;
}

.recharts-tooltip-wrapper .recharts-default-tooltip {
  background-color: var(--color-bg-primary) !important;
  border: 1px solid var(--color-border-primary) !important;
  border-radius: 0.375rem !important;
  color: var(--color-text-primary) !important;
  box-shadow: var(--shadow-md) !important;
}
