/**
 * ===================================================================
 * THEME MANAGEMENT HOOK
 * ===================================================================
 * 
 * Custom hook for managing theme state and providing theme utilities
 * throughout the application.
 */

import { useSelector } from 'react-redux';
import { useMemo } from 'react';
import { themeClasses, cn, getThemeClasses, statusClasses, chartTheme } from '../utils/theme';

/**
 * Custom hook for theme management
 * @returns {Object} Theme utilities and state
 */
export const useTheme = () => {
  // Get dark mode state from Redux
  const darkMode = useSelector((state) => state.general.darkMode);

  // Memoized theme utilities to prevent unnecessary re-renders
  const theme = useMemo(() => ({
    // Current theme state
    isDark: darkMode,
    isLight: !darkMode,
    
    // Theme classes
    classes: themeClasses,
    
    // Utility functions
    cn,
    getThemeClasses,
    
    // Status classes
    status: statusClasses,
    
    // Chart theming
    chart: chartTheme,
    
    // Quick access to common patterns
    bg: {
      page: darkMode ? 'bg-gray-900' : 'bg-gray-100',
      card: darkMode ? 'bg-gray-800' : 'bg-white',
      surface: darkMode ? 'bg-gray-700' : 'bg-gray-50',
      accent: darkMode ? 'bg-gray-600' : 'bg-gray-200'
    },
    
    text: {
      primary: darkMode ? 'text-gray-100' : 'text-gray-900',
      secondary: darkMode ? 'text-gray-300' : 'text-gray-700',
      muted: darkMode ? 'text-gray-400' : 'text-gray-600'
    },
    
    border: {
      primary: darkMode ? 'border-gray-700' : 'border-gray-200',
      secondary: darkMode ? 'border-gray-600' : 'border-gray-300'
    }
  }), [darkMode]);

  /**
   * Get theme-aware component classes
   * @param {string} component - Component type
   * @param {string} variant - Component variant
   * @returns {string} Theme-aware classes
   */
  const getComponentClasses = (component, variant = 'default') => {
    return getThemeClasses(component, variant);
  };

  /**
   * Combine classes with theme awareness
   * @param {...string} classes - Classes to combine
   * @returns {string} Combined classes
   */
  const combineClasses = (...classes) => {
    return cn(...classes);
  };

  /**
   * Get status-specific classes
   * @param {string} status - Status type (success, warning, error, info)
   * @returns {string} Status classes
   */
  const getStatusClasses = (status) => {
    return statusClasses[status] || '';
  };

  /**
   * Get chart configuration for current theme
   * @returns {Object} Chart theme configuration
   */
  const getChartTheme = () => {
    return {
      ...chartTheme,
      // Override tooltip styles based on current theme
      tooltip: {
        ...chartTheme.tooltip,
        contentStyle: {
          ...chartTheme.tooltip.contentStyle,
          backgroundColor: darkMode ? 'rgb(31 41 55)' : 'rgb(255 255 255)',
          borderColor: darkMode ? 'rgb(75 85 99)' : 'rgb(229 231 235)',
          color: darkMode ? 'rgb(243 244 246)' : 'rgb(17 24 39)'
        }
      }
    };
  };

  /**
   * Apply conditional theming
   * @param {string} lightClasses - Classes for light mode
   * @param {string} darkClasses - Classes for dark mode
   * @returns {string} Appropriate classes for current theme
   */
  const conditional = (lightClasses, darkClasses) => {
    return darkMode ? darkClasses : lightClasses;
  };

  /**
   * Get adaptive classes that work in both themes
   * @param {string} type - Type of adaptive classes needed
   * @returns {string} Adaptive classes
   */
  const adaptive = (type) => {
    const adaptiveClasses = {
      card: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
      input: 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100',
      button: 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100',
      text: 'text-gray-900 dark:text-gray-100',
      textSecondary: 'text-gray-600 dark:text-gray-400',
      border: 'border-gray-200 dark:border-gray-700',
      hover: 'hover:bg-gray-100 dark:hover:bg-gray-700'
    };
    
    return adaptiveClasses[type] || '';
  };

  return {
    // Theme state
    isDark: theme.isDark,
    isLight: theme.isLight,
    
    // Theme utilities
    classes: theme.classes,
    bg: theme.bg,
    text: theme.text,
    border: theme.border,
    
    // Functions
    cn: combineClasses,
    getComponentClasses,
    getStatusClasses,
    getChartTheme,
    conditional,
    adaptive,
    
    // Chart theming
    chart: theme.chart
  };
};

/**
 * Hook for getting theme-aware chart props
 * @returns {Object} Chart theming props
 */
export const useChartTheme = () => {
  const { getChartTheme } = useTheme();
  
  return useMemo(() => {
    const chartTheme = getChartTheme();
    
    return {
      // Common props for Recharts components
      cartesianGrid: {
        strokeDasharray: "3 3",
        stroke: "currentColor",
        className: "text-gray-300 dark:text-gray-600"
      },
      
      xAxis: {
        stroke: "currentColor",
        className: "text-gray-600 dark:text-gray-400",
        fontSize: 12
      },
      
      yAxis: {
        stroke: "currentColor", 
        className: "text-gray-600 dark:text-gray-400",
        fontSize: 12
      },
      
      tooltip: chartTheme.tooltip,
      
      colors: chartTheme.colors
    };
  }, [getChartTheme]);
};

/**
 * Hook for form theming
 * @returns {Object} Form component classes
 */
export const useFormTheme = () => {
  const { adaptive, cn } = useTheme();
  
  return useMemo(() => ({
    label: cn(adaptive('text'), 'block text-sm font-medium mb-2'),
    input: cn(adaptive('input'), 'w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors'),
    select: cn(adaptive('input'), 'w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors'),
    textarea: cn(adaptive('input'), 'w-full px-3 py-2 rounded-lg resize-vertical focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors'),
    button: {
      primary: 'bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors',
      secondary: cn(adaptive('button'), 'px-4 py-2 rounded-lg font-medium transition-colors border', adaptive('border')),
      ghost: cn(adaptive('hover'), 'px-4 py-2 rounded-lg font-medium transition-colors', adaptive('textSecondary'))
    },
    error: 'text-red-600 dark:text-red-400 text-sm mt-1',
    help: 'text-gray-500 dark:text-gray-400 text-sm mt-1'
  }), [adaptive, cn]);
};

export default useTheme;
