import { useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';

export default function useInvalidateAndNavigate(queryKey, route) {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const invalidateAndNavigate = useCallback(async () => {
    setLoading(true);
    try {
      await queryClient.invalidateQueries({ queryKey });
      navigate(route);
    } finally {
      setLoading(false);
    }
  }, [queryClient, navigate, queryKey, route]);

  return [invalidateAndNavigate, loading];
} 