/**
 * ===================================================================
 * COMPREHENSIVE THEMING UTILITIES
 * ===================================================================
 * 
 * This file provides utilities for consistent theming across the application.
 * It includes class generators, theme detection, and component helpers.
 */

/**
 * Theme class generators for consistent styling
 */
export const themeClasses = {
  // Background classes
  bg: {
    primary: 'bg-white dark:bg-gray-800',
    secondary: 'bg-gray-50 dark:bg-gray-900', 
    tertiary: 'bg-gray-100 dark:bg-gray-700',
    accent: 'bg-gray-200 dark:bg-gray-600',
    surface: 'bg-gray-100 dark:bg-gray-700',
    overlay: 'bg-black bg-opacity-50'
  },

  // Text classes
  text: {
    primary: 'text-gray-900 dark:text-gray-100',
    secondary: 'text-gray-700 dark:text-gray-300', 
    tertiary: 'text-gray-600 dark:text-gray-400',
    muted: 'text-gray-500 dark:text-gray-500',
    inverse: 'text-gray-100 dark:text-gray-900'
  },

  // Border classes
  border: {
    primary: 'border-gray-200 dark:border-gray-700',
    secondary: 'border-gray-300 dark:border-gray-600',
    accent: 'border-gray-400 dark:border-gray-500',
    focus: 'focus:border-blue-500 dark:focus:border-blue-400'
  },

  // Interactive states
  hover: {
    bg: 'hover:bg-gray-100 dark:hover:bg-gray-700',
    bgSecondary: 'hover:bg-gray-200 dark:hover:bg-gray-600',
    text: 'hover:text-gray-900 dark:hover:text-gray-100'
  },

  // Component-specific classes
  card: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm',
  input: 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors',
  button: {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white border border-blue-600 hover:border-blue-700',
    secondary: 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600',
    ghost: 'bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
  },
  modal: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg',
  table: {
    container: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden',
    header: 'bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600',
    row: 'border-b border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors',
    cell: 'text-gray-900 dark:text-gray-100'
  }
};

/**
 * Utility function to combine theme classes
 * @param {...string} classes - Theme class keys or custom classes
 * @returns {string} Combined class string
 */
export const cn = (...classes) => {
  return classes.filter(Boolean).join(' ');
};

/**
 * Get theme-aware classes for common components
 */
export const getThemeClasses = (component, variant = 'default') => {
  const componentMap = {
    card: themeClasses.card,
    input: themeClasses.input,
    button: themeClasses.button[variant] || themeClasses.button.primary,
    modal: themeClasses.modal,
    table: themeClasses.table.container,
    tableHeader: themeClasses.table.header,
    tableRow: themeClasses.table.row,
    tableCell: themeClasses.table.cell
  };

  return componentMap[component] || '';
};

/**
 * Status-specific theme classes
 */
export const statusClasses = {
  success: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border-green-200 dark:border-green-700',
  warning: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 border-yellow-200 dark:border-yellow-700',
  error: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 border-red-200 dark:border-red-700',
  info: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-700'
};

/**
 * Chart theming utilities for libraries like Recharts
 */
export const chartTheme = {
  // Colors that work well in both light and dark modes
  colors: [
    '#3B82F6', // blue-500
    '#10B981', // emerald-500  
    '#F59E0B', // amber-500
    '#EF4444', // red-500
    '#8B5CF6', // violet-500
    '#06B6D4', // cyan-500
    '#84CC16', // lime-500
    '#F97316'  // orange-500
  ],
  
  // Grid and axis styling
  grid: {
    stroke: 'currentColor',
    className: 'text-gray-300 dark:text-gray-600'
  },
  
  axis: {
    stroke: 'currentColor', 
    className: 'text-gray-600 dark:text-gray-400'
  },

  // Tooltip styling
  tooltip: {
    contentStyle: {
      backgroundColor: 'var(--color-bg-primary)',
      border: '1px solid var(--color-border-primary)',
      borderRadius: '8px',
      color: 'var(--color-text-primary)',
      boxShadow: 'var(--shadow-md)'
    }
  }
};

/**
 * Animation and transition utilities
 */
export const transitions = {
  colors: 'transition-colors duration-200 ease-in-out',
  all: 'transition-all duration-200 ease-in-out',
  fast: 'transition-all duration-150 ease-in-out',
  slow: 'transition-all duration-300 ease-in-out'
};

/**
 * Responsive design helpers
 */
export const responsive = {
  container: 'container mx-auto px-4 sm:px-6 lg:px-8',
  grid: {
    cols1: 'grid grid-cols-1',
    cols2: 'grid grid-cols-1 md:grid-cols-2',
    cols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    cols4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  },
  spacing: {
    section: 'py-6 lg:py-8',
    component: 'p-4 lg:p-6',
    tight: 'p-2 lg:p-4'
  }
};

/**
 * Form component utilities
 */
export const formClasses = {
  label: cn(themeClasses.text.primary, 'block text-sm font-medium mb-2'),
  input: cn(themeClasses.input, 'w-full px-3 py-2 rounded-lg'),
  select: cn(themeClasses.input, 'w-full px-3 py-2 rounded-lg'),
  textarea: cn(themeClasses.input, 'w-full px-3 py-2 rounded-lg resize-vertical'),
  checkbox: 'w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500',
  radio: 'w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 focus:ring-blue-500',
  error: 'text-red-600 dark:text-red-400 text-sm mt-1',
  help: 'text-gray-500 dark:text-gray-400 text-sm mt-1'
};

/**
 * Layout utilities
 */
export const layoutClasses = {
  page: cn(themeClasses.bg.secondary, 'min-h-screen', transitions.colors),
  header: cn(themeClasses.bg.primary, themeClasses.border.primary, 'border-b', transitions.colors),
  sidebar: cn(themeClasses.bg.primary, themeClasses.border.primary, 'border-r', transitions.colors),
  main: cn(themeClasses.bg.secondary, 'flex-1', transitions.colors),
  footer: cn(themeClasses.bg.primary, themeClasses.border.primary, 'border-t', transitions.colors)
};

/**
 * Utility to generate consistent spacing
 */
export const spacing = {
  xs: 'gap-1',
  sm: 'gap-2', 
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8'
};

/**
 * Export commonly used combinations
 */
export const commonClasses = {
  // Page layouts
  pageContainer: cn(layoutClasses.page, responsive.container, responsive.spacing.section),
  contentCard: cn(themeClasses.card, responsive.spacing.component, transitions.colors),
  
  // Interactive elements
  clickable: cn(transitions.fast, 'cursor-pointer'),
  disabled: 'opacity-50 cursor-not-allowed',
  
  // Loading states
  loading: 'animate-pulse',
  skeleton: cn(themeClasses.bg.accent, 'animate-pulse rounded'),
  
  // Focus states
  focusRing: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800'
};

/**
 * Helper function to apply theme classes conditionally
 */
export const applyTheme = (baseClasses, themeClasses, condition = true) => {
  return condition ? cn(baseClasses, themeClasses) : baseClasses;
};

/**
 * Default export with all utilities
 */
export default {
  themeClasses,
  cn,
  getThemeClasses,
  statusClasses,
  chartTheme,
  transitions,
  responsive,
  formClasses,
  layoutClasses,
  spacing,
  commonClasses,
  applyTheme
};
