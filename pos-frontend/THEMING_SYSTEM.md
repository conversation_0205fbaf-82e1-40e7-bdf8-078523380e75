# Comprehensive Dark/Light Mode Theming System

## Overview

This document describes the comprehensive theming system implemented for the Restaurant POS System. The system provides automatic dark/light mode support for all components without requiring manual updates to individual components.

## Architecture

### 1. Core Components

- **CSS Custom Properties** (`src/styles/theme.css`) - CSS variables for consistent theming
- **Theme Utilities** (`src/utils/theme.js`) - JavaScript utilities for theme management
- **Theme Hook** (`src/hooks/useTheme.js`) - React hook for theme state and utilities
- **Theme Provider** (`src/components/shared/ThemeProvider.jsx`) - Automatic theme application

### 2. How It Works

1. **Backend Control**: The `darkMode` boolean from Django's `AppSetting` model controls the theme
2. **Redux State**: Theme state is stored in `state.general.darkMode`
3. **Automatic Application**: `ThemeProvider` watches Redux state and applies/removes the `dark` class
4. **CSS Variables**: Theme colors are defined as CSS custom properties that change based on the `dark` class
5. **Utility Classes**: Pre-built classes provide consistent theming across components

## Usage Guide

### 1. Using Theme Utilities

```jsx
import { useTheme } from "../hooks/useTheme";

const MyComponent = () => {
  const { adaptive, cn, getComponentClasses } = useTheme();

  return (
    <div className={adaptive("card")}>
      <h2 className={adaptive("text")}>Title</h2>
      <p className={adaptive("textSecondary")}>Description</p>
    </div>
  );
};
```

### 2. Using Pre-built Component Classes

```jsx
import { getThemeClasses } from "../utils/theme";

const MyCard = () => (
  <div className={getThemeClasses("card")}>
    <input className={getThemeClasses("input")} />
    <button className={getThemeClasses("button", "primary")}>Submit</button>
  </div>
);
```

### 3. Using CSS Custom Properties

```css
.my-component {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-primary);
  transition: var(--transition-colors);
}
```

### 4. Chart Theming

```jsx
import { useChartTheme } from "../hooks/useTheme";
import { BarChart, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts";

const MyChart = ({ data }) => {
  const chartTheme = useChartTheme();

  return (
    <BarChart data={data}>
      <CartesianGrid {...chartTheme.cartesianGrid} />
      <XAxis {...chartTheme.xAxis} />
      <YAxis {...chartTheme.yAxis} />
      <Tooltip {...chartTheme.tooltip} />
    </BarChart>
  );
};
```

## Available Utilities

### Theme Classes

```javascript
// Background classes
themeClasses.bg.primary; // 'bg-white dark:bg-gray-800'
themeClasses.bg.secondary; // 'bg-gray-50 dark:bg-gray-900'
themeClasses.bg.tertiary; // 'bg-gray-100 dark:bg-gray-700'

// Text classes
themeClasses.text.primary; // 'text-gray-900 dark:text-gray-100'
themeClasses.text.secondary; // 'text-gray-700 dark:text-gray-300'
themeClasses.text.muted; // 'text-gray-500 dark:text-gray-500'

// Border classes
themeClasses.border.primary; // 'border-gray-200 dark:border-gray-700'
themeClasses.border.secondary; // 'border-gray-300 dark:border-gray-600'

// Component classes
themeClasses.card; // Complete card styling
themeClasses.input; // Complete input styling
themeClasses.button; // Button variants (primary, secondary, ghost)
```

### Status Classes

```javascript
statusClasses.success; // Green theme for success states
statusClasses.warning; // Yellow theme for warning states
statusClasses.error; // Red theme for error states
statusClasses.info; // Blue theme for info states
```

### Form Classes

```javascript
formClasses.label; // Styled labels
formClasses.input; // Styled inputs
formClasses.select; // Styled selects
formClasses.textarea; // Styled textareas
formClasses.error; // Error text styling
formClasses.help; // Help text styling
```

## CSS Custom Properties

### Color Variables

```css
/* Light mode */
--color-bg-primary: rgb(255 255 255);
--color-text-primary: rgb(17 24 39);
--color-border-primary: rgb(229 231 235);

/* Dark mode (automatically applied with .dark class) */
--color-bg-primary: rgb(31 41 55);
--color-text-primary: rgb(243 244 246);
--color-border-primary: rgb(75 85 99);
```

### Transition Variables

```css
--transition-colors: color 0.2s ease, background-color 0.2s ease,
  border-color 0.2s ease;
--transition-all: all 0.2s ease;
```

### Shadow Variables

```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
```

## Component Patterns

### 1. Card Component

```jsx
const Card = ({ children, className = "" }) => {
  const { adaptive, cn } = useTheme();

  return (
    <div
      className={cn(adaptive("card"), "p-6 rounded-lg shadow-sm", className)}
    >
      {children}
    </div>
  );
};
```

### 2. Button Component

```jsx
const Button = ({
  variant = "primary",
  children,
  className = "",
  ...props
}) => {
  const { getComponentClasses, cn } = useTheme();

  return (
    <button
      className={cn(
        getComponentClasses("button", variant),
        "px-4 py-2 rounded-lg font-medium transition-colors",
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};
```

### 3. Input Component

```jsx
const Input = ({ className = "", ...props }) => {
  const { adaptive, cn } = useTheme();

  return (
    <input
      className={cn(
        adaptive("input"),
        "px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500",
        className
      )}
      {...props}
    />
  );
};
```

## Best Practices

### 1. Always Use Theme Utilities

```jsx
// ✅ Good - Uses theme utilities
const Component = () => {
  const { adaptive } = useTheme();
  return <div className={adaptive("card")}>Content</div>;
};

// ❌ Bad - Hardcoded colors
const Component = () => {
  return <div className="bg-[#1a1a1a] text-[#f5f5f5]">Content</div>;
};
```

### 2. Prefer CSS Custom Properties for Complex Styling

```css
/* ✅ Good - Uses CSS custom properties */
.custom-component {
  background: linear-gradient(
    to right,
    var(--color-bg-primary),
    var(--color-bg-secondary)
  );
  color: var(--color-text-primary);
}

/* ❌ Bad - Hardcoded values */
.custom-component {
  background: linear-gradient(to right, #ffffff, #f9fafb);
  color: #111827;
}
```

### 3. Use Semantic Class Names

```jsx
// ✅ Good - Semantic naming
const { adaptive } = useTheme();
<div className={adaptive('card')}>
  <h2 className={adaptive('text')}>Title</h2>
  <p className={adaptive('textSecondary')}>Description</p>
</div>

// ❌ Bad - Non-semantic
<div className="bg-white dark:bg-gray-800">
  <h2 className="text-gray-900 dark:text-gray-100">Title</h2>
  <p className="text-gray-600 dark:text-gray-400">Description</p>
</div>
```

### 4. Test in Both Themes

Always test components in both light and dark modes to ensure proper contrast and readability.

## Migration Guide

### Converting Existing Components

1. **Replace hardcoded colors** with theme utilities:

   ```jsx
   // Before
   <div className="bg-[#1a1a1a] text-[#f5f5f5]">

   // After
   <div className={adaptive('card')}>
   ```

2. **Use the theme hook** for dynamic styling:

   ```jsx
   import { useTheme } from "../hooks/useTheme";

   const Component = () => {
     const { adaptive, cn } = useTheme();
     // Use adaptive() and cn() for styling
   };
   ```

3. **Update charts** to use chart theming:

   ```jsx
   import { useChartTheme } from "../hooks/useTheme";

   const Chart = () => {
     const chartTheme = useChartTheme();
     // Apply chartTheme props to chart components
   };
   ```

## Troubleshooting

### Common Issues

1. **Theme not applying**: Ensure `ThemeProvider` wraps your component tree
2. **Styles not updating**: Check that you're using theme utilities instead of hardcoded classes
3. **Chart colors not changing**: Make sure you're using `useChartTheme()` hook
4. **Transitions not smooth**: Verify that `transition-colors` is applied to elements

### Debug Tips

1. Check if the `dark` class is being applied to `<html>` element
2. Verify Redux state contains the correct `darkMode` value
3. Use browser dev tools to inspect CSS custom property values
4. Ensure all theme utilities are imported correctly

## Automatic Theme Inheritance

### 1. ThemeProvider Component

The `ThemeProvider` automatically applies theme changes to the entire application:

```jsx
import ThemeProvider from "./components/shared/ThemeProvider";

function App() {
  return (
    <ThemeProvider>
      <YourAppContent />
    </ThemeProvider>
  );
}
```

### 2. Pre-built Themed Components

Use pre-built components that automatically inherit theme styling:

```jsx
import {
  ThemedCard,
  ThemedButton,
  ThemedInput,
  ThemedModal,
  ThemedTable,
} from "./components/shared/ThemedComponents";

const MyComponent = () => (
  <ThemedCard>
    <ThemedInput placeholder="Auto-themed input" />
    <ThemedButton variant="primary">Auto-themed button</ThemedButton>
  </ThemedCard>
);
```

### 3. Higher-Order Components (HOCs)

Automatically inject theme utilities into any component:

```jsx
import { withTheme, withPageTheme, withCardTheme } from "./hoc/withTheme";

// Basic theme injection
const MyComponent = withTheme(({ theme, adaptive, cn }) => (
  <div className={adaptive("card")}>
    <h1 className={adaptive("text")}>Auto-themed content</h1>
  </div>
));

// Page-level theming
const MyPage = withPageTheme(() => (
  <div>Page content with automatic theming</div>
));

// Card-level theming
const MyCard = withCardTheme(() => (
  <div>Card content with automatic theming</div>
));
```

### 4. Themed Component Factory

Create themed variants of existing components:

```jsx
import { createThemedVariant, createThemedComponent } from "./hoc/withTheme";

// Create themed variant with multiple styles
const ThemedAlert = createThemedVariant("div", {
  baseClasses: "p-4 rounded-lg border",
  variants: {
    success:
      "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",
    error: "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",
  },
  defaultVariant: "success",
});

// Usage
<ThemedAlert variant="error">Error message</ThemedAlert>;
```

## Making Any Component Dark-Mode Compatible

### Step-by-Step Guide

#### 1. For New Components

**Option A: Use Pre-built Themed Components**

```jsx
import { ThemedCard, ThemedButton } from "../shared/ThemedComponents";

const NewComponent = () => (
  <ThemedCard>
    <ThemedButton variant="primary">Automatically themed</ThemedButton>
  </ThemedCard>
);
```

**Option B: Use Theme Hook**

```jsx
import { useTheme } from "../hooks/useTheme";

const NewComponent = () => {
  const { adaptive, cn } = useTheme();

  return (
    <div className={adaptive("card")}>
      <h1 className={adaptive("text")}>Themed content</h1>
    </div>
  );
};
```

**Option C: Use HOC**

```jsx
import { withTheme } from "../hoc/withTheme";

const NewComponent = ({ adaptive, cn }) => (
  <div className={adaptive("card")}>
    <h1 className={adaptive("text")}>Themed content</h1>
  </div>
);

export default withTheme(NewComponent);
```

#### 2. For Existing Components

**Step 1: Replace hardcoded colors**

```jsx
// Before
<div className="bg-[#1a1a1a] text-[#f5f5f5]">

// After
<div className={adaptive('card')}>
```

**Step 2: Add theme hook**

```jsx
import { useTheme } from "../hooks/useTheme";

const ExistingComponent = () => {
  const { adaptive, cn } = useTheme();
  // Rest of component logic
};
```

**Step 3: Update all styling**

```jsx
// Replace all hardcoded colors with adaptive classes
className={cn(
  adaptive('card'),
  'additional-classes'
)}
```

### Quick Reference for Common Patterns

#### Cards and Containers

```jsx
const { adaptive } = useTheme();
<div className={adaptive("card")}>Content</div>;
```

#### Text Elements

```jsx
<h1 className={adaptive('text')}>Primary text</h1>
<p className={adaptive('textSecondary')}>Secondary text</p>
```

#### Interactive Elements

```jsx
<button className={adaptive('button')}>Button</button>
<input className={adaptive('input')} />
```

#### Borders and Dividers

```jsx
<div className={adaptive('border')}>With border</div>
<hr className={adaptive('border')} />
```

#### Hover States

```jsx
<div className={cn(adaptive("card"), adaptive("hover"))}>Hoverable card</div>
```

## Advanced Theming Techniques

### 1. Custom Theme Variables

Add custom CSS variables to `src/styles/theme.css`:

```css
:root {
  --custom-primary: #your-color;
  --custom-secondary: #your-other-color;
}

.dark {
  --custom-primary: #dark-version;
  --custom-secondary: #dark-other-color;
}
```

### 2. Component-Specific Theming

Create component-specific theme utilities:

```jsx
const useMyComponentTheme = () => {
  const { isDark, cn } = useTheme();

  return {
    container: cn(
      "relative overflow-hidden",
      isDark ? "bg-gray-800" : "bg-white"
    ),
    header: cn("border-b p-4", isDark ? "border-gray-700" : "border-gray-200"),
  };
};
```

### 3. Conditional Theming

Apply different styles based on theme:

```jsx
const { conditional, isDark } = useTheme();

return (
  <div
    className={conditional(
      "bg-gradient-to-r from-blue-400 to-blue-600", // Light mode
      "bg-gradient-to-r from-gray-700 to-gray-900" // Dark mode
    )}
  >
    {isDark ? <DarkIcon /> : <LightIcon />}
  </div>
);
```

## Testing Dark Mode Components

### 1. Manual Testing

- Toggle dark mode in the app settings
- Verify all components render correctly in both themes
- Check contrast ratios for accessibility

### 2. Automated Testing

```jsx
import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../store";
import ThemeProvider from "../components/shared/ThemeProvider";

const renderWithTheme = (component, darkMode = false) => {
  // Set dark mode in store
  store.dispatch(setDarkMode(darkMode));

  return render(
    <Provider store={store}>
      <ThemeProvider>{component}</ThemeProvider>
    </Provider>
  );
};

test("component renders correctly in dark mode", () => {
  const { container } = renderWithTheme(<MyComponent />, true);
  expect(container.firstChild).toHaveClass("dark");
});
```

### 3. Visual Regression Testing

Use tools like Storybook with Chromatic to test visual changes:

```jsx
// MyComponent.stories.js
export default {
  title: "Components/MyComponent",
  component: MyComponent,
  decorators: [
    (Story) => (
      <ThemeProvider>
        <Story />
      </ThemeProvider>
    ),
  ],
};

export const LightMode = {};
export const DarkMode = {
  parameters: {
    backgrounds: { default: "dark" },
    // Force dark mode for this story
    store: {
      initialState: {
        general: { darkMode: true },
      },
    },
  },
};
```

## Performance Considerations

### 1. Memoization

Theme utilities are memoized to prevent unnecessary re-renders:

```jsx
const theme = useMemo(
  () => ({
    // Theme utilities
  }),
  [darkMode]
);
```

### 2. CSS Custom Properties

Using CSS variables instead of JavaScript for theme switching provides better performance:

```css
/* Better performance - CSS handles the switching */
.component {
  background-color: var(--color-bg-primary);
}

/* Avoid - JavaScript recalculation on every render */
.component {
  background-color: $ {
    isdark? '#1f2937' : "#ffffff";
  }
}
```

### 3. Transition Optimization

Prevent layout thrashing during theme transitions:

```css
.no-transition * {
  transition: none !important;
}
```

## Accessibility Guidelines

### 1. Color Contrast

Ensure sufficient contrast ratios:

- Normal text: 4.5:1 minimum
- Large text: 3:1 minimum
- UI components: 3:1 minimum

### 2. Focus Indicators

Maintain visible focus indicators in both themes:

```jsx
className={cn(
  adaptive('input'),
  'focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
  'dark:focus:ring-offset-gray-800'
)}
```

### 3. Reduced Motion

Respect user preferences for reduced motion:

```css
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

## Future Enhancements

- **Multiple theme support** (not just dark/light)
- **User-specific theme preferences**
- **System theme detection** (auto mode)
- **Theme transition animations**
- **High contrast mode support**
- **Custom theme builder UI**
- **Theme presets for different use cases**
