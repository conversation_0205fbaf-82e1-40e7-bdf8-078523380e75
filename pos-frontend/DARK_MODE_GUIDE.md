# Dark Mode Implementation Guide

## Overview
This React application now supports dark mode functionality that is controlled by the `darkMode` boolean value from the backend general settings API. The implementation uses Tailwind CSS's class-based dark mode system.

## How It Works

### 1. Backend Integration
- The `darkMode` value comes from the backend's `AppSetting` model
- It's fetched via the `/api/general/settings/` endpoint
- The value is stored in Redux state under `state.general.darkMode`

### 2. Frontend Implementation
- **ThemeProvider Component**: Automatically applies/removes the `dark` class to `document.documentElement` based on Redux state
- **Tailwind Configuration**: Configured with `darkMode: 'class'` to enable class-based dark mode
- **CSS Transitions**: Smooth transitions between light and dark themes

## File Structure

```
pos-frontend/src/
├── components/shared/
│   ├── ThemeProvider.jsx      # Main theme controller
│   └── DarkModeToggle.jsx     # Toggle button component
├── redux/slices/
│   └── generalSlice.js        # Contains darkMode state
├── hooks/
│   └── useLoadData.js         # Fetches darkMode from backend
└── index.css                  # Base dark mode styles
```

## Usage in Components

### Basic Dark Mode Classes
Use Tailwind's dark mode classes throughout your components:

```jsx
// Background colors
<div className="bg-white dark:bg-gray-900">

// Text colors  
<h1 className="text-gray-900 dark:text-gray-100">

// Border colors
<div className="border-gray-200 dark:border-gray-700">

// Hover states
<button className="hover:bg-gray-100 dark:hover:bg-gray-800">
```

### Example Component
```jsx
import React from 'react';

const ExampleComponent = () => {
  return (
    <div className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
      <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
        Title
      </h2>
      <p className="text-gray-600 dark:text-gray-400">
        Description text that adapts to theme
      </p>
      <button className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors">
        Action Button
      </button>
    </div>
  );
};
```

## Color Palette Guidelines

### Light Mode
- **Background**: `bg-white`, `bg-gray-50`, `bg-gray-100`
- **Text**: `text-gray-900`, `text-gray-700`, `text-gray-600`
- **Borders**: `border-gray-200`, `border-gray-300`

### Dark Mode  
- **Background**: `dark:bg-gray-900`, `dark:bg-gray-800`, `dark:bg-gray-700`
- **Text**: `dark:text-gray-100`, `dark:text-gray-200`, `dark:text-gray-400`
- **Borders**: `dark:border-gray-700`, `dark:border-gray-600`

## Testing the Implementation

1. **Start the development server**:
   ```bash
   cd pos-frontend && npm run dev
   ```

2. **Access the application**: Open http://localhost:5174 (or the port shown in terminal)

3. **Test the toggle**: Look for the sun/moon icon in the header to toggle between light and dark modes

4. **Verify persistence**: The theme should persist based on the backend setting

## Adding Dark Mode to New Components

When creating new components, follow these steps:

1. **Add transition classes** for smooth theme switching:
   ```jsx
   className="transition-colors duration-200"
   ```

2. **Use semantic color classes** instead of hardcoded colors:
   ```jsx
   // Good
   className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
   
   // Avoid
   className="bg-[#ffffff] text-[#000000]"
   ```

3. **Test both themes** during development to ensure proper contrast and readability

## Backend Configuration

The dark mode setting is controlled in the Django admin panel:
- Navigate to **General > App Settings**
- Toggle the **Dark Mode** field
- The change will be reflected across all connected clients

## Troubleshooting

### Theme not applying
- Check if `ThemeProvider` is wrapping your component tree
- Verify that `darkMode` value is being fetched from the backend
- Ensure Tailwind CSS is properly configured with `darkMode: 'class'`

### Styles not updating
- Check browser console for any JavaScript errors
- Verify that the `dark` class is being added/removed from `<html>` element
- Clear browser cache and restart development server

## Future Enhancements

- Add user-specific theme preferences
- Implement system theme detection (auto mode)
- Add theme transition animations
- Create theme-aware custom components
