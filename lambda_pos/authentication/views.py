from accounts.models import User
from authentication.api_docs import MESSAGE_RESPONSE_API_DOC
from authentication.api_docs import USER_LOGININ_RESPONSE_API_DOC
from core.views import TaggedDecorator
from django.contrib.auth import authenticate
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenBlacklistView

from .serializers import CodeLoginSerializer
from .serializers import SimpleUserAccountSerializer
from .serializers import UserAccountSerializer
from .serializers import UserLoginSerializer


def get_tokens_for_user(user: User):
    refresh = RefreshToken.for_user(user)
    if not user.is_active:
        return Response(
            data="User account is not active",
            status=status.HTTP_401_UNAUTHORIZED,
        )
    return {
        "access": str(refresh.access_token),
        "refresh": str(refresh),
    }


class UserSignupView(TaggedDecorator, CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = UserAccountSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        password = serializer.validated_data.get("password")
        user: User = serializer.save()
        user.set_password(password)
        user.is_active = False
        user.save()

        return Response(
            data=UserAccountSerializer(user).data,
            status=status.HTTP_201_CREATED,
        )


class EmailPasswordLoginView(TaggedDecorator, CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = UserLoginSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data.get("email")
        password = serializer.validated_data.get("password")

        user: User = authenticate(email=email, password=password)
        existing_user: User = User.objects.filter(email=email).first()

        if user and existing_user.is_active:

            user.last_login = timezone.now()
            user.save(update_fields=["last_login"])

            return Response(
                {
                    "token": get_tokens_for_user(user),
                    "user": SimpleUserAccountSerializer(user).data,
                },
                status=status.HTTP_200_OK,
            )

        elif existing_user and not existing_user.is_active:
            return Response(
                "User account is not active",
                status=status.HTTP_401_UNAUTHORIZED,
            )

        return Response(
            "Email or Password is not valid",
            status=status.HTTP_401_UNAUTHORIZED,
        )

    @USER_LOGININ_RESPONSE_API_DOC
    def post(self, request, *args, **kwargs):

        return super().post(request, *args, **kwargs)


class CodeLoginView(TaggedDecorator, CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = CodeLoginSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            data=request.data, context={"code": request.user}
        )
        serializer.is_valid(raise_exception=True)

        code = serializer.validated_data.get("code")

        user: User = User.objects.filter(username=code).first()

        if not user:
            return Response(
                data="User account not found",
                status=status.HTTP_401_UNAUTHORIZED,
            )

        if user and not user.is_active:
            return Response(
                data="User account is not active",
                status=status.HTTP_401_UNAUTHORIZED,
            )

        return Response(
            {
                "token": get_tokens_for_user(user),
                "user": SimpleUserAccountSerializer(user).data,
            },
            status=status.HTTP_200_OK,
        )

    @MESSAGE_RESPONSE_API_DOC
    def post(self, request, *args, **kwargs):

        return super().post(request, *args, **kwargs)


class TokenBlacklistView(TaggedDecorator, TokenBlacklistView):
    pass
