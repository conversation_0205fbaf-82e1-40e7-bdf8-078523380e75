from accounts.models import User
from django.conf import settings
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.db import transaction

from drf_extra_fields.fields import Base64<PERSON><PERSON><PERSON>ield
from rest_framework import serializers
from user_roles.models import User<PERSON>ole
from user_roles.serializers import RoleSummarySerilizer
from user_roles.models import Role

class UserAccountSerializer(serializers.ModelSerializer):
    image = Base64ImageField(required=False, allow_null=True)
    password2 = serializers.CharField(write_only=True, required=False)

    role = serializers.ChoiceField(
        choices=Role.ROLES.CHOICES, required=False, default=Role.ROLES.WAITER
    )
    code = serializers.IntegerField(min_value=1000, max_value=9999)
   

    class Meta:
        model = User
        exclude = [
            "groups",
            "user_permissions",
            "last_login",
            "date_joined",
        ]
        read_only_fields = ["id", "is_superuser", "is_staff","username"]
        extra_kwargs = {
            "password": {"write_only": True},
            "password2": {"write_only": True, "required": True},
            "is_superuser": {"read_only": True},
          
        }

    def validate(self, attrs):
        password = attrs.get("password")
        password2 = self.initial_data.get("password_2")

        if password and password2:
            if password != password2:
                raise serializers.ValidationError(
                    code="password2",
                    detail="Password and Confirm Password do not match",
                )

        return super().validate(attrs)

    @transaction.atomic
    def create(self, validated_data):
        role = validated_data.pop("role", None)
        code = validated_data.pop("code", None)
        validated_data["username"] = code
       
        user: User = User.objects.create(**validated_data)

        if role:
            user.set_role(role)

        return user


class SimpleUserAccountSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        fields = [
            "id",
            "first_name",
            "last_name",
            "email",
            "mobile",
            "address",
            "gender",
            "is_active",
            "role",
            "code",
        ]

        read_only_fields = ["is_active"]




class UserTokenSerializer(serializers.Serializer):
    token = serializers.CharField()
    username = serializers.CharField(help_text="mobile or email")


class UserSignUpSerializer(serializers.Serializer):
    message = serializers.CharField()


class TokenSerializer(serializers.Serializer):
    access_token = serializers.CharField(default="")
    refresh_token = serializers.CharField(default="")


class UserLoginResponseSerializer(serializers.Serializer):
    user = SimpleUserAccountSerializer()
    token = TokenSerializer()


class UserLoginSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(max_length=255)

    class Meta:
        model = User
        fields = ["email", "password"]


class UserChangePasswordSerializer(serializers.Serializer):
    password = serializers.CharField(
        max_length=255, style={"input_type": "password"}, write_only=True
    )
    password2 = serializers.CharField(
        max_length=255,
        style={"input_type": "password"},
        write_only=True,
        required=False,
    )

    class Meta:
        fields = ["password", "password2"]

    def validate(self, attrs):
        password = attrs.get("password")
        password2 = self.initial_data.get("password_2")
        user: User = self.context.get("user")
        if password != password2:
            raise serializers.ValidationError(
                "Password and Confirm Password doesn't match"
            )

        try:
            validate_password(password)
        except ValidationError as e:
            raise serializers.ValidationError({"password": e.messages})
        user.set_password(password)
        user.save()
        return attrs




class CodeLoginSerializer(serializers.Serializer):
    code = serializers.IntegerField(min_value=1000, max_value=9999)


