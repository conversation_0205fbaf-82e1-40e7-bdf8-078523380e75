from django.urls import path

from .views import AdvancedSummaryView
from .views import ArticleSalesStatsView
from .views import ComprehensiveDashboardView
from .views import DailySalesTrendView
from .views import DiscountAnalyticsView
from .views import OrderStatusAnalyticsView
from .views import PaymentAnalyticsView
from .views import PopularItemsByQuantityView
from .views import PopularItemsByRevenueView
from .views import SalesByDayOfWeekView
from .views import SalesByFamilyView
from .views import SalesByHourView
from .views import SalesHierarchyView
from .views import SalesSummaryView
from .views import ServingPeriodAnalyticsView
from .views import SplitOrderAnalyticsView
from .views import TablePerformanceView
from .views import TipsAnalyticsView
from .views import UserPerformanceView
from .views import VoidAnalyticsView
from .views import WorkstationPerformanceView

app_name = "analytics"

urlpatterns = [
    path(
        "popular-items/quantity/",
        PopularItemsByQuantityView.as_view(),
        name="popular-items-quantity",
    ),
    path(
        "popular-items/revenue/",
        PopularItemsByRevenueView.as_view(),
        name="popular-items-revenue",
    ),
    path(
        "articles/<uuid:article_id>/stats/",
        ArticleSalesStatsView.as_view(),
        name="article-sales-stats",
    ),
    path("sales-summary/", SalesSummaryView.as_view(), name="sales-summary"),
    path("sales-by-hour/", SalesByHourView.as_view(), name="sales-by-hour"),
    path("sales-by-day/", SalesByDayOfWeekView.as_view(), name="sales-by-day"),
    path("sales-by-family/", SalesByFamilyView.as_view(), name="sales-by-family"),
    path(
        "table-performance/", TablePerformanceView.as_view(), name="table-performance"
    ),
    path("user-performance/", UserPerformanceView.as_view(), name="user-performance"),
    path("daily-trend/", DailySalesTrendView.as_view(), name="daily-trend"),
    path("advanced-summary/", AdvancedSummaryView.as_view(), name="advanced-summary"),
    path(
        "order-status/",
        OrderStatusAnalyticsView.as_view(),
        name="order-status-analytics",
    ),
    path(
        "serving-period/",
        ServingPeriodAnalyticsView.as_view(),
        name="serving-period-analytics",
    ),
    path(
        "workstation-performance/",
        WorkstationPerformanceView.as_view(),
        name="workstation-performance",
    ),
    path("tips/", TipsAnalyticsView.as_view(), name="tips-analytics"),
    path("payment/", PaymentAnalyticsView.as_view(), name="payment-analytics"),
    path("discount/", DiscountAnalyticsView.as_view(), name="discount-analytics"),
    path(
        "split-orders/", SplitOrderAnalyticsView.as_view(), name="split-order-analytics"
    ),
    path("voids/", VoidAnalyticsView.as_view(), name="void-analytics"),
    path(
        "dashboard/",
        ComprehensiveDashboardView.as_view(),
        name="comprehensive-dashboard",
    ),
    path("sales-hierarchy/", SalesHierarchyView.as_view(), name="sales-hierarchy"),
]
