from core.filters import DATE_RANGE_PRESET
from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import OpenApiParameter
from orders.models import Order


DATE_RANGE_PRESET_PARAMETER = OpenApiParameter(
    name="date_range_preset",
    enum=DATE_RANGE_PRESET.ALL,
    location=OpenApiParameter.QUERY,
    description="Preset date range filter",
    required=False,
)

COMMON_FILTER_PARAMETERS = [
    OpenApiParameter(
        name="start_date",
        type=str,
        location=OpenApiParameter.QUERY,
        description="Start date for filtering (YYYY-MM-DD)",
        required=False,
    ),
    OpenApiParameter(
        name="end_date",
        type=str,
        location=OpenApiParameter.QUERY,
        description="End date for filtering (YYYY-MM-DD)",
        required=False,
    ),
    DATE_RANGE_PRESET_PARAMETER,
    OpenApiParameter(
        name="status",
        enum=Order.STATUS.ALL,
        location=OpenApiParameter.QUERY,
        description="Filter by order status (can be multiple)",
        required=False,
    ),
    OpenApiParameter(
        name="user",
        type=str,
        location=OpenApiParameter.QUERY,
        description="Filter by user ID (UUID)",
        required=False,
    ),
    OpenApiParameter(
        name="limit",
        type=int,
        location=OpenApiParameter.QUERY,
        description="Limit the number of results (default: 10)",
        required=False,
    ),
]

# Popular Items Views
POPULAR_ITEMS_API_DOCS = extend_schema(
    description="Get popular items ranked by quantity sold",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

POPULAR_ITEMS_REVENUE_API_DOCS = extend_schema(
    description="Get popular items ranked by revenue",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Article Sales Stats View
ARTICLE_SALES_STATS_API_DOCS = extend_schema(
    description="Get sales statistics for a specific article",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Sales Summary View
SALES_SUMMARY_API_DOCS = extend_schema(
    description="Get overall sales summary",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Sales Analytics Views
SALES_BY_HOUR_API_DOCS = extend_schema(
    description="Get sales data by hour of day",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

SALES_BY_DAY_OF_WEEK_API_DOCS = extend_schema(
    description="Get sales data by day of week",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

SALES_BY_FAMILY_API_DOCS = extend_schema(
    description="Get sales data by product family",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Performance Analytics Views
TABLE_PERFORMANCE_API_DOCS = extend_schema(
    description="Get table performance metrics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

USER_PERFORMANCE_API_DOCS = extend_schema(
    description="Get user/waiter performance metrics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Sales Trend Views
DAILY_SALES_TREND_API_DOCS = extend_schema(
    description="Get daily sales trend analysis",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

ADVANCED_SUMMARY_API_DOCS = extend_schema(
    description="Get advanced analytics summary with multiple metrics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Order Analytics Views
ORDER_STATUS_ANALYTICS_API_DOCS = extend_schema(
    description="Get order status distribution analytics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

SERVING_PERIOD_ANALYTICS_API_DOCS = extend_schema(
    description="Get serving period analytics (Morning, Lunch, etc.)",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

WORKSTATION_PERFORMANCE_API_DOCS = extend_schema(
    description="Get workstation performance analytics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Payment and Tips Analytics
TIPS_ANALYTICS_API_DOCS = extend_schema(
    description="Get tips analytics and trends",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

PAYMENT_ANALYTICS_API_DOCS = extend_schema(
    description="Get payment method analytics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

DISCOUNT_ANALYTICS_API_DOCS = extend_schema(
    description="Get discount analytics and trends",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Order Management Analytics
SPLIT_ORDER_ANALYTICS_API_DOCS = extend_schema(
    description="Get split order analytics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

VOID_ANALYTICS_API_DOCS = extend_schema(
    description="Get void order analytics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Dashboard Views
COMPREHENSIVE_DASHBOARD_API_DOCS = extend_schema(
    description="Get comprehensive dashboard with all key metrics",
    parameters=COMMON_FILTER_PARAMETERS,
    tags=["Analytics"],
)

# Sales Hierarchy View (already exists, but updated)
SALES_HIERARCHY_API_DOCS = extend_schema(
    description="Get hierarchical sales report with Family → Sub-Family → Article structure",
    parameters=[
        OpenApiParameter(
            name="start_date",
            type=str,
            location=OpenApiParameter.QUERY,
            description="Start date for filtering (YYYY-MM-DD)",
            required=False,
        ),
        OpenApiParameter(
            name="end_date",
            type=str,
            location=OpenApiParameter.QUERY,
            description="End date for filtering (YYYY-MM-DD)",
            required=False,
        ),
        DATE_RANGE_PRESET_PARAMETER,
        OpenApiParameter(
            name="status",
            enum=Order.STATUS.ALL,
            location=OpenApiParameter.QUERY,
            description="Filter by order status (can be multiple)",
            required=False,
        ),
        OpenApiParameter(
            name="user",
            type=str,
            location=OpenApiParameter.QUERY,
            description="Filter by user ID (UUID)",
            required=False,
        ),
        OpenApiParameter(
            name="include_sub_family",
            type=bool,
            location=OpenApiParameter.QUERY,
            description="Include sub-family data in the response (default: false)",
            required=False,
        ),
        OpenApiParameter(
            name="include_articles",
            type=bool,
            location=OpenApiParameter.QUERY,
            description="Include article data in the response (default: false)",
            required=False,
        ),
    ],
    tags=["Analytics"],
)
