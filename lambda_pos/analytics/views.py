from analytics.api_docs import ADVANCED_SUMMARY_API_DOCS
from analytics.api_docs import ARTICLE_SALES_STATS_API_DOCS
from analytics.api_docs import COMPREHENSIVE_DASHBOARD_API_DOCS
from analytics.api_docs import DAILY_SALES_TREND_API_DOCS
from analytics.api_docs import DISCOUNT_ANALYTICS_API_DOCS
from analytics.api_docs import ORDER_STATUS_ANALYTICS_API_DOCS
from analytics.api_docs import PAYMENT_ANALYTICS_API_DOCS
from analytics.api_docs import POPULAR_ITEMS_API_DOCS
from analytics.api_docs import POPULAR_ITEMS_REVENUE_API_DOCS
from analytics.api_docs import SALES_BY_DAY_OF_WEEK_API_DOCS
from analytics.api_docs import SALES_BY_FAMILY_API_DOCS
from analytics.api_docs import SALES_BY_HOUR_API_DOCS
from analytics.api_docs import SALES_HIERARCHY_API_DOCS
from analytics.api_docs import SALES_SUMMARY_API_DOCS
from analytics.api_docs import SERVING_PERIOD_ANALYTICS_API_DOCS
from analytics.api_docs import SPLIT_ORDER_ANALYTICS_API_DOCS
from analytics.api_docs import TABLE_PERFORMANCE_API_DOCS
from analytics.api_docs import TIPS_ANALYTICS_API_DOCS
from analytics.api_docs import USER_PERFORMANCE_API_DOCS
from analytics.api_docs import VOID_ANALYTICS_API_DOCS
from analytics.api_docs import WORKSTATION_PERFORMANCE_API_DOCS
from core.dependency_injection import service_locator
from core.filters import DateRangeFilterMixin
from core.views import TaggedDecorator
from django.http import Http404
from django_filters.rest_framework import DjangoFilterBackend
from orders.models import Order
from rest_framework.generics import ListAPIView
from rest_framework.response import Response
from rest_framework.views import APIView

from .serializers import AdvancedSummarySerializer
from .serializers import ComprehensiveDashboardSerializer
from .serializers import DailySalesTrendSerializer
from .serializers import DiscountAnalyticsSerializer
from .serializers import OrderStatusAnalyticsSerializer
from .serializers import PaymentAnalyticsSerializer
from .serializers import PopularItemSerializer
from .serializers import SalesByDayOfWeekSerializer
from .serializers import SalesByFamilySerializer
from .serializers import SalesByHourSerializer
from .serializers import SalesHierarchySerializer
from .serializers import SalesSummarySerializer
from .serializers import ServingPeriodAnalyticsSerializer
from .serializers import SplitOrderAnalyticsSerializer
from .serializers import TablePerformanceSerializer
from .serializers import TipsAnalyticsSerializer
from .serializers import UserPerformanceSerializer
from .serializers import VoidAnalyticsSerializer
from .serializers import WorkstationPerformanceSerializer


@POPULAR_ITEMS_API_DOCS
class PopularItemsByQuantityView(TaggedDecorator, ListAPIView):
    """View to get popular items ranked by quantity sold"""

    pagination_class = None
    serializer_class = PopularItemSerializer

    def get_queryset(self):
        # Get query parameters
        limit = int(self.request.query_params.get("limit", 10))
        status_filter = self.request.query_params.getlist("status")
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        date_range_preset = self.request.query_params.get("date_range_preset")

        # Use default status filter if none provided
        if not status_filter:
            status_filter = [Order.STATUS.PAID]

        # Get analytics service
        return service_locator.analytics_service.get_popular_items_by_quantity(
            limit=limit,
            status_filter=status_filter,
            date_from=start_date,
            date_to=end_date,
            date_range_preset=date_range_preset,
        )


@POPULAR_ITEMS_REVENUE_API_DOCS
class PopularItemsByRevenueView(TaggedDecorator, ListAPIView):
    pagination_class = None
    serializer_class = PopularItemSerializer

    def get_queryset(self):
        # Get query parameters
        limit = int(self.request.query_params.get("limit", 10))
        status_filter = self.request.query_params.getlist("status")
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        date_range_preset = self.request.query_params.get("date_range_preset")

        # Use default status filter if none provided
        if not status_filter:
            status_filter = [Order.STATUS.PAID]

        # Get analytics service
        return service_locator.analytics_service.get_popular_items_by_revenue(
            limit=limit,
            status_filter=status_filter,
            date_from=start_date,
            date_to=end_date,
            date_range_preset=date_range_preset,
        )


@ARTICLE_SALES_STATS_API_DOCS
class ArticleSalesStatsView(TaggedDecorator, ListAPIView):
    """View to get sales stats for a specific article"""

    pagination_class = None
    serializer_class = PopularItemSerializer

    def get_queryset(self):
        article_id = self.kwargs.get("article_id")
        if not article_id:
            raise Http404("Article ID is required")

        # Get query parameters
        status_filter = self.request.query_params.getlist("status")
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        date_range_preset = self.request.query_params.get("date_range_preset")

        # Use default status filter if none provided
        if not status_filter:
            status_filter = [Order.STATUS.PAID]

        # Get analytics service
        stats = service_locator.analytics_service.get_article_sales_stats(
            article_id=article_id,
            status_filter=status_filter,
            date_from=start_date,
            date_to=end_date,
            date_range_preset=date_range_preset,
        )

        return [stats]  # Return as list for ListAPIView


@SALES_SUMMARY_API_DOCS
class SalesSummaryView(TaggedDecorator, APIView):
    """View to get overall sales summary"""

    def get(self, request, *args, **kwargs):
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        date_range_preset = request.query_params.get("date_range_preset")
        user_id = request.query_params.get("user_id")
        revenue_center = request.query_params.get("revenue_center")
        workstation = request.query_params.get("workstation")
        serving_period = request.query_params.get("serving_period")
        status_filter = request.query_params.getlist("status")

        if not status_filter:
            status_filter = [Order.STATUS.PAID]

        summary = service_locator.analytics_service.get_sales_summary(
            date_from=start_date,
            date_to=end_date,
            date_range_preset=date_range_preset,
            user_id=user_id,
            revenue_center=revenue_center,
            workstation=workstation,
            serving_period=serving_period,
            status_filter=status_filter,
        )
        serializer = SalesSummarySerializer(summary)
        return Response(serializer.data)


class BaseAnalyticsView(TaggedDecorator, ListAPIView):
    """Base view for analytics with common filtering"""

    pagination_class = None
    filter_backends = [DjangoFilterBackend]
    filterset_class = DateRangeFilterMixin

    def get_filter_params(self):
        """Extract common filter parameters"""
        limit = int(self.request.query_params.get("limit", 10))
        status_filter = self.request.query_params.getlist("status")
        user_id = self.request.query_params.get("user_id")
        revenue_center = self.request.query_params.get("revenue_center")
        workstation = self.request.query_params.get("workstation")
        serving_period = self.request.query_params.get("serving_period")

        # Get date range from DateRangeFilterMixin
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        date_range_preset = self.request.query_params.get("date_range_preset")

        if not status_filter:
            status_filter = [Order.STATUS.PAID]

        return {
            "limit": limit,
            "status_filter": status_filter,
            "date_from": start_date,
            "date_to": end_date,
            "date_range_preset": date_range_preset,
            "user_id": user_id,
            "revenue_center": revenue_center,
            "workstation": workstation,
            "serving_period": serving_period,
        }


@SALES_BY_HOUR_API_DOCS
class SalesByHourView(BaseAnalyticsView):
    """View to get sales data by hour"""

    serializer_class = SalesByHourSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return service_locator.analytics_service.get_sales_by_hour(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
            user_id=params["user_id"],
            revenue_center=params["revenue_center"],
            workstation=params["workstation"],
            serving_period=params["serving_period"],
        )


@SALES_BY_DAY_OF_WEEK_API_DOCS
class SalesByDayOfWeekView(BaseAnalyticsView):
    """View to get sales data by day of week"""

    serializer_class = SalesByDayOfWeekSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return service_locator.analytics_service.get_sales_by_day_of_week(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
        )


@SALES_BY_FAMILY_API_DOCS
class SalesByFamilyView(BaseAnalyticsView):
    """View to get sales data by product family"""

    serializer_class = SalesByFamilySerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return service_locator.analytics_service.get_sales_by_family(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
            revenue_center=params["revenue_center"],
        )


@TABLE_PERFORMANCE_API_DOCS
class TablePerformanceView(BaseAnalyticsView):
    """View to get table performance metrics"""

    serializer_class = TablePerformanceSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return service_locator.analytics_service.get_table_performance(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
        )


@USER_PERFORMANCE_API_DOCS
class UserPerformanceView(BaseAnalyticsView):
    """View to get user/waiter performance metrics"""

    serializer_class = UserPerformanceSerializer

    def get_queryset(self):

        params = self.get_filter_params()
        return service_locator.analytics_service.get_user_performance(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
            user_id=params["user_id"],
            revenue_center=params["revenue_center"],
        )


@DAILY_SALES_TREND_API_DOCS
class DailySalesTrendView(BaseAnalyticsView):
    """View to get daily sales trend"""

    serializer_class = DailySalesTrendSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return service_locator.analytics_service.get_daily_sales_trend(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
        )


@ADVANCED_SUMMARY_API_DOCS
class AdvancedSummaryView(BaseAnalyticsView):
    """View to get advanced analytics summary"""

    serializer_class = AdvancedSummarySerializer

    def get_queryset(self):
        params = self.get_filter_params()
        summary = service_locator.analytics_service.get_advanced_summary(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
        )
        return [summary]

    def filter_queryset(self, queryset):
        # Override to skip filtering for views that return lists
        return queryset


@ORDER_STATUS_ANALYTICS_API_DOCS
class OrderStatusAnalyticsView(BaseAnalyticsView):
    serializer_class = OrderStatusAnalyticsSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return service_locator.analytics_service.get_order_status_analytics(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
        )


@SERVING_PERIOD_ANALYTICS_API_DOCS
class ServingPeriodAnalyticsView(BaseAnalyticsView):
    serializer_class = ServingPeriodAnalyticsSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return service_locator.analytics_service.get_serving_period_analytics(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
        )


@WORKSTATION_PERFORMANCE_API_DOCS
class WorkstationPerformanceView(BaseAnalyticsView):
    serializer_class = WorkstationPerformanceSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return service_locator.analytics_service.get_workstation_performance(
            date_from=params["date_from"],
            date_to=params["date_to"],
            date_range_preset=params["date_range_preset"],
            status_filter=params["status_filter"],
        )


@TIPS_ANALYTICS_API_DOCS
class TipsAnalyticsView(BaseAnalyticsView):
    serializer_class = TipsAnalyticsSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return [
            service_locator.analytics_service.get_tips_analytics(
                date_from=params["date_from"],
                date_to=params["date_to"],
                date_range_preset=params["date_range_preset"],
                status_filter=params["status_filter"],
            )
        ]

    def filter_queryset(self, queryset):
        # Override to skip filtering for views that return lists
        return queryset


@PAYMENT_ANALYTICS_API_DOCS
class PaymentAnalyticsView(BaseAnalyticsView):
    serializer_class = PaymentAnalyticsSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return [
            service_locator.analytics_service.get_payment_analytics(
                date_from=params["date_from"],
                date_to=params["date_to"],
                date_range_preset=params["date_range_preset"],
                status_filter=params["status_filter"],
            )
        ]

    def filter_queryset(self, queryset):
        # Override to skip filtering for views that return lists
        return queryset


@DISCOUNT_ANALYTICS_API_DOCS
class DiscountAnalyticsView(BaseAnalyticsView):
    serializer_class = DiscountAnalyticsSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return [
            service_locator.analytics_service.get_discount_analytics(
                date_from=params["date_from"],
                date_to=params["date_to"],
                date_range_preset=params["date_range_preset"],
                status_filter=params["status_filter"],
            )
        ]

    def filter_queryset(self, queryset):
        # Override to skip filtering for views that return lists
        return queryset


@SPLIT_ORDER_ANALYTICS_API_DOCS
class SplitOrderAnalyticsView(BaseAnalyticsView):
    serializer_class = SplitOrderAnalyticsSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return [
            service_locator.analytics_service.get_split_order_analytics(
                date_from=params["date_from"],
                date_to=params["date_to"],
                date_range_preset=params["date_range_preset"],
                status_filter=params["status_filter"],
            )
        ]

    def filter_queryset(self, queryset):
        # Override to skip filtering for views that return lists
        return queryset


@VOID_ANALYTICS_API_DOCS
class VoidAnalyticsView(BaseAnalyticsView):
    serializer_class = VoidAnalyticsSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return [
            service_locator.analytics_service.get_void_analytics(
                date_from=params["date_from"],
                date_to=params["date_to"],
                date_range_preset=params["date_range_preset"],
            )
        ]

    def filter_queryset(self, queryset):
        # Override to skip filtering for views that return lists
        return queryset


@COMPREHENSIVE_DASHBOARD_API_DOCS
class ComprehensiveDashboardView(BaseAnalyticsView):
    serializer_class = ComprehensiveDashboardSerializer

    def get_queryset(self):
        params = self.get_filter_params()
        return [
            service_locator.analytics_service.get_comprehensive_dashboard(
                date_from=params["date_from"],
                date_to=params["date_to"],
                date_range_preset=params["date_range_preset"],
                status_filter=params["status_filter"],
            )
        ]

    def filter_queryset(self, queryset):
        # Override to skip filtering for views that return lists
        return queryset


@SALES_HIERARCHY_API_DOCS
class SalesHierarchyView(TaggedDecorator, ListAPIView):
    pagination_class = None
    """View to get hierarchical sales report (Family → Sub-Family → Article)"""

    serializer_class = SalesHierarchySerializer

    def get_queryset(self):
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        date_range_preset = self.request.query_params.get("date_range_preset")
        status_filter = self.request.query_params.getlist("status")
        include_sub_family = (
            self.request.query_params.get("include_sub_family", "false").lower()
            == "true"
        )
        include_articles = (
            self.request.query_params.get("include_articles", "false").lower() == "true"
        )
        if include_articles:
            include_sub_family = True
        user_id = self.request.query_params.get("user_id", None)

        if not status_filter:
            status_filter = [Order.STATUS.PAID]

        return [
            service_locator.analytics_service.get_sales_hierarchy_report(
                date_from=start_date,
                date_to=end_date,
                user_id=user_id,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
                include_sub_family=include_sub_family,
                include_articles=include_articles,
            )
        ]
