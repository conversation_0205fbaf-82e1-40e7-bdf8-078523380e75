from rest_framework import serializers


class PopularItemSerializer(serializers.Serializer):
    """Serializer for popular items data"""

    orderitem__article__id = serializers.IntegerField(source="article_id")
    orderitem__article__name = serializers.CharField(source="article_name")
    orderitem__article__price = serializers.DecimalField(
        source="article_price", max_digits=10, decimal_places=2
    )
    orderitem__article__category__name = serializers.CharField(
        source="sub_family_name", allow_null=True
    )
    total_quantity_sold = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_quantity_per_order = serializers.DecimalField(
        max_digits=5, decimal_places=2, allow_null=True
    )

    def to_representation(self, instance):
        """Custom representation to flatten the data structure"""
        return {
            "article_id": instance.get("orderitem__article__id"),
            "article_name": instance.get("orderitem__article__name"),
            "article_price": instance.get("orderitem__article__price"),
            "category_name": instance.get("orderitem__article__category__name"),
            "total_quantity_sold": instance.get("total_quantity_sold", 0),
            "total_orders": instance.get("total_orders", 0),
            "total_revenue": instance.get("total_revenue", 0),
            "avg_quantity_per_order": round(
                instance.get("avg_quantity_per_order", 0) or 0, 2
            ),
        }


class SalesSummarySerializer(serializers.Serializer):
    """Serializer for sales summary data"""

    period_days = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_items_sold = serializers.IntegerField()
    generated_at = serializers.DateTimeField()


class SalesByHourSerializer(serializers.Serializer):
    hour = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)


class SalesByDayOfWeekSerializer(serializers.Serializer):
    day_of_week = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)


class SalesByFamilySerializer(serializers.Serializer):
    family_id = serializers.UUIDField(source="article__sub_family__family__id")
    family_name = serializers.CharField(source="article__sub_family__family__name")
    family_code = serializers.CharField(source="article__sub_family__family__code")
    total_quantity_sold = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    unique_articles = serializers.IntegerField()


class TablePerformanceSerializer(serializers.Serializer):
    table_id = serializers.UUIDField(source="table__id")
    table_name = serializers.CharField(source="table__name")
    table_code = serializers.CharField(source="table__code")
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_items = serializers.IntegerField()


class UserPerformanceSerializer(serializers.Serializer):
    user_id = serializers.UUIDField(source="created_by__id")
    username = serializers.CharField(source="created_by__username")
    first_name = serializers.CharField(source="created_by__first_name")
    last_name = serializers.CharField(source="created_by__last_name")
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_items = serializers.IntegerField()


class DailySalesTrendSerializer(serializers.Serializer):
    date = serializers.DateField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_items = serializers.IntegerField()


class AdvancedSummarySerializer(serializers.Serializer):
    period_days = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_items_sold = serializers.IntegerField()
    total_discount = serializers.DecimalField(max_digits=12, decimal_places=2)
    unique_tables = serializers.IntegerField()
    orders_per_day = serializers.DecimalField(max_digits=10, decimal_places=2)
    revenue_per_day = serializers.DecimalField(max_digits=12, decimal_places=2)
    items_per_order = serializers.DecimalField(max_digits=10, decimal_places=2)
    generated_at = serializers.DateTimeField()


class OrderStatusAnalyticsSerializer(serializers.Serializer):
    status = serializers.CharField()
    count = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    percentage = serializers.FloatField()


class ServingPeriodAnalyticsSerializer(serializers.Serializer):
    serving_period = serializers.CharField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_items = serializers.IntegerField()


class WorkstationPerformanceSerializer(serializers.Serializer):
    workstation_id = serializers.UUIDField(source="workstation__id")
    workstation_name = serializers.CharField(source="workstation__name")
    workstation_location = serializers.CharField(source="workstation__location")
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_items = serializers.IntegerField()


class TipsByCustomerSerializer(serializers.Serializer):
    customer = serializers.CharField(source="tips_set__customer", allow_null=True)
    total_tips = serializers.DecimalField(max_digits=12, decimal_places=2)
    order_count = serializers.IntegerField()


class TipsAnalyticsSerializer(serializers.Serializer):
    summary = serializers.DictField()
    by_customer = TipsByCustomerSerializer(many=True)


class PaymentMethodAnalyticsSerializer(serializers.Serializer):
    payment_method_id = serializers.CharField(source="orderpayment__payment_method__id")
    payment_method_name = serializers.CharField(
        source="orderpayment__payment_method__name"
    )
    total_payments = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_payment = serializers.DecimalField(max_digits=10, decimal_places=2)


class PaymentStatusAnalyticsSerializer(serializers.Serializer):
    fully_paid = serializers.IntegerField()
    partially_paid = serializers.IntegerField()
    unpaid = serializers.IntegerField()
    voided = serializers.IntegerField()


class PaymentAnalyticsSerializer(serializers.Serializer):
    payment_methods = PaymentMethodAnalyticsSerializer(many=True)
    payment_status = PaymentStatusAnalyticsSerializer()


class DiscountAnalyticsSerializer(serializers.Serializer):
    total_discount = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_discount = serializers.DecimalField(max_digits=10, decimal_places=2)
    orders_with_discount = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    discount_percentage = serializers.FloatField()


class SplitOrderAnalyticsSerializer(serializers.Serializer):
    original_split_orders = serializers.IntegerField()
    split_children = serializers.IntegerField()
    split_children_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_split_value = serializers.DecimalField(max_digits=10, decimal_places=2)


class VoidReasonSerializer(serializers.Serializer):
    reason = serializers.CharField(
        source="orderitemvoid__reason__name", allow_null=True
    )
    count = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=12, decimal_places=2)


class VoidedOrdersSerializer(serializers.Serializer):
    count = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=12, decimal_places=2)


class VoidedItemsSerializer(serializers.Serializer):
    count = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=12, decimal_places=2)


class VoidAnalyticsSerializer(serializers.Serializer):
    voided_orders = VoidedOrdersSerializer()
    voided_items = VoidedItemsSerializer()
    void_reasons = VoidReasonSerializer(many=True)


class ComprehensiveDashboardSerializer(serializers.Serializer):
    sales_summary = SalesSummarySerializer()
    popular_items = PopularItemSerializer(many=True)
    top_revenue_items = PopularItemSerializer(many=True)
    sales_by_hour = SalesByHourSerializer(many=True)
    sales_by_day = SalesByDayOfWeekSerializer(many=True)
    sales_by_family = SalesByFamilySerializer(many=True)
    table_performance = TablePerformanceSerializer(many=True)
    user_performance = UserPerformanceSerializer(many=True)
    order_status = OrderStatusAnalyticsSerializer(many=True)
    serving_period = ServingPeriodAnalyticsSerializer(many=True)
    workstation_performance = WorkstationPerformanceSerializer(many=True)
    tips_analytics = TipsAnalyticsSerializer()
    payment_analytics = PaymentAnalyticsSerializer()
    discount_analytics = DiscountAnalyticsSerializer()
    split_order_analytics = SplitOrderAnalyticsSerializer()
    void_analytics = VoidAnalyticsSerializer()


class ArticleSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    name = serializers.CharField()
    code = serializers.CharField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_quantity_sold = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    avg_quantity_per_order = serializers.DecimalField(max_digits=5, decimal_places=2)


class SubFamilySerializer(serializers.Serializer):
    id = serializers.UUIDField()
    name = serializers.CharField()
    code = serializers.CharField()
    total_quantity_sold = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    unique_articles = serializers.IntegerField()
    articles = ArticleSerializer(many=True)


class FamilySerializer(serializers.Serializer):
    id = serializers.UUIDField()
    name = serializers.CharField()
    code = serializers.CharField()
    total_quantity_sold = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    unique_sub_families = serializers.IntegerField()
    unique_articles = serializers.IntegerField()


class FamilyHierarchySerializer(serializers.Serializer):
    family = FamilySerializer()
    sub_families = serializers.DictField(child=SubFamilySerializer())
    total_quantity_sold = serializers.IntegerField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)


class SalesHierarchySummarySerializer(serializers.Serializer):
    total_families = serializers.IntegerField()
    total_sub_families = serializers.IntegerField()
    total_articles = serializers.IntegerField()
    total_quantity_sold = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_orders = serializers.IntegerField()


class SalesHierarchySerializer(serializers.Serializer):
    hierarchy = serializers.DictField(child=FamilyHierarchySerializer())
    summary = SalesHierarchySummarySerializer()
