# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
.swp
*.swp
# C extensions
*.so
db.sqlite3
.sqlite3
# Distribution / packaging
.Python
db.sqlite3
build/
sql_app.db
*.db
*.sqlite3
.develop-eggs/
dump.rdb
/dump.rdb

*.rdb
test_sdk.py
/db/*.sqlite3
conftests_update.py
test_sdk.py
db.sqlite3
dist/
downloads/
*.sqlite
eggs/
.eggs/
lib/
migrations (copy)/
LOGS
__pycache__
*__pycache__
/LOGS
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec
venv
guid.md
# Installer logs
pip-log.txt
pip-delete-this-directory.txt
*.env
.env
# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
venv
*venv

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
# db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
/venv
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/
media


.DS_Store
test_sdk.py
