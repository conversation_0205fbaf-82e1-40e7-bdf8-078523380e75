import colorsys
import hashlib

from core.utils.format_responses import format_response_data
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON>ender<PERSON>
from rest_framework.response import Response


class CustomJsonRender(CamelCaseJSONRenderer):
    def render(self, data, accepted_media_type=None, renderer_context=None):
        status_code = None
        if renderer_context:
            response: Response = renderer_context["response"]
            status_code = response.status_code

        data = format_response_data(data, status_code)
        return super().render(data, accepted_media_type, renderer_context)


def get_dynamic_color_palette(identifier):

    color_palette = [
        "#03A9F4",  # Light Blue
        "#673AB7",  # Deep Purple
        "#F44336",  # Red
        "#FF9800",  # Orange
        "#CDDC39",  # Lime
        "#009688",  # Teal
        "#3F51B5",  # Indigo
        "#8BC34A",  # Light Green
        "#FFC107",  # Amber
        "#795548",  # Brown
        "#607D8B",  # Blue Grey
        "#FF4081",  # Pink Accent
        "#536DFE",  # Indigo Accent
        "#18FFFF",  # <PERSON>an Accent
        "#69F0AE",  # Green Accent
        "#FFFF00",  # Pure Yellow
        "#FF0080",  # Hot Pink
        "#8000FF",  # Electric Purple
        "#00FF80",  # Spring Green
        "#FF8000",  # Electric Orange
        "#FF1744",  # Bright Red
        "#FF6D00",  # Bright Orange
        "#FFD600",  # Bright Yellow
        "#00E676",  # Bright Green
        "#00BCD4",  # Bright Cyan
        "#2196F3",  # Bright Blue
        "#9C27B0",  # Bright Purple
        "#E91E63",  # Bright Pink
        "#FF5722",  # Deep Orange
        "#4CAF50",  # Green
    ]

    hash_object = hashlib.md5(str(identifier).encode("utf-8"))
    hash_int = int(hash_object.hexdigest()[:8], 16)
    return color_palette[hash_int % len(color_palette)]


def get_subfamily_color_variation(family_color, subfamily_id):

    if not family_color:
        return get_dynamic_color_palette(subfamily_id)

    # Convert hex to RGB then to HSV
    hex_color = family_color.lstrip("#")
    if len(hex_color) != 6:
        return get_dynamic_color_palette(subfamily_id)

    try:
        rgb = tuple(int(hex_color[i : i + 2], 16) for i in (0, 2, 4))
        h, s, v = colorsys.rgb_to_hsv(rgb[0] / 255, rgb[1] / 255, rgb[2] / 255)
    except (ValueError, TypeError):
        return get_dynamic_color_palette(subfamily_id)

    subfamily_id_int = subfamily_id.int

    hue_shift = (subfamily_id_int % 8) * 0.04
    h = (h + hue_shift) % 1.0

    s_variation = (subfamily_id_int % 4) * 0.1 - 0.1

    s = max(0.6, min(1.0, s + s_variation))

    v_variation = (subfamily_id_int % 5) * 0.05 - 0.1
    v = max(0.7, min(1.0, v + v_variation))

    # Convert back to RGB and hex
    try:
        rgb = colorsys.hsv_to_rgb(h, s, v)
        return "#{:02x}{:02x}{:02x}".format(
            int(rgb[0] * 255), int(rgb[1] * 255), int(rgb[2] * 255)
        )
    except Exception:
        return get_dynamic_color_palette(subfamily_id)


def get_article_color_variation(subfamily_color, article_id):

    if not subfamily_color:
        return get_dynamic_color_palette(article_id)

    # Convert hex to RGB then to HSV
    hex_color = subfamily_color.lstrip("#")
    if len(hex_color) != 6:
        return get_dynamic_color_palette(article_id)

    try:
        rgb = tuple(int(hex_color[i : i + 2], 16) for i in (0, 2, 4))
        h, s, v = colorsys.rgb_to_hsv(rgb[0] / 255, rgb[1] / 255, rgb[2] / 255)
    except (ValueError, TypeError):
        return subfamily_color

    article_id_int = article_id.int

    hue_shift = ((article_id_int % 3) - 1) * 0.01
    h = (h + hue_shift) % 1.0

    v_variation = ((article_id_int % 3) - 1) * 0.05
    v = max(0.6, min(1.0, v + v_variation))

    s = max(0.6, min(1.0, s))

    try:
        rgb = colorsys.hsv_to_rgb(h, s, v)
        return "#{:02x}{:02x}{:02x}".format(
            int(rgb[0] * 255), int(rgb[1] * 255), int(rgb[2] * 255)
        )
    except:
        return subfamily_color
