from drf_spectacular.utils import extend_schema

def app_tagged_view(view_class):
    """Automatically tag based on the app the view is in"""
    module = view_class.__module__
    
    if 'apps.' in module:
        app_name = module.split('apps.')[1].split('.')[0].title()
    else:
        app_name = module.split('.')[0].title()
    
    # Apply basic tagging to all view types
    return extend_schema(tags=[app_name])(view_class)