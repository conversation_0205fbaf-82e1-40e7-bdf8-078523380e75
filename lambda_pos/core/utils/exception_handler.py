import secrets
from datetime import datetime

from rest_framework import serializers
from rest_framework.response import Response
from rest_framework.views import exception_handler


def custom_exception_handler(exc, context):
    # Call REST framework's default exception handler first,
    # to get the standard error response.
    response: Response = exception_handler(exc, context)

    return response


def prevent_future_date_validator(value):
    if value and value > datetime.now().date():
        raise serializers.ValidationError(
            code="nonFieldErrors",
            detail="future  date not allowed",
        )


def prevent_past_date_validator(value):
    if value and value < datetime.now().date():
        raise serializers.ValidationError(
            code="nonFieldErrors",
            detail="past date not allowed",
        )


def percentage_validator(value):
    if value != 0 and (value < 1 or value > 100):
        raise serializers.ValidationError(
            code="nonFieldErrors",
            detail="Ensure this value is between 1 and 100.",
        )


def secure_random_with_N_digits(
    no_digits, model_class=None, field_name="code", max_attempts=100
):
    if no_digits > 16:  # Avoid huge numbers
        raise ValueError("Too many digits requested")

    range_start = 10 ** (no_digits - 1)
    range_end = (10**no_digits) - 1

    for _ in range(max_attempts):
        code = secrets.randbelow(range_end - range_start + 1) + range_start
        if not model_class:
            return code
        if not model_class.objects.filter(**{field_name: code}).exists():
            return code

    raise ValueError(f"Couldn't generate unique code after {max_attempts} attempts")
