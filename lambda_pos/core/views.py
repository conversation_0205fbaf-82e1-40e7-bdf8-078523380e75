from rest_framework.pagination import PageNumberPagination
from rest_framework.request import Request
from core.api_docs import FILTER_PAGINATE_SPECTACULAR_DOCS
from core.utils.decorators import app_tagged_view


class PaginateMixin:
    class OptionalPageNumberPagination(PageNumberPagination):
        def get_page_size(self, request: Request):
            if request.query_params.get("paginate", "true").lower() == "false":
                return None
            return super().get_page_size(request)
        
    pagination_class = OptionalPageNumberPagination

    @FILTER_PAGINATE_SPECTACULAR_DOCS
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
class TaggedDecorator(object):
    
    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Apply the decorator when a class inherits from this
        app_tagged_view(cls)