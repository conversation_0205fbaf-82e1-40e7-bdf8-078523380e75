from rest_framework import serializers
from core.dependency_injection import service_locator


class CreatedByMixin:
    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["created_by"] = user

        return super().create(validated_data)


class AbstractSerializer(serializers.ModelSerializer):

    class Meta:
        model = None
        fields = "__all__"
        depth = 1


class BaseToRepresentation:

    sensitive_fields = ['password', 'is_superuser',
                        'groups', 'user_permissions']

    def clean_sensitive_data(self, data):
        """Remove sensitive fields from nested dictionaries"""
        for value in data.values():
            if isinstance(value, dict):
                for field in self.sensitive_fields:
                    value.pop(field, None)

   
    def add_custom_fields(self, data, instance):
        for field_name, field in self.fields.items():
            if isinstance(field, serializers.SerializerMethodField):
                data[field_name] = getattr(self, f'get_{field_name}')(instance)
            elif field_name not in data:
                value = getattr(instance, field_name, None)
                data[field_name] = field.to_representation(
                    value) if value is not None else None

    def to_representation(self, instance):
        base_serializer = AbstractSerializer(instance=instance)
        base_serializer.Meta.model = instance.__class__
        data = base_serializer.data if instance else {}

        self.clean_sensitive_data(data)
        self.add_custom_fields(data, instance)

        return data
