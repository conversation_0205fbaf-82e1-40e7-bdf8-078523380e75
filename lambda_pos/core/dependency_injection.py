from analytics.service import Analy<PERSON>Service
from core.service import CoreService
from general.service import GeneralService
from orders.service import OrderService
from printing.services import ThermalPrintService
from user_roles.services import RoleService


class SERVICE_NAMES:

    CoreService = "core_service"
    RoleService = "role_service"
    OrderService = "order_service"
    AnalyticsService = "analytics_service"
    GeneralService = "general_service"
    ThermalPrintService = "thermal_print_service"


class ServiceLocator:

    service = {}

    core_service: CoreService
    role_service: RoleService
    order_service: OrderService
    analytics_service: AnalyticsService
    general_service: GeneralService
    thermal_print_service: ThermalPrintService

    def __init__(self):
        self._services = {}

    def register(self, name, service):
        self._services[name] = service

    def get(self, name):
        return self._services[name]

    def __getitem__(self, name):
        return self.get(name)

    def __getattr__(self, name):
        return self.get(name)


#  register services


service_locator = ServiceLocator()

# Instantiate and register the services

service_locator.register(SERVICE_NAMES.CoreService, CoreService())
service_locator.register(SERVICE_NAMES.RoleService, RoleService())
service_locator.register(SERVICE_NAMES.OrderService, OrderService())
service_locator.register(SERVICE_NAMES.AnalyticsService, AnalyticsService())
service_locator.register(SERVICE_NAMES.GeneralService, GeneralService())
service_locator.register(SERVICE_NAMES.ThermalPrintService, ThermalPrintService())
