from datetime import date
from datetime import datetime
from datetime import timedelta

from django import forms
from django.core.exceptions import ValidationError
from django.db.models import QuerySet
from django_filters import rest_framework as filters


class DATE_RANGE_PRESET:
    TODAY = "today"
    YESTERDAY = "yesterday"
    LAST_7_DAYS = "last_7_days"
    LAST_30_DAYS = "last_30_days"
    LAST_90_DAYS = "last_90_days"
    THIS_MONTH = "this_month"
    LAST_MONTH = "last_month"
    FIRST_QUARTER = "first_quarter"
    SECOND_QUARTER = "second_quarter"
    THIRD_QUARTER = "third_quarter"
    FOURTH_QUARTER = "fourth_quarter"
    THIS_YEAR = "this_year"
    THIS_WEEK = "this_week"
    LAST_WEEK = "last_week"

    CHOICES = [
        (TODAY, "Today"),
        (YESTERDAY, "Yesterday"),
        (LAST_7_DAYS, "Last 7 days"),
        (LAST_30_DAYS, "Last 30 days"),
        (LAST_90_DAYS, "Last 90 days"),
        (THIS_WEEK, "This week"),
        (LAST_WEEK, "Last week"),
        (THIS_MONTH, "This month"),
        (LAST_MONTH, "Last month"),
        (FIRST_QUARTER, "First quarter"),
        (SECOND_QUARTER, "Second quarter"),
        (THIRD_QUARTER, "Third quarter"),
        (FOURTH_QUARTER, "Fourth quarter"),
        (THIS_YEAR, "This year"),
    ]
    ALL = [
        TODAY,
        YESTERDAY,
        LAST_7_DAYS,
        LAST_30_DAYS,
        LAST_90_DAYS,
        THIS_WEEK,
        LAST_WEEK,
        THIS_MONTH,
        LAST_MONTH,
        FIRST_QUARTER,
        SECOND_QUARTER,
        THIRD_QUARTER,
        FOURTH_QUARTER,
        THIS_YEAR,
    ]


class DateRangeFilterMixin(filters.FilterSet):
    start_date = filters.DateFilter(
        field_name="created_at",
        lookup_expr="gte",
        label="Start Date",
        help_text="Filter records from this date onwards (YYYY-MM-DD)",
        widget=forms.DateInput(attrs={"type": "date"}),
    )

    end_date = filters.DateFilter(
        field_name="created_at",
        lookup_expr="lte",
        label="End Date",
        help_text="Filter records up to this date (YYYY-MM-DD)",
        widget=forms.DateInput(attrs={"type": "date"}),
    )

    date_range_preset = filters.ChoiceFilter(
        choices=DATE_RANGE_PRESET.CHOICES,
        method="filter_by_preset",
        label="Quick Date Range",
        help_text="Select a predefined date range",
    )

    def filter_by_preset(self, queryset: QuerySet, name, value):
        """Handle preset date range filtering on created_at field"""
        today = date.today()
        # Calculate start and end of this week (Monday to Sunday)
        start_of_this_week = today - timedelta(days=today.weekday())
        end_of_this_week = start_of_this_week + timedelta(days=6)
        # Calculate start and end of last week
        start_of_last_week = start_of_this_week - timedelta(days=7)
        end_of_last_week = start_of_this_week - timedelta(days=1)

        preset_filters = {
            DATE_RANGE_PRESET.TODAY: lambda: queryset.filter(created_at__date=today),
            DATE_RANGE_PRESET.YESTERDAY: lambda: queryset.filter(
                created_at__date=today - timedelta(days=1)
            ),
            DATE_RANGE_PRESET.LAST_7_DAYS: lambda: queryset.filter(
                created_at__date__gte=today - timedelta(days=7),
                created_at__date__lte=today,
            ),
            DATE_RANGE_PRESET.LAST_30_DAYS: lambda: queryset.filter(
                created_at__date__gte=today - timedelta(days=30),
                created_at__date__lte=today,
            ),
            DATE_RANGE_PRESET.LAST_90_DAYS: lambda: queryset.filter(
                created_at__date__gte=today - timedelta(days=90),
                created_at__date__lte=today,
            ),
            DATE_RANGE_PRESET.THIS_WEEK: lambda: queryset.filter(
                created_at__date__gte=start_of_this_week,
                created_at__date__lte=end_of_this_week,
            ),
            DATE_RANGE_PRESET.LAST_WEEK: lambda: queryset.filter(
                created_at__date__gte=start_of_last_week,
                created_at__date__lte=end_of_last_week,
            ),
            DATE_RANGE_PRESET.THIS_MONTH: lambda: queryset.filter(
                created_at__date__gte=today.replace(day=1), created_at__date__lte=today
            ),
            DATE_RANGE_PRESET.LAST_MONTH: lambda: self._get_last_month_filter(
                queryset, today
            ),
            DATE_RANGE_PRESET.FIRST_QUARTER: lambda: queryset.filter(
                created_at__quarter=1, created_at__year=today.year
            ),
            DATE_RANGE_PRESET.SECOND_QUARTER: lambda: queryset.filter(
                created_at__quarter=2, created_at__year=today.year
            ),
            DATE_RANGE_PRESET.THIRD_QUARTER: lambda: queryset.filter(
                created_at__quarter=3, created_at__year=today.year
            ),
            DATE_RANGE_PRESET.FOURTH_QUARTER: lambda: queryset.filter(
                created_at__quarter=4, created_at__year=today.year
            ),
            DATE_RANGE_PRESET.THIS_YEAR: lambda: queryset.filter(
                created_at__date__gte=today.replace(month=1, day=1),
                created_at__date__lte=today,
            ),
        }

        filter_func = preset_filters.get(value)
        if filter_func:
            return filter_func()

        return queryset

    def _get_last_month_filter(self, queryset: QuerySet, today):
        """Helper method for last month filter logic"""
        first_day_this_month = today.replace(day=1)
        last_month_end = first_day_this_month - timedelta(days=1)
        last_month_start = last_month_end.replace(day=1)
        return queryset.filter(
            created_at__date__gte=last_month_start, created_at__date__lte=last_month_end
        )

    def filter_queryset(self, queryset):
        if hasattr(self, "request") and self.request:
            start_date_str = self.request.GET.get("start_date")
            end_date_str = self.request.GET.get("end_date")

            if start_date_str and end_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()

                    if start_date > end_date:
                        raise ValidationError(
                            "Start date must be before or equal to end date."
                        )
                except ValueError:
                    raise ValidationError("Invalid date format. Use YYYY-MM-DD.")

        return super().filter_queryset(queryset)
