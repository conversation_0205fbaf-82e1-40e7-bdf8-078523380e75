from django.core.management.base import BaseCommand
import subprocess

class Command(BaseCommand):
    help = "Starts the server using daphne script"


    def handle(self, *args, **options):
        try:
            subprocess.run(["sh", "./start_daphne.sh"], check=True)
            self.stdout.write(self.style.SUCCESS('Daphne server started successfully.'))
        except subprocess.CalledProcessError as e:
            self.stderr.write(self.style.ERROR(f'Failed to start Daphne server: {e}'))



