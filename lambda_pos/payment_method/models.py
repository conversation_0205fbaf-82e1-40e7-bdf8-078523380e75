from django.db import models
from core.models import BaseModel
from core.utils.renders import get_dynamic_color_palette

class PaymentMethod(BaseModel):
    name = models.CharField(max_length=20)
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    icon = models.ImageField(upload_to='payment_method_icons/', null=True, blank=True)


    @property
    def generated_color(self):
        return get_dynamic_color_palette(self.id)

    def __str__(self):
        return self.name
