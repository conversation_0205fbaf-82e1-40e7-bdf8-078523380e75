from rest_framework import viewsets
from .models import PaymentMethod
from .serializers import PaymentMethodSerializer
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from core.views import TaggedDecorator

class PaymentMethodViewSet(TaggedDecorator,viewsets.ModelViewSet):
    pagination_class = None
    permission_classes = [IsAuthenticated]
    queryset = PaymentMethod.objects.order_by('-created_at')
    serializer_class = PaymentMethodSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active','name']
    

