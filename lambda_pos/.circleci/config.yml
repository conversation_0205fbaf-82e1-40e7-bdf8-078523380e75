version: 2.1

orbs:
  jira: circleci/jira@1.3.1
  browser-tools: circleci/browser-tools@1.2.5

jobs:
  build:
    working_directory: ~/backend
    environment:
      DATABASE_NAME: postgres
      DATABASE_USER: postgres
      DATABASE_PASSWORD: postgres
      DATABASE_PORT: 5432
      DATABASE_HOST: localhost
      AWS_ACCESS_KEY_ID: ab
      AWS_SECRET_ACCESS_KEY: ELE8
      AWS_STORAGE_BUCKET_NAME: "te"
      AWS_S3_SIGNATURE_VERSION: "s3v4"
      AWS_S3_REGION_NAME: "eu-west-3"
      AWS_S3_CUSTOM_DOMAIN: localhost
      AWS_PRESIGNED_EXPIRY: "10"
      FILE_UPLOAD_STORAGE: local #local | s3
      FILE_MAX_SIZE: "10485760" #10MB
      CELERY_BROKER_URL: "redis://localhost:6379"
      CELERY_RESULT_BACKEND: "redis://localhost:6379"
      PASSWORD_RESET_TIMEOUT: "10"
      SOCIAL_AUTH_GOOGLE_GOOGLE_REDIRECT_URL: "http://localhost:5053"
      SOCIAL_AUTH_GOOGLE_OAUTH2_KEY: "qrf24-t359jiwefow2"
      LOGIN_TOKEN_TIMEOUT_IN_SECONDS: "10"
      FRONTEND_URL: "http://localhost:3000"
      BASE_URL: "http://localhost:3000"
      SENDGRID_KEY: "2242fqwfwsc'qfe"
      SECRET_KEY: "qf24f243"
      CHAT_API_BASE_URL: "http://localhost:3000"
      CHAT_ORGANISATION_TOKEN: "2F24G3G3"
      CHAT_MODELS: "d2qf2"
      OBJECT_TYPE_SERIALIZERS: "{}"

      PAYSTACK_VALID_IP_ADDRESSES: "*********,**********"
      PAYSTACK_TEST_SECRET_KEY: "2r2w4t"
      PAYSTACK_TEST_PUBLIC_KEY: "2r2w4t"
      PAYSTACK_BASE_URL: "http://paystack.com"
      CLIENT_URL: "https://mobile-fixit-client.pages.dev/dashboard"
      ADMIN_URL: "https://mobile-fixit-admin.pages.dev/dashboard"
      REPAIRER_URL: "https://mobile-fixit-repairer.pages.dev/dashboard"

      ACCOUNT_DELETION_PERIOD_DAYS: 14

    docker:
      - image: cimg/python:3.9.12
        environment:
          CIRCLE_ARTIFACTS: .artifacts
          DATABASE_URL: postgresql://ubuntu@localhost/circle_test
          ENVIRONMENT: circleci
          PYTHONPATH: .

      - image: cimg/postgres:14.0
        environment:
          POSTGRES_USER: "postgres"
          POSTGRES_DB: "mobile_fixit"
          POSTGRES_PASSWORD: "postgres"
      - image: cimg/redis:6.2.6

    steps:
      - checkout

      - add_ssh_keys: # add private SSH key from CircleCI account based on fingerprint.
          fingerprints:
            - "SHA256:UvVWpcnrJNzJMGjw/zKYmByfbIen20RB84tO2GYnUTg"

      - restore_cache:
          keys:
            - mobile_fixit-{{ .Branch }}-{{ checksum "requirements.txt"}}
      - run:
          name: Install libraries
          command: |
            sudo apt update && sudo apt install python3-pip libpango-1.0-0 libpangoft2-1.0-0 libharfbuzz0b  imagemagick

            pip uninstall -y typing
            pip uninstall -y ktg_storage
            pip install --upgrade -r requirements.txt

      - save_cache:
          paths:
            - ~/.cache/pip
          key: mobile_fixit-{{ .Branch }}-{{ checksum "requirements.txt"}}

      # Tests, style checks
      - run:
          name: Check for code smells (prints, debugger)
          command: scripts/check_code_smells.sh

      - run:
          name: Run code style check
          command: scripts/check_git_style.sh

      - run:
          name: Check for conflicting migrations

          command: scripts/check_migrations.sh

      - run:
          name: Run tests
          command: python manage.py test --verbosity=2

  build_docker_image:
    resource_class: medium
    machine:
      image: default
    steps:
      - checkout

      - run:
          name: Build image
          command: docker build -t mobile_fixit .

      - run:
          name: Publish image
          command: ./scripts/publish-image.sh $CIRCLE_BRANCH

  deploy:
    docker:
      - image: cimg/python:3.10

    steps:
      - checkout

      - restore_cache:
          keys:
            - mobile_fixit-{{ .Branch }}-{{ checksum "requirements.txt"}}
            - mobile_fixit-{{ .Branch }}
            - mobile_fixit

      - run:
          name: Install dependencies
          command: |
            pip install awscli

      - deploy:
          name: Deploy to ecs
          command: ./scripts/deploy-ecs.sh $CIRCLE_BRANCH

workflows:
  test_and_deploy:
    jobs:
      - build

      - build_docker_image:
          name: build_docker_image
          requires:
            - build
          filters:
            branches:
              only:
                - master
                - production

      - deploy:
          name: deploy
          requires:
            - build_docker_image
          filters:
            branches:
              only:
                - master
                - production
