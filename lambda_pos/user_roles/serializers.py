from rest_framework import serializers

from .models import Role


class RoleSerializer(serializers.ModelSerializer):
   
    name = serializers.SerializerMethodField()

    class Meta:
        model = Role
        exclude = ["created_at", "updated_at", "is_deleted"]

    def get_name(self, obj: Role):
        return obj.name


class RoleSummarySerilizer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = Role
        exclude = [
            "created_at",
            "updated_at",
            "is_deleted",
        ]

    def get_name(self, obj: Role):
        return obj.name
