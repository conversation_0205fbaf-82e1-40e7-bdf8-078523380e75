from typing import Union

from accounts.models import User
from core.models import BaseModel
from django.db import models
from django.db.models.query import QuerySet
from django.utils.translation import gettext_lazy as _


class RolesManager(models.Manager):
    def get_queryset(self) -> QuerySet:
        return super().get_queryset().order_by("level", "name")

    def get_by_natural_key(self, name):
        return self.get(name=name)


class Role(BaseModel):
    class ROLES:

        ADMINISTRATOR = "administrator"
        CASHIER = "cashier"
        WAITER = "waiter"

        ALL = (
            ADMINISTRATOR,
            CASHIER,
            WAITER,

        )
        CHOICES = (
            (ADMINISTRATOR, _("Administrator")),
            (CASHIER, _("Cashier")),
            (WAITER, _("Waiter")),

        )

    objects: RolesManager = RolesManager()
    name = models.CharField(max_length=255, choices=ROLES.CHOICES, unique=True)
    description = models.TextField()
    level = models.IntegerField(default=0)

    def __str__(self):
        return self.name


class UserRolesManager(models.Manager):
    def get_queryset(self) -> QuerySet:
        return (
            super()
            .get_queryset()
            .all()
        )

    def get_user_roles(
        self, user: User
    ) -> Union["UserRole", None]:
        return (
            self.get_queryset()
            .filter(user=user)
            .first()
        )


class UserRole(BaseModel):
    objects: UserRolesManager = UserRolesManager()
    role = models.ForeignKey(
        Role, verbose_name=_("Role"), on_delete=models.CASCADE
    )
    user = models.OneToOneField(
        User, verbose_name=_("User"), on_delete=models.CASCADE
    )

    def __str__(self) -> str:
        return self.role.name
