from typing import List
from typing import TypedDict

from django.db import transaction
from django.db.models import QuerySet
from user_roles.models import Role
from user_roles.models import User
from user_roles.models import UserRole


class UserRoleData(TypedDict):
    name: str
    description: str
    features: List[str]


ROLES: List[UserRoleData] = [
    {
        "name": Role.ROLES.ADMINISTRATOR,
        "level": 0,
        "description": "Description for Admin",
        "features": [],
    },
    {
        "name": Role.ROLES.CASHIER,
        "level": 1,
        "description": "Description for Cashier",
        "features": [],
    },
    {
        "name": Role.ROLES.WAITER,
        "level": 2,
        "description": "Description for Waiter",
        "features": [],
    },
]


class RoleService:
    def __init__(self) -> None:
        pass

  
    @transaction.atomic
    def delete_roles(self):
        Role.objects.all().delete()

    def get_user_role(self, user) -> UserRole:
        user_role: UserRole = UserRole.objects.filter(user=user).first()

        return user_role

    def has_role(self, user, role_name: str) -> bool:
        user_role = self.get_user_role(user)
        return user_role is not None and user_role.role.name == role_name

    def is_administrator(self, user) -> bool:
        return self.has_role(user, Role.ROLES.ADMINISTRATOR)

    def is_cashier(self, user) -> bool:
        return self.has_role(user, Role.ROLES.CASHIER)

    def is_waiter(self, user) -> bool:
        return self.has_role(user, Role.ROLES.WAITER)

    def get_users_with_same_role(self, role_name: str) -> QuerySet[User]:
        return User.objects.filter(userrole__role__name=role_name)
    
    def can_view_all_tables(self, user) -> bool:
        return self.is_administrator(user) or self.is_cashier(user)
