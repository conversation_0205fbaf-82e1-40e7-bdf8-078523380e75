#!/bin/bash

./manage.py graph_models --exclude-models=*Abstract* --no-inheritance  --hide-relations-from-fields -a --rankdir BT -o ./model_graph/all.png
./manage.py graph_models --exclude-models=*Abstract* --no-inheritance  --hide-relations-from-fields -a -g --rankdir BT -o ./model_graph/all_grouped.png
./manage.py graph_models  --no-inheritance -a --rankdir BT -o ./model_graph/normal.png
./manage.py graph_models  --no-inheritance -g -a --rankdir BT -o ./model_graph/normal_grouped.png