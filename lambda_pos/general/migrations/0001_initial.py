# Generated by Django 5.2.3 on 2025-06-18 01:48

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AppSetting',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('mode', models.CharField(choices=[('restaurant', 'Restaurant'), ('bar', 'Bar'), ('hotel', 'Hotel'), ('cafe', 'Cafe'), ('other', 'Other')], default='restaurant', max_length=20)),
                ('company_address', models.TextField(blank=True, null=True)),
                ('company_phone', models.CharField(blank=True, max_length=200, null=True)),
                ('company_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('default_currency', models.CharField(blank=True, max_length=10, null=True)),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='')),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='')),
                ('company_name', models.CharField(blank=True, max_length=200, null=True)),
                ('company_website', models.URLField(blank=True, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
