from general.models import AppSetting
from general.models import UserSettings
from rest_framework import serializers


class AppSettingSerializer(serializers.ModelSerializer):
    vat = serializers.SerializerMethodField()
    company_logo = serializers.SerializerMethodField()

    class Meta:
        model = AppSetting
        exclude = [
            "is_deleted",
        ]

    def get_vat(self, obj: AppSetting):
        from literals.models import Vat
        from literals.serializer import VatSerializer

        vat = Vat.objects.filter(is_active=True).first()
        return VatSerializer(vat).data

    def get_company_logo(self, obj: AppSetting):
        request = self.context.get("request")

        if not request:
            return

        return request.build_absolute_uri(obj.company_logo.url)


class UserSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserSettings
        exclude = [
            "is_deleted",
        ]
