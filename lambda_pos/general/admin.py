from django.contrib import admin
from general.models import AppSetting
from general.models import UserSettings


@admin.register(AppSetting)
class AppSettingAdmin(admin.ModelAdmin):
    list_display = [
        "mode",
        "company_name",
        "company_phone",
        "company_email",
        "company_website",
    ]
    list_filter = ["mode"]
    search_fields = [
        "company_name",
        "company_phone",
        "company_email",
        "company_website",
    ]
    list_per_page = 10
    list_max_show_all = 100
    list_editable = [
        "company_name",
        "company_phone",
        "company_email",
        "company_website",
    ]
    list_display_links = ["mode"]

    def has_add_permission(self, request):
        if AppSetting.objects.exists():
            return False
        return super().has_add_permission(request)


admin.site.register(UserSettings)
