import factory
from general.models import AppSetting


class AppSettingFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = AppSetting

    service_fee = factory.Faker(
        "pydecimal", left_digits=3, right_digits=2, positive=True
    )
    company_address = factory.Faker("address")
    company_phone = factory.Faker("phone_number")
    company_email = factory.Faker("email")
    mode = factory.Faker("random_element", elements=AppSetting.MODE.ALL)
    default_currency = factory.Faker("currency_code")
    company_logo = factory.Faker("file_name", category="image")
    company_name = factory.Faker("company")
    slack_user_ids = factory.Faker(
        "pylist", nb_elements=3, variable_nb_elements=True, value_types=str
    )
    payment_gateway_live_mode = factory.Faker("boolean")
    is_active = factory.Faker("boolean")
