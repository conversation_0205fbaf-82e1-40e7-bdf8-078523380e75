from core.views import TaggedDecorator
from django.http import Http404
from general.models import AppSetting
from general.models import UserSettings
from rest_framework.generics import RetrieveUpdateAPIView
from rest_framework.permissions import IsAuthenticated

from .serializers import AppSettingSerializer
from .serializers import UserSettingsSerializer


class AppSettingViewSet(TaggedDecorator, RetrieveUpdateAPIView):
    serializer_class = AppSettingSerializer

    def get_queryset(self):

        return AppSetting.objects.all()

    def get_object(self):

        queryset = self.get_queryset()
        obj = queryset.first()
        if obj is None:
            raise Http404()
        return obj


class UserSettingsViewSet(TaggedDecorator, RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserSettingsSerializer

    def get_queryset(self):
        return UserSettings.objects.filter(user=self.request.user)

    def get_object(self):
        queryset = self.get_queryset()
        obj = queryset.first()
        if obj is None:
            obj = UserSettings.objects.create(user=self.request.user)
        return obj
