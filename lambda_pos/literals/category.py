class LiteralCategory:
    PRINTER = "printer"
    PRODUCT_TYPE = "product_type"
    UNIT_MEASURE = "unit_measure"
    VAT = "vat"
    WORKSTATION = "workstation"
    VOID_REASON = "void_reason"
    REVENUE_CENTER = "revenue_center"

    ALL = [
        PRINTER,
        PRODUCT_TYPE,
        UNIT_MEASURE,
        VAT,
        WOR<PERSON>TA<PERSON><PERSON>,
        VOID_REASON,
        REVENUE_CENTER,
    ]

    CHOICES = [
        (PRINTE<PERSON>, "Printer"),
        (PRODUCT_TYPE, "Product Type"),
        (UNIT_MEASURE, "Unit Measure"),
        (VAT, "VAT"),
        (WOR<PERSON><PERSON><PERSON>ON, "Workstation"),
        (VOID_REASON, "Void Reason"),
        (REVENUE_CENTER, "Revenue Center"),
    ]
