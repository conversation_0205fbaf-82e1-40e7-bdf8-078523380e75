from colorfield.fields import ColorField
from core.models import BaseModel
from django.db import models
from django.db.models import F

# Create your models here.


class LiteralBaseManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(is_deleted=False)
            .annotate(label=F("name"), value=F("id"))
        )


class BaseLiterals(BaseModel):
    objects: LiteralBaseManager = LiteralBaseManager()
    name = models.CharField(max_length=1024)
    description = models.TextField(blank=True, null=True)
    disabled = models.BooleanField(default=False)

    class Meta:
        abstract = True

    def __str__(self) -> str:
        return self.name


class ProductType(BaseLiterals):
    pass


class UnitMeasure(BaseLiterals):
    pass


class Vat(BaseLiterals):
    rate = models.DecimalField(max_digits=5, decimal_places=2)
    is_active = models.BooleanField(default=False)


class Workstation(BaseLiterals):

    ip_address = models.GenericIPAddressField(blank=True, null=True)
    is_active = models.BooleanField(default=False)
    is_connected = models.BooleanField(default=False)
    location = models.CharField(max_length=255, blank=True, null=True)


class Printer(BaseLiterals):
    class CONNECTION_TYPE:
        NETWORK = "network"
        USB = "usb"
        CHOICES = [(NETWORK, "Network"), (USB, "USB")]
        ALL = [NETWORK, USB]

    ip_address = models.GenericIPAddressField(blank=True, null=True)
    port = models.IntegerField(default=9100)
    is_active = models.BooleanField(default=False)
    is_connected = models.BooleanField(default=False)
    location = models.CharField(max_length=255, blank=True, null=True)
    workstation = models.ForeignKey(Workstation, on_delete=models.CASCADE, null=True)

    connection_type = models.CharField(
        max_length=10, choices=CONNECTION_TYPE.CHOICES, default=CONNECTION_TYPE.NETWORK
    )


class RevenueCenter(BaseLiterals):
    pass


class VoidReason(BaseLiterals):
    pass


class GeneratedColor(BaseModel):

    family = models.ForeignKey("products.Family", on_delete=models.CASCADE)
    color_hex = ColorField()
