from core.utils.renders import get_article_color_variation
from core.utils.renders import get_dynamic_color_palette
from core.utils.renders import get_subfamily_color_variation
from literals.models import GeneratedColor


class ColorMixin:
    @property
    def generated_color(self):
        if self.color:
            return self.color

        if hasattr(self, "sub_family"):
            family = self.sub_family.family
        elif hasattr(self, "family"):
            family = self.family
        else:
            family = self

        try:
            stored_color = GeneratedColor.objects.get(family=family)
            return stored_color.color_hex
        except GeneratedColor.DoesNotExist:
            pass

        if hasattr(self, "sub_family"):
            subfamily_color = self.sub_family.generated_color
            return get_article_color_variation(subfamily_color, self.id)
        elif hasattr(self, "family"):
            family_color = self.family.generated_color
            return get_subfamily_color_variation(family_color, self.id)
        else:
            return get_dynamic_color_palette(self.id)
