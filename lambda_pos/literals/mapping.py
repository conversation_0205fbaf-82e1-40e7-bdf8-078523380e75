from typing import Dict

from literals.category import LiteralCategory
from literals.models import Printer
from literals.models import ProductType
from literals.models import RevenueCenter
from literals.models import UnitMeasure
from literals.models import Vat
from literals.models import VoidReason
from literals.models import Workstation

from . import models


LITERAL_CATEGORY_MODEL_MAPPING: Dict[LiteralCategory, models.Model] = {
    LiteralCategory.PRINTER: Printer,
    LiteralCategory.PRODUCT_TYPE: ProductType,
    LiteralCategory.UNIT_MEASURE: UnitMeasure,
    LiteralCategory.VAT: Vat,
    LiteralCategory.WORKSTATION: Workstation,
    LiteralCategory.VOID_REASON: VoidReason,
    LiteralCategory.REVENUE_CENTER: RevenueCenter,
}
