from django.contrib import admin

from . import models

# Register your models here.
admin.site.register(
    [
        models.Printer,
        models.ProductType,
        models.UnitMeasure,
        models.VoidReason,
        models.RevenueCenter,
    ]
)


@admin.register(models.Workstation)
class WorkstationAdmin(admin.ModelAdmin):
    list_display = ["name", "ip_address", "is_active"]
    list_filter = ["is_active"]
    search_fields = ["name", "ip_address"]

    list_editable = ["is_active"]


@admin.register(models.Vat)
class VatAdmin(admin.ModelAdmin):
    list_display = ["name", "rate", "is_active"]
    list_filter = ["is_active"]
    search_fields = ["name", "rate"]
    list_editable = ["is_active"]


class GeneratedColorAdmin(admin.ModelAdmin):
    list_display = ["id", "family", "color_hex"]
    list_filter = ["family"]
    search_fields = ["family__name", "color_hex"]
    list_editable = ["family", "color_hex"]


admin.site.register(models.GeneratedColor, GeneratedColorAdmin)
