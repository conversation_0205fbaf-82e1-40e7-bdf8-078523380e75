from core.views import TaggedDecorator
from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import OpenApiParameter
from literals.category import LiteralCategory
from literals.serializer import ListAllLiteralsSerializer
from literals.serializer import LITERAL_CATEGORY_SERIALIZER_MAPPING
from literals.serializer import LiteralsSerializer
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView


class ValidCategoryMixin:
    def validate_category(self, category):
        if category not in LiteralCategory.ALL:

            raise ValidationError(
                detail="Invalid category, available categories are %s"
                % LiteralCategory.ALL,
                code="Invalid_Category",
            )

    def get_context(self, *args, **kwargs):
        return {"request": self.request}


class ListLiteralsView(TaggedDecorator, APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        responses=ListAllLiteralsSerializer,
        description="Returns all literal lookup values used in the frontend.",
    )
    def get(self, request):
        serializer = LiteralsSerializer(context={"request": request}, data={})
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ListLiteralCategoryApiView(TaggedDecorator, APIView, ValidCategoryMixin):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="category",
                location=OpenApiParameter.QUERY,
                required=True,
                type=str,
                description="category",
                enum=LiteralCategory.ALL,
            ),
            OpenApiParameter(
                name="name",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="name (partial match)",
            ),
        ],
        responses={200: None},
    )
    def get(self, request: Request, *args, **kwargs):
        category = self.request.query_params.get("category")
        name = self.request.query_params.get("name")
        self.validate_category(category)

        serializer_class = LITERAL_CATEGORY_SERIALIZER_MAPPING.get(category, None)
        if not serializer_class:
            raise ValidationError(
                {
                    "code": "invalid serializer class",
                    "details": "literals serializer not found",
                }
            )
        model = serializer_class.Meta.model
        queryset = model.objects.all()
        if name:
            queryset = queryset.filter(name__icontains=name)
        serializer = serializer_class(queryset, many=True)

        return Response(data=serializer.data, status=status.HTTP_200_OK)
