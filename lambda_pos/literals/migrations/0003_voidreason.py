# Generated by Django 5.2.3 on 2025-06-23 10:27

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('literals', '0002_alter_printer_ip_address_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='VoidReason',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=1024)),
                ('description', models.TextField(blank=True, null=True)),
                ('disabled', models.BooleanField(default=False)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
