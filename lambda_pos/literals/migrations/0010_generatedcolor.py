# Generated by Django 5.2.3 on 2025-08-03 19:49
import uuid

import colorfield.fields
import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    dependencies = [
        ("literals", "0009_remove_is_main_receipt_printer"),
        ("products", "0006_alter_family_color_alter_subfamily_color"),
    ]

    operations = [
        migrations.CreateModel(
            name="GeneratedColor",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "color_hex",
                    colorfield.fields.ColorField(
                        default="#FFFFFF", image_field=None, max_length=25, samples=None
                    ),
                ),
                (
                    "family",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.family",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
