from typing import Dict

from drf_spectacular.utils import extend_schema_field
from general.models import AppSetting
from general.serializers import AppSettingSerializer
from literals.category import LiteralCategory
from literals.models import Printer
from literals.models import ProductType
from literals.models import RevenueCenter
from literals.models import UnitMeasure
from literals.models import Vat
from literals.models import VoidReason
from literals.models import Workstation
from rest_framework import serializers

# from drf_yasg.utils import swagger_serializer_method


class RevenueCenterSerializer(serializers.ModelSerializer):
    class Meta:
        model = RevenueCenter
        exclude = ["is_deleted"]


class VoidReasonSerializer(serializers.ModelSerializer):
    class Meta:
        model = VoidReason
        exclude = ["is_deleted"]


class ProductTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductType
        exclude = ["is_deleted"]


class UnitMeasureSerializer(serializers.ModelSerializer):
    class Meta:
        model = UnitMeasure
        exclude = ["is_deleted"]


class VatSerializer(serializers.ModelSerializer):
    class Meta:
        model = Vat

        exclude = ["is_deleted"]


class WorkstationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Workstation
        exclude = ["is_deleted"]


class PrinterSerializer(serializers.ModelSerializer):
    class Meta:
        model = Printer
        exclude = ["is_deleted"]


class ListAllLiteralsSerializer(serializers.Serializer):
    product_types = serializers.SerializerMethodField()
    unit_measures = serializers.SerializerMethodField()
    vats = serializers.SerializerMethodField()
    workstations = serializers.SerializerMethodField()
    printers = serializers.SerializerMethodField()
    app_settings = serializers.SerializerMethodField()
    void_reasons = serializers.SerializerMethodField()
    revenue_center = serializers.SerializerMethodField()

    @extend_schema_field(RevenueCenterSerializer(many=True))
    def get_revenue_center(self, obj):
        return RevenueCenterSerializer(RevenueCenter.objects.all(), many=True).data

    @extend_schema_field(VoidReasonSerializer(many=True))
    def get_void_reasons(self, obj):
        return VoidReasonSerializer(VoidReason.objects.all(), many=True).data

    @extend_schema_field(AppSettingSerializer(many=True))
    def get_app_settings(self, obj):
        return AppSettingSerializer(AppSetting.objects.all(), many=True).data

    @extend_schema_field(ProductTypeSerializer(many=True))
    def get_product_types(self, obj):
        return ProductTypeSerializer(ProductType.objects.all(), many=True).data

    @extend_schema_field(UnitMeasureSerializer(many=True))
    def get_unit_measures(self, obj):
        return UnitMeasureSerializer(UnitMeasure.objects.all(), many=True).data

    @extend_schema_field(VatSerializer(many=True))
    def get_vats(self, obj):
        return VatSerializer(Vat.objects.all(), many=True).data

    @extend_schema_field(WorkstationSerializer(many=True))
    def get_workstations(self, obj):
        return WorkstationSerializer(Workstation.objects.all(), many=True).data

    @extend_schema_field(PrinterSerializer(many=True))
    def get_printers(self, obj):
        return PrinterSerializer(Printer.objects.all(), many=True).data


class LiteralsSerializer(serializers.Serializer):
    product_types = serializers.SerializerMethodField()
    unit_measures = serializers.SerializerMethodField()
    vats = serializers.SerializerMethodField()
    workstations = serializers.SerializerMethodField()
    printers = serializers.SerializerMethodField()
    app_settings = serializers.SerializerMethodField()
    revenue_center = serializers.SerializerMethodField()

    void_reasons = serializers.SerializerMethodField()

    def get_revenue_center(self, obj):
        return RevenueCenter.objects.values()

    def get_void_reasons(self, obj):
        return VoidReason.objects.values()

    def get_app_settings(self, obj):
        app_settings = AppSetting.objects.first()

        return AppSettingSerializer(app_settings, context=self.context).data

    def get_product_types(self, obj):
        return ProductType.objects.values()

    def get_unit_measures(self, obj):
        return UnitMeasure.objects.values()

    def get_vats(self, obj):
        return Vat.objects.values()

    def get_workstations(self, obj):
        return Workstation.objects.values()

    def get_printers(self, obj):
        return Printer.objects.values()


LITERAL_CATEGORY_SERIALIZER_MAPPING: Dict[LiteralCategory, serializers.Serializer] = {
    LiteralCategory.PRINTER: PrinterSerializer,
    LiteralCategory.PRODUCT_TYPE: ProductTypeSerializer,
    LiteralCategory.UNIT_MEASURE: UnitMeasureSerializer,
    LiteralCategory.VAT: VatSerializer,
    LiteralCategory.WORKSTATION: WorkstationSerializer,
    LiteralCategory.VOID_REASON: VoidReasonSerializer,
    LiteralCategory.REVENUE_CENTER: RevenueCenterSerializer,
}
