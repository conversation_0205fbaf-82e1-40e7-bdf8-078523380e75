from orders.models import Order, OrderItem, OrderItemVat
from literals.models import Vat
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.db.models import Sum

class OrderService:
    def __init__(self):
        pass
        
    def get_vat_breakdown(self, order: Order):
        """Get consolidated VAT breakdown for entire order"""
        
        return OrderItemVat.objects.filter(
            order_item__order=order
        ).values('vat_rate', 'vat_name').annotate(
            total_vat_amount=Sum('vat_amount'),
            net_amount=Sum('order_item__subtotal')
        ).order_by('vat_rate')
    
    def get_detailed_vat_breakdown(self, order: Order):
        """Get detailed VAT breakdown by order item"""
        breakdown = []
        for item in order.orderitem_set.all():
            item:OrderItem
            item_data:dict = {
                'article_name': item.article.name,
                'quantity': item.quantity,
                'unit_price': item.price,
                'subtotal': item.subtotal,
                'price_includes_vat': item.article.price_with_vat,
                'vat_details': [],
                'total_vat': item.total_vat_amount,
                'line_total': item.line_total
            }
            
            for vat_detail in item.orderitemvat_set.all():
                vat_detail:OrderItemVat
                item_data['vat_details'].append({
                    'vat_name': vat_detail.vat_name,
                    'vat_rate': vat_detail.vat_rate,
                    'vat_amount': vat_detail.vat_amount
                })
            
            breakdown.append(item_data)
        
        return breakdown
    
    def get_vat_summary(self, order: Order):
        """Get summary of VAT totals"""
        
        summary = order.orderitem_set.aggregate(
            subtotal=Sum('subtotal'),
            total_vat=Sum('total_vat_amount'),
            grand_total=Sum('line_total')
        )
        
        vat_breakdown = self.get_vat_breakdown(order)  
        
        return {
            'subtotal': summary['subtotal'] or 0,
            'total_vat_amount': summary['total_vat'] or 0,
            'discount': order.discount,
            'grand_total': (summary['grand_total'] or 0) - order.discount,
            'vat_breakdown': list(vat_breakdown)
        }

    def calculate_order_totals(self, order: Order):
        """Calculate and update order totals"""
        summary = order.orderitem_set.aggregate(
            total_amount=Sum('line_total')
        )
        
        total_amount = summary['total_amount'] or 0
        grand_total = total_amount - order.discount
        
       
        Order.objects.filter(id=order.id).update(
            total_amount=total_amount,
            grand_total=grand_total
        )
        
        # Refresh the instance to reflect the changes
        order.refresh_from_db()
        return order

    def send_order_update(self,order:Order,action:str=None):
        channel_layer = get_channel_layer()
        
        message_data = {
            'id': str(order.id),
            'name': order.table.name,
            'created_at': order.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            'action': action,
            'is_kot':order.is_kot
        }
        
        # Send to group
        async_to_sync(channel_layer.group_send)(
            'orders',
            {
                'type': 'order_update',
                'message': message_data
            }
        )
    
    def calculate_orderitem_totals_and_vat(self, order_item: OrderItem):

        order_item.subtotal = order_item.price * order_item.quantity
        
        # Get all active VATs
        active_vats = Vat.objects.filter(is_active=True)
        total_vat = 0
        
        if active_vats.exists():
            for vat in active_vats:
                vat:Vat
                if order_item.article.price_with_vat:
                    # Price includes VAT - extract VAT amount
                    vat_amount = (order_item.subtotal * vat.rate) / (100 + vat.rate)
                else:
                    # Price excludes VAT - add VAT amount
                    vat_amount = (order_item.subtotal * vat.rate) / 100
                
                total_vat += vat_amount
                
                # Create or update VAT breakdown
                OrderItemVat.objects.update_or_create(
                    order_item=order_item,
                    vat_rate=vat.rate,
                    defaults={
                        'vat_name': vat.name,
                        'vat_amount': vat_amount
                    }
                )
        
        order_item.total_vat_amount = total_vat
        
        if order_item.article.price_with_vat:
            # Price already includes VAT
            order_item.line_total = order_item.subtotal
        else:
            # Add VAT to price
            order_item.line_total = order_item.subtotal + order_item.total_vat_amount
        

        OrderItem.objects.filter(id=order_item.id).update(
            subtotal=order_item.subtotal,
            total_vat_amount=order_item.total_vat_amount,
            line_total=order_item.line_total
        )
        
        # Update the order totals after updating the order item
        self.calculate_order_totals(order_item.order)