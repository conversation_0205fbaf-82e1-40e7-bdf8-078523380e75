from rest_framework.routers import DefaultRouter
from .import views 
from django.urls import path

app_name = 'orders'


router = DefaultRouter()
router.register(r'', views.OrderViewSet,basename='orders')
    
urlpatterns = [
    path('ordered-items/', views.OrderedItemViewSet.as_view({'get': 'list'}), name='ordered-items'),
    path('ordered-items/kot-orders/', views.OrderedItemViewSet.as_view({'get': 'kot_orders_list'}), name='kot-orders-list'),
    path('ordered-items/kot-orders/<uuid:pk>/', views.OrderedItemViewSet.as_view({'patch': 'kot_orders_update'}), name='kot-orders-update'),
    path('ordered-items/<uuid:pk>/', views.OrderedItemViewSet.as_view({'patch': 'partial_update'}), name='ordered-item'),
    path('ordered-items/split-quantity/', views.SplitQuantityCreateView.as_view(), name='split-quantity'),
    path('ordered-items/void/', views.VoidOrderItemCreateView.as_view(), name='void-order-item'),
     path('<uuid:pk>/payments/', 
         views.OrderPaymentListCreateView.as_view(), 
         name='order-payments'),
    path('tips/', views.TipsView.as_view(), name='tips'),
]

urlpatterns += router.urls