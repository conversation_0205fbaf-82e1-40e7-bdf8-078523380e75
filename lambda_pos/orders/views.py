from core.dependency_injection import service_locator
from core.views import PaginateMixin
from core.views import TaggedDecorator
from django.db.models import Min
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import OpenApiParameter
from general.models import AppSetting
from orders.models import OrderItemVoid
from orders.models import Tips
from orders.serializers import OrderCreateSerializer
from orders.serializers import OrderDetailSerializer
from orders.serializers import OrderListSerializer
from orders.serializers import TipsSerializer
from rest_framework import generics
from rest_framework import status
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import AllowAny
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .filters import OrderedItemFilter
from .filters import OrderFilter
from .models import Order
from .models import OrderItem
from .models import OrderPayment
from .serializers import OrderItemSerializer
from .serializers import OrderPaymentCreateSerializer
from .serializers import OrderPaymentSerializer
from .serializers import OrderPaymentSummarySerializer
from .serializers import SplitQuantitySerializer
from .serializers import VoidOrderItemReasonSerializer


class DefaultQueryParamsMixin:
    default_query_params = {}

    def get_query_params_with_defaults(self, extra_defaults=None):
        defaults = {**self.default_query_params}
        if extra_defaults:
            defaults.update(extra_defaults)

        return {**defaults, **self.request.query_params}


class OrderViewSet(
    TaggedDecorator, PaginateMixin, viewsets.ModelViewSet, DefaultQueryParamsMixin
):
    permission_classes = [IsAuthenticated]
    # serializer_class = OrderSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = OrderFilter
    queryset = Order.objects.order_by("-created_at")

    def get_serializer_class(self):
        serializer_map = {
            "create": OrderCreateSerializer,
            "retrieve": OrderDetailSerializer,
            "list": OrderListSerializer,
            "update": OrderCreateSerializer,
            "partial_update": OrderCreateSerializer,
        }
        return serializer_map.get(self.action, self.serializer_class)

    def get_queryset(self):
        if not service_locator.role_service.can_view_all_tables(self.request.user):
            return self.queryset.filter(created_by=self.request.user)

        return self.queryset

    @action(
        detail=False,
        methods=["get"],
        authentication_classes=[],
        permission_classes=[AllowAny],
        serializer_class=OrderListSerializer,
        url_path="kot-orders",
    )
    def kot_orders_list(self, request, *args, **kwargs):
        orders = (
            Order.objects.filter(status__in=[Order.STATUS.SENT, Order.STATUS.PRINTED])
            .annotate(earliest_item_created=Min("orderitem__created_at"))
            .order_by("-created_at", "earliest_item_created")
        )

        defaults = {
            "include_voided": "true",
            "printed_and_sent_orders": "true",
        }

        query_params = self.get_query_params_with_defaults(defaults)
        orders = OrderFilter(
            data=query_params, queryset=orders, request=self.request
        ).qs

        serializer = self.get_serializer(orders, many=True)

        data = {
            "app_setting": AppSetting.objects.values().first(),
            "orders": serializer.data,
        }
        return Response(data)

    @extend_schema(
        description="Mark a KOT order as prepared.",
        responses={200: {"status": "success"}},
    )
    @action(
        detail=True,
        methods=["patch"],
        authentication_classes=[],
        permission_classes=[AllowAny],
        url_path="kot-orders",
    )
    def kot_orders_update(self, request, pk=None, *args, **kwargs):

        try:
            item = Order.objects.get(id=pk)
            item.status = Order.STATUS.PREPARED
            item.save()
            return Response({"status": "success"})
        except OrderItem.DoesNotExist:
            return Response({"error": "Order item not found"}, status=404)
        except Exception as e:
            return Response({"error": str(e)}, status=500)

    def get_permissions(self):
        if self.action in ["kot_orders_list", "kot_orders_update"]:
            return [AllowAny()]
        return super().get_permissions()

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="include_voided",
                location=OpenApiParameter.QUERY,
                required=False,
                type=bool,
                description="include voided items",
            )
        ],
        responses={200: OrderListSerializer(many=True)},
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)


class OrderedItemViewSet(TaggedDecorator, PaginateMixin, viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = OrderItem.objects.select_related("order").order_by("-created_at")
    serializer_class = OrderItemSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = OrderedItemFilter

    @action(
        detail=False,
        methods=["get"],
        authentication_classes=[],
        permission_classes=[AllowAny],
        url_path="kot-orders",
    )
    def kot_orders_list(self, request, *args, **kwargs):

        orders = self.get_queryset().filter(order__status=Order.STATUS.OPENED)
        orders = self.filter_queryset(orders)
        serializer = self.get_serializer(orders, many=True)
        return Response(serializer.data)

    @extend_schema(
        description="Mark a KOT order item as done.",
        responses={200: {"status": "success"}},
    )
    @action(
        detail=True,
        methods=["patch"],
        authentication_classes=[],
        permission_classes=[AllowAny],
        url_path="kot-orders",
    )
    def kot_orders_update(self, request, pk=None, *args, **kwargs):
        if pk is None:
            return Response({"error": "Order item ID not provided"}, status=400)

        try:
            item = OrderItem.objects.get(id=pk)
            item.status = OrderItem.STATUS.DONE
            item.save()
            return Response({"status": "success"})
        except OrderItem.DoesNotExist:
            return Response({"error": "Order item not found"}, status=404)
        except Exception as e:
            return Response({"error": str(e)}, status=500)

    def get_permissions(self):
        if self.action in ["kot_orders_list", "kot_orders_update"]:
            return [AllowAny()]
        return super().get_permissions()


class SplitQuantityCreateView(TaggedDecorator, CreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SplitQuantitySerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        results = serializer.save()
        return Response(data=results, status=status.HTTP_201_CREATED)


class VoidOrderItemCreateView(TaggedDecorator, CreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = VoidOrderItemReasonSerializer
    queryset = OrderItemVoid.objects.order_by("-created_at")


class OrderPaymentListCreateView(TaggedDecorator, generics.ListCreateAPIView):

    permission_classes = [IsAuthenticated]

    def get_queryset(self):

        order_id = self.kwargs["pk"]
        return OrderPayment.objects.filter(order_id=order_id).order_by("-created_at")

    def get_serializer_class(self):
        if self.request.method == "POST":
            return OrderPaymentCreateSerializer
        return OrderPaymentSerializer

    def create(self, request, *args, **kwargs):
        order = get_object_or_404(Order, id=self.kwargs["pk"])
        serializer = self.get_serializer(
            data=request.data, context={"request": request, "order": order}
        )
        serializer.is_valid(raise_exception=True)

        result = serializer.save()
        order_serializer = OrderPaymentSummarySerializer(result["order"])

        return Response(data=order_serializer.data, status=status.HTTP_201_CREATED)


class TipsView(TaggedDecorator, generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Tips.objects.order_by("created_at")
    serializer_class = TipsSerializer
