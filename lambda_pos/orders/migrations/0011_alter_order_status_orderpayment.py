# Generated by Django 5.2.3 on 2025-07-03 01:40

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0010_order_workstation'),
        ('payment_method', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='status',
            field=models.CharField(choices=[('Printed', 'Printed'), ('Sent', 'Sent'), ('Prepared', 'Prepared'), ('Paid', 'Paid'), ('Voided', 'Voided'), ('Partially Paid', 'Partially Paid')], default='Printed', max_length=20),
        ),
        migrations.CreateModel(
            name='OrderPayment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
                ('payment_method', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='payment_method.paymentmethod')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
