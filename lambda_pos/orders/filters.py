from core.filters import DateRangeFilterMixin
from django.db.models import QuerySet
from django_filters import rest_framework as filters

from .models import Order
from .models import OrderItem


class OrderFilter(DateRangeFilterMixin):
    statuses = filters.MultipleChoiceFilter(
        field_name="status", label="Statuses", choices=Order.STATUS.CHOICES
    )
    exclude_voided_items = filters.BooleanFilter(
        method="filter_exclude_voided_items", label="Exclude orders with voided items"
    )
    printed_and_sent_orders = filters.BooleanFilter(
        method="filter_printed_and_sent_orders", label="Printed and sent orders"
    )

    class Meta:
        model = Order
        fields = [
            "statuses",
            "table",
            "created_by",
            "status",
            "serving_period",
            "workstation",
            "revenue_center",
        ]

    def filter_printed_and_sent_orders(self, queryset: QuerySet, name, value):
        if value:
            return queryset.filter(status__in=[Order.STATUS.SENT, Order.STATUS.PRINTED])
        return queryset

    def filter_exclude_voided_items(self, queryset, name, value):
        if value:
            return queryset.exclude(orderitem__status=OrderItem.STATUS.VOIDED)
        return queryset


class OrderedItemFilter(filters.FilterSet):
    order_status = filters.ChoiceFilter(
        method="filter_order_status", label="Order Status", choices=Order.STATUS.CHOICES
    )

    class Meta:
        model = OrderItem
        fields = ["status", "order", "article"]

    def filter_order_status(self, queryset, name, value):
        if value:
            return queryset.filter(order__status=value)
        return queryset
