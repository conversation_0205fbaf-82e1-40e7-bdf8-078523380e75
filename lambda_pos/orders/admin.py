from django.contrib import admin

from .models import Order
from .models import OrderItem
from .models import OrderItemVat
from .models import OrderItemVoid
from .models import OrderMessage


admin.site.register(OrderMessage)


class OrderItemVatInline(admin.StackedInline):
    model = OrderItemVat
    extra = 1
    can_delete = False
    readonly_fields = ["vat_name", "vat_rate", "vat_amount"]


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    inlines = [OrderItemVatInline]
    list_display = [
        "article",
        "quantity",
        "price",
        "subtotal",
        "total_vat_amount",
        "line_total",
    ]
    search_fields = ["article__name"]
    list_filter = ["article__sub_family", "article__product_type", "status"]
    readonly_fields = ["subtotal", "total_vat_amount", "line_total"]


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ["table", "status", "total_amount", "discount", "grand_total"]
    search_fields = ["table__name"]
    list_filter = ["status", "created_at"]
    readonly_fields = ["total_amount", "discount", "grand_total"]


@admin.register(OrderItemVoid)
class OrderItemVoidAdmin(admin.ModelAdmin):
    list_display = [
        "order_item",
        "reason",
        "original_quantity",
        "voided_quantity",
        "voided_by",
    ]
    list_filter = ["reason"]
    readonly_fields = [
        "order_item",
        "reason",
        "original_quantity",
        "voided_quantity",
        "voided_by",
    ]
