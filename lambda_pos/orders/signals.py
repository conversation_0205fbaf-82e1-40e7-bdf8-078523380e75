from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Order,OrderItem



@receiver(post_save, sender=Order)  
@receiver(post_save, sender=OrderItem)
def send_order_or_orderitem_saved(sender, instance, created, **kwargs):
    from core.dependency_injection import service_locator

    
    if sender == Order:
        action = 'created' if created else 'updated'
        service_locator.order_service.send_order_update(instance, action=action)
    
    elif sender == OrderItem:
        if not created and instance.status == OrderItem.STATUS.VOIDED:
            service_locator.order_service.send_order_update(instance.order, action='updated')
    
  


@receiver(post_save, sender=OrderItem)
def calculate_orderitem_totals(sender, instance:OrderItem, created, update_fields, **kwargs):
    from core.dependency_injection import service_locator
    if instance.status == OrderItem.STATUS.VOIDED:
        return
    service_locator.order_service.calculate_orderitem_totals_and_vat(instance)




def init_signals():
    pass