import uuid

from accounts.models import User
from core.models import BaseModel
from django.db import models
from literals.models import RevenueCenter


class Table(BaseModel):
    class STATUS:
        OPENED = "Opened"
        CLOSED = "Closed"
        DISPOSED = "Disposed"
        VOIDED = "Voided"

        CHOICES = [
            (OPENED, "Opened"),
            (CLOS<PERSON>, "Closed"),
            (DISPOSED, "Disposed"),
            (VOIDED, "Voided"),
        ]
        ALL = [OPENED, CLOSED, DISPOSED, VOIDED]

    code = models.Char<PERSON>ield(max_length=20, null=True, blank=True)
    name = models.Char<PERSON>ield(max_length=20)
    seats = models.IntegerField(null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=10, choices=STATUS.CHOICES, default=STATUS.OPENED
    )
    phone = models.CharField(max_length=20, null=True, blank=True)
    splited_from = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, blank=True
    )
    revenue_center = models.ForeignKey(
        RevenueCenter, on_delete=models.CASCADE, null=True
    )

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = f"CHK{uuid.uuid4().hex[:8].upper()}"
        super().save(*args, **kwargs)

    def update_status(self, status):
        self.status = status
        self.save()

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return self.name
