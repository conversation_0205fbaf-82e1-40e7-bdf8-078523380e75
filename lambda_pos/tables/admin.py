from django.contrib import admin
from .models import Table
# Register your models here.




class TableAdmin(admin.ModelAdmin):
    list_display = ["name", "seats", "status", "created_by", "code", "created_at", "updated_at"]
    list_filter = ["status", "created_by"]
    search_fields = ["name", "code"]
    list_editable = ["status"]
    list_display_links = ["name", "code"]
    list_select_related = ["created_by"]
    list_filter = ["status", "created_by", "created_at"]

admin.site.register(Table, TableAdmin)