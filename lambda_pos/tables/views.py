from django.shortcuts import render
from rest_framework import viewsets
from .models import Table
from .serializers import TableSerializer
from rest_framework.permissions import IsAuthenticated
from core.dependency_injection import service_locator
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.pagination import PageNumberPagination
from rest_framework.request import Request
from .filters import TableFilter
from .api_docs import FILTER_TABLE_SPECTACULAR_DOCS
from core.views import TaggedDecorator


class TableViewSet(TaggedDecorator,viewsets.ModelViewSet):
    class OptionalPageNumberPagination(PageNumberPagination):
        def get_page_size(self, request: Request):
            if request.query_params.get("paginate", "true").lower() == "false":
                return None
            return super().get_page_size(request)
        
    permission_classes = [IsAuthenticated]
    queryset = Table.objects.order_by('-created_at')
    serializer_class = TableSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = TableFilter
    pagination_class = OptionalPageNumberPagination
    def get_queryset(self):
        if not service_locator.role_service.can_view_all_tables(self.request.user):
            return Table.objects.filter(created_by=self.request.user)
  
        return Table.objects.all()
    
    @FILTER_TABLE_SPECTACULAR_DOCS
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    
    

  