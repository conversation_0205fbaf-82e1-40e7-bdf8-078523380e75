/*!
 * Copyright (c) 2021 Momo Bassit.
 * Licensed under the MIT License (MIT)
 * https://github.com/mdbassit/Coloris
 */
!function(u,p,s,c){var d,f,h,i,b,y,v,m,g,l,w,k,L,E,a,n,r=p.createElement("canvas").getContext("2d"),x={r:0,g:0,b:0,h:0,s:0,v:0,a:1},A={},C={el:"[data-coloris]",parent:"body",theme:"default",themeMode:"light",rtl:!1,wrap:!0,margin:2,format:"hex",formatToggle:!1,swatches:[],swatchesOnly:!1,alpha:!0,forceAlpha:!1,focusInput:!0,selectInput:!1,inline:!1,defaultColor:"#000000",clearButton:!1,clearLabel:"Clear",closeButton:!1,closeLabel:"Close",onChange:function(){return c},a11y:{open:"Open color picker",close:"Close color picker",clear:"Clear the selected color",marker:"Saturation: {s}. Brightness: {v}.",hueSlider:"Hue slider",alphaSlider:"Opacity slider",input:"Color value field",format:"Color format",swatch:"Color swatch",instruction:"Saturation and brightness selector. Use up, down, left and right arrow keys to select."}},o={},S="",T={},B=!1;function M(t){if("object"==typeof t)for(var e in t)switch(e){case"el":D(t.el),!1!==t.wrap&&R(t.el);break;case"parent":(d=t.parent instanceof HTMLElement?t.parent:p.querySelector(t.parent))&&(d.appendChild(f),C.parent=t.parent,d===p.body&&(d=c));break;case"themeMode":C.themeMode=t.themeMode,"auto"===t.themeMode&&u.matchMedia&&u.matchMedia("(prefers-color-scheme: dark)").matches&&(C.themeMode="dark");case"theme":t.theme&&(C.theme=t.theme),f.className="clr-picker clr-"+C.theme+" clr-"+C.themeMode,C.inline&&j();break;case"rtl":C.rtl=!!t.rtl,Array.from(p.getElementsByClassName("clr-field")).forEach(function(e){return e.classList.toggle("clr-rtl",C.rtl)});break;case"margin":t.margin*=1,C.margin=(isNaN(t.margin)?C:t).margin;break;case"wrap":t.el&&t.wrap&&R(t.el);break;case"formatToggle":C.formatToggle=!!t.formatToggle,V("clr-format").style.display=C.formatToggle?"block":"none",C.formatToggle&&(C.format="auto");break;case"swatches":Array.isArray(t.swatches)&&function(){var e=V("clr-swatches"),l=p.createElement("div");e.textContent="",t.swatches.forEach(function(e,t){var a=p.createElement("button");a.setAttribute("type","button"),a.setAttribute("id","clr-swatch-"+t),a.setAttribute("aria-labelledby","clr-swatch-label clr-swatch-"+t),a.style.color=e,a.textContent=e,l.appendChild(a)}),t.swatches.length&&e.appendChild(l),C.swatches=t.swatches.slice()}();break;case"swatchesOnly":C.swatchesOnly=!!t.swatchesOnly,f.setAttribute("data-minimal",C.swatchesOnly);break;case"alpha":C.alpha=!!t.alpha,f.setAttribute("data-alpha",C.alpha);break;case"inline":C.inline=!!t.inline,f.setAttribute("data-inline",C.inline),C.inline&&(l=t.defaultColor||C.defaultColor,E=P(l),j(),Y(l));break;case"clearButton":"object"==typeof t.clearButton&&(t.clearButton.label&&(C.clearLabel=t.clearButton.label,v.innerHTML=C.clearLabel),t.clearButton=t.clearButton.show),C.clearButton=!!t.clearButton,v.style.display=C.clearButton?"block":"none";break;case"clearLabel":C.clearLabel=t.clearLabel,v.innerHTML=C.clearLabel;break;case"closeButton":C.closeButton=!!t.closeButton,C.closeButton?f.insertBefore(m,b):b.appendChild(m);break;case"closeLabel":C.closeLabel=t.closeLabel,m.innerHTML=C.closeLabel;break;case"a11y":var a,l,r=t.a11y,n=!1;if("object"==typeof r)for(var o in r)r[o]&&C.a11y[o]&&(C.a11y[o]=r[o],n=!0);n&&(a=V("clr-open-label"),l=V("clr-swatch-label"),a.innerHTML=C.a11y.open,l.innerHTML=C.a11y.swatch,m.setAttribute("aria-label",C.a11y.close),v.setAttribute("aria-label",C.a11y.clear),g.setAttribute("aria-label",C.a11y.hueSlider),w.setAttribute("aria-label",C.a11y.alphaSlider),y.setAttribute("aria-label",C.a11y.input),h.setAttribute("aria-label",C.a11y.instruction));break;default:C[e]=t[e]}}function H(e,t){"string"==typeof e&&"object"==typeof t&&(o[e]=t,B=!0)}function N(e){delete o[e],0===Object.keys(o).length&&(B=!1,e===S&&O())}function t(l){if(B){var e,r=["el","wrap","rtl","inline","defaultColor","a11y"];for(e in o)if("break"===function(e){var t=o[e];if(l.matches(e)){for(var a in S=e,T={},r.forEach(function(e){return delete t[e]}),t)T[a]=Array.isArray(C[a])?C[a].slice():C[a];return M(t),"break"}}(e))break}}function O(){0<Object.keys(T).length&&(M(T),S="",T={})}function D(e){e instanceof HTMLElement&&(e=[e]),Array.isArray(e)?e.forEach(function(e){Z(e,"click",I),Z(e,"input",q)}):(Z(p,"click",e,I),Z(p,"input",e,q))}function I(e){C.inline||(t(e.target),L=e.target,a=L.value,E=P(a),f.classList.add("clr-open"),j(),Y(a),(C.focusInput||C.selectInput)&&(y.focus({preventScroll:!0}),y.setSelectionRange(L.selectionStart,L.selectionEnd)),C.selectInput&&y.select(),(n||C.swatchesOnly)&&Q().shift().focus(),L.dispatchEvent(new Event("open",{bubbles:!0})))}function j(){var e,t,a,l,r=d,n=u.scrollY,o=f.offsetWidth,c=f.offsetHeight,i={left:!1,top:!1},s={x:0,y:0};r&&(a=u.getComputedStyle(r),e=parseFloat(a.marginTop),l=parseFloat(a.borderTopWidth),(s=r.getBoundingClientRect()).y+=l+n),C.inline||(a=(t=L.getBoundingClientRect()).x,l=n+t.y+t.height+C.margin,r?(a-=s.x,l-=s.y,a+o>r.clientWidth&&(a+=t.width-o,i.left=!0),l+c>r.clientHeight-e&&c+C.margin<=t.top-(s.y-n)&&(l-=t.height+c+2*C.margin,i.top=!0),l+=r.scrollTop):(a+o>p.documentElement.clientWidth&&(a+=t.width-o,i.left=!0),l+c-n>p.documentElement.clientHeight&&c+C.margin<=t.top&&(l=n+t.y-c-C.margin,i.top=!0)),f.classList.toggle("clr-left",i.left),f.classList.toggle("clr-top",i.top),f.style.left=a+"px",f.style.top=l+"px",s.x+=f.offsetLeft,s.y+=f.offsetTop),A={width:h.offsetWidth,height:h.offsetHeight,x:h.offsetLeft+s.x,y:h.offsetTop+s.y}}function R(e){e instanceof HTMLElement?W(e):(Array.isArray(e)?e:p.querySelectorAll(e)).forEach(W)}function W(e){var t,a,l=e.parentNode;l.classList.contains("clr-field")||(t=p.createElement("div"),a="clr-field",(C.rtl||e.classList.contains("clr-rtl"))&&(a+=" clr-rtl"),t.innerHTML='<button type="button" aria-labelledby="clr-open-label"></button>',l.insertBefore(t,e),t.className=a,t.style.color=e.value,t.appendChild(e))}function q(e){var t=e.target.parentNode;t.classList.contains("clr-field")&&(t.style.color=e.target.value)}function F(e){var t;L&&!C.inline&&(t=L,e&&(L=c,a!==t.value&&(t.value=a,t.dispatchEvent(new Event("input",{bubbles:!0})))),setTimeout(function(){a!==t.value&&t.dispatchEvent(new Event("change",{bubbles:!0}))}),f.classList.remove("clr-open"),B&&O(),t.dispatchEvent(new Event("close",{bubbles:!0})),C.focusInput&&t.focus({preventScroll:!0}),L=c)}function Y(e){var t=function(e){r.fillStyle="#000",r.fillStyle=e,e=(e=/^((rgba)|rgb)[\D]+([\d.]+)[\D]+([\d.]+)[\D]+([\d.]+)[\D]*?([\d.]+|$)/i.exec(r.fillStyle))?{r:+e[3],g:+e[4],b:+e[5],a:+e[6]}:(e=r.fillStyle.replace("#","").match(/.{2}/g).map(function(e){return parseInt(e,16)}),{r:e[0],g:e[1],b:e[2],a:1});return e}(e),e=function(e){var t=e.r/255,a=e.g/255,l=e.b/255,r=s.max(t,a,l),n=s.min(t,a,l),o=r-n,c=r,i=0,n=0;o&&(r===t&&(i=(a-l)/o),r===a&&(i=2+(l-t)/o),r===l&&(i=4+(t-a)/o),r&&(n=o/r));return{h:(i=s.floor(60*i))<0?i+360:i,s:s.round(100*n),v:s.round(100*c),a:e.a}}(t);G(e.s,e.v),z(t,e),g.value=e.h,f.style.color="hsl("+e.h+", 100%, 50%)",l.style.left=e.h/360*100+"%",i.style.left=A.width*e.s/100+"px",i.style.top=A.height-A.height*e.v/100+"px",w.value=100*e.a,k.style.left=100*e.a+"%"}function P(e){e=e.substring(0,3).toLowerCase();return"rgb"===e||"hsl"===e?e:"hex"}function U(e){e=e!==c?e:y.value,L&&(L.value=e,L.dispatchEvent(new Event("input",{bubbles:!0}))),C.onChange&&C.onChange.call(u,e,L),p.dispatchEvent(new CustomEvent("coloris:pick",{detail:{color:e,currentEl:L}}))}function X(e,t){e={h:+g.value,s:e/A.width*100,v:100-t/A.height*100,a:w.value/100},t=function(e){var t=e.s/100,a=e.v/100,l=t*a,r=e.h/60,n=l*(1-s.abs(r%2-1)),o=a-l;l+=o,n+=o;t=s.floor(r)%6,a=[l,n,o,o,n,l][t],r=[n,l,l,n,o,o][t],t=[o,o,n,l,l,n][t];return{r:s.round(255*a),g:s.round(255*r),b:s.round(255*t),a:e.a}}(e);G(e.s,e.v),z(t,e),U()}function G(e,t){var a=C.a11y.marker;e=+e.toFixed(1),t=+t.toFixed(1),a=(a=a.replace("{s}",e)).replace("{v}",t),i.setAttribute("aria-label",a)}function K(e){var t={pageX:((a=e).changedTouches?a.changedTouches[0]:a).pageX,pageY:(a.changedTouches?a.changedTouches[0]:a).pageY},a=t.pageX-A.x,t=t.pageY-A.y;d&&(t+=d.scrollTop),$(a,t),e.preventDefault(),e.stopPropagation()}function $(e,t){e=e<0?0:e>A.width?A.width:e,t=t<0?0:t>A.height?A.height:t,i.style.left=e+"px",i.style.top=t+"px",X(e,t),i.focus()}function z(e,t){void 0===t&&(t={});var a,l,r=C.format;for(a in e=void 0===e?{}:e)x[a]=e[a];for(l in t)x[l]=t[l];var n,o=function(e){var t=e.r.toString(16),a=e.g.toString(16),l=e.b.toString(16),r="";e.r<16&&(t="0"+t);e.g<16&&(a="0"+a);e.b<16&&(l="0"+l);C.alpha&&(e.a<1||C.forceAlpha)&&(e=255*e.a|0,r=e.toString(16),e<16&&(r="0"+r));return"#"+t+a+l+r}(x),c=o.substring(0,7);switch(i.style.color=c,k.parentNode.style.color=c,k.style.color=o,b.style.color=o,h.style.display="none",h.offsetHeight,h.style.display="",k.nextElementSibling.style.display="none",k.nextElementSibling.offsetHeight,k.nextElementSibling.style.display="","mixed"===r?r=1===x.a?"hex":"rgb":"auto"===r&&(r=E),r){case"hex":y.value=o;break;case"rgb":y.value=(n=x,!C.alpha||1===n.a&&!C.forceAlpha?"rgb("+n.r+", "+n.g+", "+n.b+")":"rgba("+n.r+", "+n.g+", "+n.b+", "+n.a+")");break;case"hsl":y.value=(n=function(e){var t,a=e.v/100,l=a*(1-e.s/100/2);0<l&&l<1&&(t=s.round((a-l)/s.min(l,1-l)*100));return{h:e.h,s:t||0,l:s.round(100*l),a:e.a}}(x),!C.alpha||1===n.a&&!C.forceAlpha?"hsl("+n.h+", "+n.s+"%, "+n.l+"%)":"hsla("+n.h+", "+n.s+"%, "+n.l+"%, "+n.a+")")}p.querySelector('.clr-format [value="'+r+'"]').checked=!0}function e(){var e=+g.value,t=+i.style.left.replace("px",""),a=+i.style.top.replace("px","");f.style.color="hsl("+e+", 100%, 50%)",l.style.left=e/360*100+"%",X(t,a)}function J(){var e=w.value/100;k.style.left=100*e+"%",z({a:e}),U()}function Q(){return Array.from(f.querySelectorAll("input, button")).filter(function(e){return!!e.offsetWidth})}function V(e){return p.getElementById(e)}function Z(e,t,a,l){var r=Element.prototype.matches||Element.prototype.msMatchesSelector;"string"==typeof a?e.addEventListener(t,function(e){r.call(e.target,a)&&l.call(e.target,e)}):(l=a,e.addEventListener(t,l))}function _(e,t){t=t!==c?t:[],"loading"!==p.readyState?e.apply(void 0,t):p.addEventListener("DOMContentLoaded",function(){e.apply(void 0,t)})}NodeList!==c&&NodeList.prototype&&!NodeList.prototype.forEach&&(NodeList.prototype.forEach=Array.prototype.forEach),u.Coloris=function(){var r={set:M,wrap:R,close:F,setInstance:H,removeInstance:N,updatePosition:j,ready:_};function e(e){_(function(){e&&("string"==typeof e?D:M)(e)})}for(var t in r)!function(l){e[l]=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];_(r[l],t)}}(t);return e}(),_(function(){d=c,(f=p.createElement("div")).setAttribute("id","clr-picker"),f.className="clr-picker",f.innerHTML='<input id="clr-color-value" name="clr-color-value" class="clr-color" type="text" value="" spellcheck="false" aria-label="'+C.a11y.input+'"><div id="clr-color-area" class="clr-gradient" role="application" aria-label="'+C.a11y.instruction+'"><div id="clr-color-marker" class="clr-marker" tabindex="0"></div></div><div class="clr-hue"><input id="clr-hue-slider" name="clr-hue-slider" type="range" min="0" max="360" step="1" aria-label="'+C.a11y.hueSlider+'"><div id="clr-hue-marker"></div></div><div class="clr-alpha"><input id="clr-alpha-slider" name="clr-alpha-slider" type="range" min="0" max="100" step="1" aria-label="'+C.a11y.alphaSlider+'"><div id="clr-alpha-marker"></div><span></span></div><div id="clr-format" class="clr-format"><fieldset class="clr-segmented"><legend>'+C.a11y.format+'</legend><input id="clr-f1" type="radio" name="clr-format" value="hex"><label for="clr-f1">Hex</label><input id="clr-f2" type="radio" name="clr-format" value="rgb"><label for="clr-f2">RGB</label><input id="clr-f3" type="radio" name="clr-format" value="hsl"><label for="clr-f3">HSL</label><span></span></fieldset></div><div id="clr-swatches" class="clr-swatches"></div><button type="button" id="clr-clear" class="clr-clear" aria-label="'+C.a11y.clear+'">'+C.clearLabel+'</button><div id="clr-color-preview" class="clr-preview"><button type="button" id="clr-close" class="clr-close" aria-label="'+C.a11y.close+'">'+C.closeLabel+'</button></div><span id="clr-open-label" hidden>'+C.a11y.open+'</span><span id="clr-swatch-label" hidden>'+C.a11y.swatch+"</span>",p.body.appendChild(f),h=V("clr-color-area"),i=V("clr-color-marker"),v=V("clr-clear"),m=V("clr-close"),b=V("clr-color-preview"),y=V("clr-color-value"),g=V("clr-hue-slider"),l=V("clr-hue-marker"),w=V("clr-alpha-slider"),k=V("clr-alpha-marker"),D(C.el),R(C.el),Z(f,"mousedown",function(e){f.classList.remove("clr-keyboard-nav"),e.stopPropagation()}),Z(h,"mousedown",function(e){Z(p,"mousemove",K)}),Z(h,"contextmenu",function(e){e.preventDefault()}),Z(h,"touchstart",function(e){p.addEventListener("touchmove",K,{passive:!1})}),Z(i,"mousedown",function(e){Z(p,"mousemove",K)}),Z(i,"touchstart",function(e){p.addEventListener("touchmove",K,{passive:!1})}),Z(y,"change",function(e){var t=y.value;(L||C.inline)&&U(""===t?t:Y(t))}),Z(v,"click",function(e){U(""),F()}),Z(m,"click",function(e){U(),F()}),Z(V("clr-format"),"click",".clr-format input",function(e){E=e.target.value,z(),U()}),Z(f,"click",".clr-swatches button",function(e){Y(e.target.textContent),U(),C.swatchesOnly&&F()}),Z(p,"mouseup",function(e){p.removeEventListener("mousemove",K)}),Z(p,"touchend",function(e){p.removeEventListener("touchmove",K)}),Z(p,"mousedown",function(e){n=!1,f.classList.remove("clr-keyboard-nav"),F()}),Z(p,"keydown",function(e){var t,a=e.key,l=e.target,r=e.shiftKey;"Escape"===a?F(!0):["Tab","ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(a)&&(n=!0,f.classList.add("clr-keyboard-nav")),"Tab"===a&&l.matches(".clr-picker *")&&(a=(t=Q()).shift(),t=t.pop(),r&&l===a?(t.focus(),e.preventDefault()):r||l!==t||(a.focus(),e.preventDefault()))}),Z(p,"click",".clr-field button",function(e){B&&O(),e.target.nextElementSibling.dispatchEvent(new Event("click",{bubbles:!0}))}),Z(i,"keydown",function(e){var t={ArrowUp:[0,-1],ArrowDown:[0,1],ArrowLeft:[-1,0],ArrowRight:[1,0]};Object.keys(t).includes(e.key)&&(!function(e,t){$(+i.style.left.replace("px","")+e,+i.style.top.replace("px","")+t)}.apply(void 0,t[e.key]),e.preventDefault())}),Z(h,"click",K),Z(g,"input",e),Z(w,"input",J)})}(window,document,Math);
