from accounts.models import User
from drf_extra_fields.fields import Base64I<PERSON><PERSON>ield
from rest_framework import serializers
from user_roles.models import Role
from user_roles.models import UserRole
from user_roles.serializers import RoleSerializer



class BaseUserSerializer(serializers.Serializer):
   
   
    image_size = serializers.SerializerMethodField()

  

    def get_image_size(self, obj: User):
        try:
            if hasattr(obj, "image") and obj.image:
                return obj.image.size
        except Exception:
            return


class UserSerializer(BaseUserSerializer, serializers.ModelSerializer):

    image = Base64ImageField(required=False)
    code = serializers.IntegerField(min_value=1000, max_value=9999)
    role = serializers.ChoiceField(
        choices=Role.ROLES.CHOICES, required=False, default=Role.ROLES.WAITER
    )

    class Meta:
        model = User

        exclude = [
            "groups",
            "user_permissions",
            "is_staff",
            "is_superuser",
            "username",
            "password",
        ]

        extra_kwargs = {
            "email": {"required": False},
        }
        read_only_fields = ["date_joined", "id"]

    
    def create(self, validated_data):
        role = validated_data.pop("role", None)
        code = validated_data.pop("code", None)
        validated_data["username"] = code
       
        user: User = User.objects.create(**validated_data)

        if role:
            user.set_role(role)

        return user





