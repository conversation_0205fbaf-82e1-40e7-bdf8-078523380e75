from literals.factories import DeviceTypeFactory, DeviceBrandFactory
import random
import factory
from accounts.models import User, RepairerDetails
from faker import Faker
fake = Faker()


class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User
        django_get_or_create = ("email",)

    email = factory.Faker("email")


class RepairerDetailsFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = RepairerDetails

    user = factory.SubFactory(UserFactory)

    skills = factory.LazyAttribute(
        lambda _: [fake.word() for _ in range(random.randint(1, 5))])

    services_offered = factory.LazyAttribute(
        lambda _: [fake.word() for _ in range(random.randint(1, 5))])

    device_brand = factory.SubFactory(DeviceBrandFactory)

    @factory.post_generation
    def device_type(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for device_type in extracted:
                self.device_type.add(device_type)
        else:
            num_types = random.randint(1, 3)
            device_types = DeviceTypeFactory.create_batch(num_types)
            self.device_type.add(*device_types)
