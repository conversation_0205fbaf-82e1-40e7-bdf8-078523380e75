from accounts import models
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin


@admin.register(models.User)
class CustomUserAdmin(UserAdmin):

    list_display = (
        "email",
        "first_name",
        "last_name",
        "address",
        "is_staff",
        "is_active",
        "mobile",
        "is_superuser",
        "username",
        "mobile",

    )
    prepopulated_fields = {"username": ["email"]}
    list_filter = ("is_staff", "is_active", "is_superuser")

    search_fields = ("email", "first_name", "last_name")

    ordering = ["email"]

    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "first_name",
                    "last_name",
                    "email",
                    "username",
                    "password1",
                    "password2",

                ),
            },
        ),
    )

    fieldsets = (
        ("Login Info", {"fields": ("email", "username", "password")}),
        (
            "Personal info",
            {"fields": ("first_name", "last_name", "address", "mobile")},
        ),
        (
            "Permissions",
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                )
            },
        ),
        ("Important dates", {"fields": ("last_login", "date_joined")}),

    )
