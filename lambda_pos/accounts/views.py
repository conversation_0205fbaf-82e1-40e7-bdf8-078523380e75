from accounts.models import User
from core.views import PaginateMixin
from core.views import TaggedDecorator
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

from .filters import UserFilter
from .serializers import UserSerializer


class UserSerializerView(TaggedDecorator, PaginateMixin, viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = User.objects.all()
    serializer_class = UserSerializer
    filterset_class = UserFilter
