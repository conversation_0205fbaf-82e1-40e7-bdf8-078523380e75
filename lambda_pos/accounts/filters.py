import django_filters
from accounts.models import User
from django_filters import rest_framework as filters
from user_roles.models import Role
from django.db.models import QuerySet
from django.db.models import Q


class UserFilter(filters.FilterSet):

    role = filters.ChoiceFilter(
        choices=Role.ROLES.CHOICES,
        method="filter_role",
        label="Role",
    )

    search = django_filters.CharFilter(
        method='filter_search', label='Search', required=False)

    class Meta:
        model = User
        fields = [
            "mobile",
            "email",
            "is_active",
            "search",
            "first_name",
            "last_name",
            "username",
            "address",
        ]

    def filter_role(self, queryset: QuerySet, name, value):

        if value:

            return queryset.filter(userrole__role__name=value)
        return queryset

    def filter_search(self, queryset: QuerySet, name, value):
        if value:

            return queryset.filter(
                Q(first_name__icontains=value) |
                Q(last_name__icontains=value) |
                Q(username__icontains=value)

            )
        return queryset


