import logging
import socket
import typing
from datetime import datetime
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple

import serial
from django.core.exceptions import ObjectDoesNotExist

from .models import PrintingRule

if typing.TYPE_CHECKING:
    from orders.models import Order
    from literals.models import Printer
    from orders.models import OrderItem

logger = logging.getLogger(__name__)


class PrinterConnectionError(Exception):
    pass


class PrinterDataError(Exception):
    pass


class PrintingRuleNotFoundError(Exception):
    pass


class ThermalPrintService:
    # ESC/POS commands for thermal printers
    ESC = b"\x1b"
    INIT_PRINTER = ESC + b"@"
    CUT_PAPER = ESC + b"i"
    FEED_LINE = b"\n"
    BOLD_ON = ESC + b"E\x01"
    BOLD_OFF = ESC + b"E\x00"
    CENTER_ALIGN = ESC + b"a\x01"
    LEFT_ALIGN = ESC + b"a\x00"
    NORMAL_SIZE = ESC + b"!\x00"
    SMALL_FONT = ESC + b"M\x01"
    NORMAL_FONT = ESC + b"M\x00"

    # Print settings
    DEFAULT_WIDTH = 32
    DEFAULT_TIMEOUT = 5
    DEFAULT_BAUD_RATE = 9600

    def __init__(self, timeout: int = DEFAULT_TIMEOUT):
        self.timeout = timeout

    # ======================== MAIN PRINTING METHODS ========================

    def print_order_receipt(self, order: "Order", workstation=None) -> Dict[str, bool]:
        """
        Print customer receipt for an order (all items together on workstation printer)

        Args:
            order: The Order object to print (will access order.orderitem_set)
            workstation: Optional workstation to determine printer

        Returns:
            Dict mapping printer name to success status
        """
        try:
            # Get non-voided order items through the order's orderitem_set
            order_items = self._get_valid_order_items(order)
            if not order_items:
                logger.warning("No valid items found for order %s", order.id)
                return {}

            # Get workstation printer instead of grouping by family
            printer = self._get_workstation_printer(
                workstation or order.workstation, order.revenue_center
            )
            if not printer:
                logger.error(
                    "No printer found for workstation %s and revenue center %s",
                    getattr(workstation or order.workstation, "name", "unknown"),
                    getattr(order.revenue_center, "name", "unknown"),
                )
                return {}

            # Format receipt with all items (no family grouping)
            receipt_content = self._format_complete_receipt(order, order_items)
            success = self._send_to_printer(printer, receipt_content)

            result_key = f"{printer.name}_receipt"
            return {result_key: success}

        except Exception as e:
            logger.error("Error printing order receipt: %s", str(e))
            raise

    def _get_valid_order_items(self, order: "Order") -> List["OrderItem"]:
        """
        Get valid order items from the order's orderitem_set
        Excludes voided and cancelled items and prefetches related data
        """
        return list(
            order.orderitem_set.select_related("article__sub_family__family").all()
        )

    def print_kot(self, order: "Order") -> Dict[str, bool]:
        """
        Print KOT (Kitchen Order Ticket) for an order (grouped by family and printer)

        Args:
            order: The Order object to print (will access order.orderitem_set)

        Returns:
            Dict mapping printer names + family codes to success status
        """
        try:
            # Get non-voided order items through the order's orderitem_set
            order_items = self._get_valid_order_items(order)
            if not order_items:
                logger.warning("No valid items found for order %s", order.id)
                return {}

            return self._print_order_content(order, order_items, "kot")
        except Exception as e:
            logger.error("Error printing KOT: %s", str(e))
            raise

    def print_payment_receipt(self, order: "Order", workstation=None) -> bool:
        """
        Print payment receipt showing payment details and balance

        Args:
            order: The Order object
            workstation: Optional workstation to determine printer

        Returns:
            bool: True if print was successful
        """
        try:
            # Get payments directly from the order
            payments = order.orderpayment_set.all()
            if not payments.exists():
                logger.warning("No payments found for order %s", order.id)
                return False

            printer = self._get_workstation_printer(
                workstation or order.workstation, order.revenue_center
            )
            if not printer:
                logger.error("No printer found for payment receipt")
                return False
            receipt_data = self._format_payment_receipt(order, payments)
            return self._send_to_printer(printer, receipt_data)
        except Exception as e:
            logger.error("Failed to print payment receipt: %s", str(e))
            raise

    def print_report(
        self,
        report_content: str,
        title: str = "REPORT",
        workstation=None,
        revenue_center=None,
    ) -> bool:
        """
        Print a generic report on the workstation printer

        Args:
            report_content: The content of the report to print
            title: Title for the report (default: "REPORT")
            workstation: Workstation to determine printer
            revenue_center: Revenue center for printer lookup

        Returns:
            bool: True if print was successful
        """
        try:
            printer = self._get_workstation_printer(workstation, revenue_center)
            if not printer:
                logger.error("No printer found for report printing")
                return False

            report_data = self._format_report(report_content, title)
            return self._send_to_printer(printer, report_data)
        except Exception as e:
            logger.error("Failed to print report: %s", str(e))
            raise

    def _print_order_content(
        self, order: "Order", order_items: List["OrderItem"], receipt_type: str
    ) -> Dict[str, bool]:
        """Internal method to handle printing of both receipts and KOTs"""
        results = {}
        valid_items = []
        invalid_items = []

        # Validate all items and collect invalid ones
        for item in order_items:
            if not item.article:
                logger.warning("Order item %s missing article", item.id)
                invalid_items.append(item)
                continue

            # if not hasattr(item.article, 'sub_family') or not item.article.sub_family:
            #     logger.warning("Article %s missing sub_family",
            #                    item.article.id)
            #     invalid_items.append(item)
            #     continue

            valid_items.append(item)

        if not valid_items:
            logger.error(
                "No valid items with complete article data for order %s", order.id
            )
            return results

        # Group valid items by family and printer
        family_printer_groups = self._group_items_by_family_and_printer(
            order, valid_items
        )

        if not family_printer_groups:
            logger.error("No printer groups found for order %s", order.id)
            return results

        # Print each group
        for (printer, family), items in family_printer_groups.items():
            try:
                if receipt_type == "kot":
                    content = self._format_kot(order, items, family)
                    suffix = "kot"
                else:
                    content = self._format_receipt(order, items, family)
                    suffix = "receipt"

                success = self._send_to_printer(printer, content)
                family_code = family.code if family else "unknown"
                results[f"{printer.name}_{family_code}_{suffix}"] = success

                if not success and receipt_type == "receipt":
                    backup_printer = self._get_backup_printer(
                        items[0], order.revenue_center
                    )
                    if backup_printer:
                        backup_success = self._send_to_printer(backup_printer, content)
                        results[
                            f"{printer.name}_{family_code}_{suffix}_backup"
                        ] = backup_success

            except PrinterDataError as e:
                logger.error("Failed to format content: %s", str(e))
            except PrinterConnectionError as e:
                logger.error("Printer connection failed: %s", str(e))

        return results

    def _group_items_by_family_and_printer(
        self, order: "Order", order_items: List["OrderItem"]
    ) -> Dict:
        """Group order items by their family AND printer combination"""
        groups = {}

        for item in order_items:
            try:
                family = item.article.sub_family.family
                printing_rule = PrintingRule.objects.get(
                    family=family, revenue_center=order.revenue_center
                )
                key = (printing_rule.printer, family)
                groups.setdefault(key, []).append(item)
            except ObjectDoesNotExist:
                logger.warning(
                    "No printing rule for family %s in revenue center %s",
                    getattr(family, "code", "unknown"),
                    getattr(order.revenue_center, "name", "unknown"),
                )
            except Exception as e:
                logger.error("Error processing item %s: %s", item.id, str(e))

        return groups

    # ======================== INTERNAL METHODS ========================

    def _format_receipt(
        self, order: "Order", order_items: List["OrderItem"], family=None
    ) -> bytes:
        """Format customer receipt content (for family-grouped KOT printing)"""
        try:
            from core.dependency_injection import service_locator

            receipt = bytearray()
            receipt.extend(self.INIT_PRINTER)
            receipt.extend(self.NORMAL_FONT)
            receipt.extend(self.CENTER_ALIGN)
            receipt.extend(self.BOLD_ON)

            # Company Info
            app_setting = service_locator.general_service.app_setting
            if app_setting.company_name:
                receipt.extend(f"{self._truncate_line()}\n".encode("utf-8"))
            receipt.extend(self.BOLD_OFF)

            # Order details
            receipt.extend(f"Order #: {order.code}\n".encode())
            receipt.extend(f"Date: {order.created_at.strftime()}\n".encode("utf-8"))

            if family:
                receipt.extend(f"Category: {family.name}\n".encode())

            receipt.extend(("-" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))

            # Items
            for item in order_items:
                try:
                    qty = item.quantity
                    item.article.name if item.article else "Unknown Item"
                    receipt.extend(f"{qty}x {self._truncate_line()}\n".encode("utf-8"))
                except Exception:
                    receipt.extend(b"1x Error formatting item\n")

            # Footer
            receipt.extend(("=" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))
            receipt.extend(f"Total: {order.grand_total:.2f}\n".encode())
            receipt.extend(self.CUT_PAPER)

            return bytes(receipt)
        except Exception as e:
            raise PrinterDataError(f"Failed to format receipt: {str(e)}")

    def _format_complete_receipt(
        self, order: "Order", order_items: List["OrderItem"]
    ) -> bytes:
        """Format complete customer receipt with all items (no family grouping)"""
        try:
            from core.dependency_injection import service_locator

            receipt = bytearray()
            receipt.extend(self.INIT_PRINTER)
            receipt.extend(self.NORMAL_FONT)
            receipt.extend(self.CENTER_ALIGN)
            receipt.extend(self.BOLD_ON)

            # Company Info
            app_setting = service_locator.general_service.app_setting
            if app_setting.company_name:
                receipt.extend(f"{self._truncate_line()}\n".encode("utf-8"))
            receipt.extend(self.BOLD_OFF)

            # Order details
            receipt.extend(f"Order #: {order.code}\n".encode())
            receipt.extend(f"Date: {order.created_at.strftime()}\n".encode("utf-8"))

            # Table info if available
            if hasattr(order, "table") and order.table:
                receipt.extend(f"Table: {order.table.name}\n".encode())

            receipt.extend(("-" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))

            # All items without family grouping
            total_amount = 0
            for item in order_items:
                try:
                    qty = item.quantity
                    description = item.article.name if item.article else "Unknown Item"
                    price = getattr(item, "price", 0)
                    line_total = qty * price
                    total_amount += line_total

                    # Format: "2x Item Name        10.00"
                    item_line = f"{qty}x {self._truncate_line(description, self.DEFAULT_WIDTH - 8)}"
                    price_str = f"{line_total:.2f}"
                    self.DEFAULT_WIDTH - len(item_line) - len(price_str)
                    receipt.extend(
                        f"{item_line}{' ' * max()}{price_str}\n".encode("utf-8")
                    )

                except Exception as e:
                    logger.warning(
                        "Error formatting item %s: %s",
                        getattr(item, "id", "unknown"),
                        str(e),
                    )
                    receipt.extend(b"1x Error formatting item\n")

            # Footer with totals
            receipt.extend(("=" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))
            receipt.extend(f"Subtotal: {total_amount:.2f}\n".encode())

            # Add discount if any
            if hasattr(order, "discount") and order.discount > 0:
                receipt.extend(f"Discount: -{order.discount:.2f}\n".encode())

            receipt.extend(self.BOLD_ON)
            receipt.extend(f"TOTAL: {order.grand_total:.2f}\n".encode())
            receipt.extend(self.BOLD_OFF)

            receipt.extend(self.FEED_LINE)
            receipt.extend(b"Thank you for your visit!\n")
            receipt.extend(self.FEED_LINE * 2)
            receipt.extend(self.CUT_PAPER)

            return bytes(receipt)
        except Exception as e:
            raise PrinterDataError(f"Failed to format complete receipt: {str(e)}")

    def _format_kot(
        self, order: "Order", order_items: List["OrderItem"], family=None
    ) -> bytes:
        """Format KOT (Kitchen Order Ticket) content"""
        try:
            kot = bytearray()
            kot.extend(self.INIT_PRINTER)
            kot.extend(self.NORMAL_FONT)
            kot.extend(self.CENTER_ALIGN)
            kot.extend(self.BOLD_ON)
            kot.extend(b"KITCHEN ORDER TICKET\n")
            kot.extend(self.BOLD_OFF)

            # Order info
            kot.extend(f"Order #: {order.code}\n".encode())
            kot.extend(f"Time: {order.created_at.strftime()}\n".encode("utf-8"))

            if family:
                kot.extend(f"Category: {family.name}\n".encode())

            kot.extend(("-" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))

            # Items with notes
            for item in order_items:
                qty = item.quantity
                item.article.name if item.article else "Unknown Item"
                notes = (
                    f" ({item.notes})" if hasattr(item, "notes") and item.notes else ""
                )
                kot.extend(f"{qty}x {self._truncate_line()}{notes}\n".encode("utf-8"))

            kot.extend(self.FEED_LINE * 2)
            kot.extend(self.CUT_PAPER)
            return bytes(kot)
        except Exception as e:
            raise PrinterDataError(f"Failed to format KOT: {str(e)}")

    def _format_payment_receipt(self, order: "Order", payments) -> bytes:
        """Format payment receipt content"""
        try:
            receipt = bytearray()
            receipt.extend(self.INIT_PRINTER)
            receipt.extend(self.NORMAL_FONT)
            receipt.extend(self.CENTER_ALIGN)
            receipt.extend(self.BOLD_ON)
            receipt.extend(b"PAYMENT RECEIPT\n")
            receipt.extend(self.BOLD_OFF)

            # Payment details
            receipt.extend(f"Order #: {order.code}\n".encode())
            receipt.extend(f"Date: {order.created_at.strftime()}\n".encode("utf-8"))
            receipt.extend(("-" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))

            for payment in payments:
                method = payment.payment_method.name
                receipt.extend(f"{method}: {payment.amount:.2f}\n".encode())

            receipt.extend(("=" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))
            receipt.extend(f"Total Paid: {sum():.2f}\n".encode("utf-8"))
            receipt.extend(f"Balance: {order.remaining_balance:.2f}\n".encode())
            receipt.extend(self.CUT_PAPER)
            return bytes(receipt)
        except Exception as e:
            raise PrinterDataError(f"Failed to format payment receipt: {str(e)}")

    def _format_report(self, content: str, title: str = "REPORT") -> bytes:
        """Format generic report content for printing"""
        try:

            report = bytearray()
            report.extend(self.INIT_PRINTER)
            report.extend(self.NORMAL_FONT)
            report.extend(self.CENTER_ALIGN)
            report.extend(self.BOLD_ON)

            # Title
            report.extend(f"{title.upper()}\n".encode("utf-8"))
            report.extend(self.BOLD_OFF)

            # Timestamp
            timestamp = datetime.now().strftime("%a, %b %d %Y at %I:%M %p")
            report.extend(f"Generated: {timestamp}\n".encode())
            report.extend(("=" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))

            # Content (left aligned)
            report.extend(self.LEFT_ALIGN)

            # Split content into lines and format each
            lines = content.split("\n")
            for line in lines:
                # Truncate long lines to fit printer width
                truncated_line = self._truncate_line(line, self.DEFAULT_WIDTH)
                report.extend(f"{truncated_line}\n".encode())

            # Footer
            report.extend(self.FEED_LINE)
            report.extend(("=" * self.DEFAULT_WIDTH + "\n").encode("utf-8"))
            report.extend(self.FEED_LINE * 2)
            report.extend(self.CUT_PAPER)

            return bytes(report)
        except Exception as e:
            raise PrinterDataError(f"Failed to format report: {str(e)}")

    # ======================== PRINTER COMMUNICATION ========================

    def _send_to_printer(self, printer: "Printer", data: bytes) -> bool:
        """Send data to printer via network or USB"""
        if not printer:
            raise PrinterConnectionError("Printer object is None")

        try:
            if printer.connection_type == "network":
                return self._send_network(printer, data)
            elif printer.connection_type == "usb":
                return self._send_usb(printer, data)
            else:
                raise PrinterConnectionError(
                    f"Unknown connection type: {printer.connection_type}"
                )
        except Exception as e:
            logger.error("Failed to send to printer %s: %s", printer.name, str(e))
            raise PrinterConnectionError(str(e))

    def _send_network(self, printer: "Printer", data: bytes) -> bool:
        """Send to network printer"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(self.timeout)
                sock.connect((printer.ip_address, int(printer.port)))
                sock.sendall(data)
                return True
        except Exception as e:
            raise PrinterConnectionError(f"Network error: {str(e)}")

    def _send_usb(self, printer: "Printer", data: bytes) -> bool:
        """Send to USB printer using serial port"""
        try:
            with serial.Serial(
                printer.port, self.DEFAULT_BAUD_RATE, timeout=self.timeout
            ) as ser:
                ser.write(data)
                return True
        except Exception as e:
            raise PrinterConnectionError(f"USB error: {str(e)}")

    # ======================== UTILITY METHODS ========================

    def _get_backup_printer(
        self, order_item: "OrderItem", revenue_center
    ) -> Optional["Printer"]:
        """Get backup printer for a given order item"""
        try:
            family = order_item.article.sub_family.family
            printing_rule = PrintingRule.objects.get(
                family=family, revenue_center=revenue_center
            )
            return printing_rule.backup_printer
        except Exception:
            return None

    def _get_workstation_printer(
        self, workstation, revenue_center
    ) -> Optional["Printer"]:
        """
        Get printer for a specific workstation and revenue center using PrintingRule.
        This is used for receipts and reports that should print on the workstation printer.

        Args:
            workstation: The Workstation object
            revenue_center: The RevenueCenter object

        Returns:
            Printer object or None if not found
        """
        if not workstation or not revenue_center:
            logger.warning("Missing workstation or revenue_center for printer lookup")
            return None

        try:
            # Look for a PrintingRule that matches workstation and revenue_center
            printing_rule = PrintingRule.objects.filter(
                workstation=workstation, revenue_center=revenue_center
            ).first()

            if printing_rule and printing_rule.printer:
                return printing_rule.printer

            # Fallback: try to find any PrintingRule for this revenue_center
            fallback_rule = PrintingRule.objects.filter(
                revenue_center=revenue_center
            ).first()

            if fallback_rule and fallback_rule.printer:
                logger.info(
                    "Using fallback printer for revenue_center %s", revenue_center.name
                )
                return fallback_rule.printer

            logger.warning(
                "No printer found for workstation %s and revenue_center %s",
                workstation.name,
                revenue_center.name,
            )
            return None

        except Exception as e:
            logger.error("Error finding workstation printer: %s", str(e))
            return None

    def _truncate_line(self, text: str, width: int = DEFAULT_WIDTH) -> str:
        """Safely truncate text to fit printer width"""
        return str(text)[:width] if text else ""

    # ======================== PRINTER TESTING ========================

    def test_printer(self, printer: "Printer") -> Tuple[bool, Optional[str]]:
        """Test printer connectivity with a test page"""
        try:
            test_data = (
                self.INIT_PRINTER
                + b"TEST PRINT\n"
                + self.FEED_LINE * 2
                + self.CUT_PAPER
            )
            success = self._send_to_printer(printer, test_data)
            return success, None if success else "Test print failed"
        except PrinterConnectionError as e:
            return False, str(e)
        except Exception as e:
            return False, f"Unexpected error: {str(e)}"
