# Generated by Django 5.2.3 on 2025-07-21 16:30
import uuid

import django.db.models.deletion
from django.db import migrations
from django.db import models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("literals", "0005_printer_workstation"),
        ("products", "0005_remove_family_printer"),
    ]

    operations = [
        migrations.CreateModel(
            name="PrintingRule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "backup_printer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="literals.printer",
                    ),
                ),
                (
                    "family",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.family",
                    ),
                ),
                (
                    "printer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="literals.printer",
                    ),
                ),
                (
                    "revenue_center",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="literals.revenuecenter",
                    ),
                ),
            ],
            options={
                "unique_together": {("family", "revenue_center")},
            },
        ),
    ]
