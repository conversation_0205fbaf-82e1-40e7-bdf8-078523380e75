from core.models import BaseModel
from django.db import models
from general.models import AppSetting
from literals.models import Printer
from literals.models import RevenueCenter
from literals.models import Workstation


class PrintingRule(BaseModel):
    class DESTINATION:
        KITCHEN = AppSetting.MODE.RESTAURANT
        BAR = AppSetting.MODE.BAR

        CHOICES = [
            (K<PERSON><PERSON><PERSON>, "Kitchen"),
            (BAR, "Bar"),
        ]

    family = models.ForeignKey("products.Family", on_delete=models.CASCADE)
    revenue_center = models.ForeignKey(RevenueCenter, on_delete=models.CASCADE)
    printer = models.ForeignKey(Printer, on_delete=models.CASCADE)
    backup_printer = models.ForeignKey(
        Printer, on_delete=models.CASCADE, related_name="+", null=True, blank=True
    )
    message_destination = models.CharField(
        max_length=20, choices=DESTINATION.CHOICES, null=True, blank=True
    )
    workstation = models.ForeignKey(Workstation, on_delete=models.CASCADE, null=True)

    class Meta:
        unique_together = ("family", "revenue_center")

    def __str__(self):
        return f"{self.family.name} in {self.revenue_center} → {self.printer}"
