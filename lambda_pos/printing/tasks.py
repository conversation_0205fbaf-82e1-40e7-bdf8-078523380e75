from celery import shared_task
from core.dependency_injection import service_locator


@shared_task
def print_kot_async(order_id):
    service_locator.thermal_print_service.print_kot(order_id)


@shared_task
def print_order_receipt_async(order_id):
    service_locator.thermal_print_service.print_order_receipt(order_id)


@shared_task
def print_report_async(order_id):
    service_locator.thermal_print_service.print_report(order_id)
