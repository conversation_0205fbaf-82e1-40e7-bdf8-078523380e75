from rest_framework import serializers
from .models import Article, Family, SubFamily
from core.serializers import CreatedByMixin



class FamilySerializer(CreatedByMixin, serializers.ModelSerializer):
    generated_color = serializers.CharField(read_only=True,allow_null=True)
    class Meta:
        model = Family
        exclude = ['is_deleted']
        read_only_fields = ['id','created_by']

class SubFamilySerializer(CreatedByMixin, serializers.ModelSerializer):
    generated_color = serializers.CharField(read_only=True,allow_null=True)
    class Meta:
        model = SubFamily
        exclude = ['is_deleted']
        read_only_fields = ['id','created_by']

class ArticleSerializer(CreatedByMixin, serializers.ModelSerializer):
    generated_color = serializers.CharField(read_only=True,allow_null=True)
    class Meta:
        model = Article
        exclude = ['is_deleted']
        read_only_fields = ['id','created_by']