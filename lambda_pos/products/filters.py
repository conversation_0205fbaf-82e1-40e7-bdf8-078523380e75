import django_filters
from django_filters import rest_framework as filters
from products.models import Article
from products.models import Family
from products.models import SubFamily


class FamilyFilter(filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr="icontains")
    code = django_filters.CharFilter(lookup_expr="icontains")

    class Meta:
        model = Family
        fields = ["name", "disabled", "code", "created_by"]


class SubFamilyFilter(filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr="icontains")
    code = django_filters.CharFilter(lookup_expr="icontains")

    class Meta:
        model = SubFamily
        fields = ["name", "disabled", "code", "family", "created_by"]


class ArticleFilter(filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr="icontains")
    code = django_filters.CharFilter(lookup_expr="icontains")

    class Meta:
        model = Article
        fields = [
            "name",
            "is_favorite",
            "disabled",
            "code",
            "sub_family",
            "created_by",
            "workstations",
            "product_type",
            "unit_measure",
        ]
