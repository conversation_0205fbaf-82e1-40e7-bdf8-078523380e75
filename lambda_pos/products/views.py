from core.views import PaginateMixin
from core.views import TaggedDecorator
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema
from literals.models import Workstation
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .filters import ArticleFilter
from .filters import FamilyFilter
from .filters import SubFamilyFilter
from .models import Article
from .models import Family
from .models import SubFamily
from .serializers import ArticleSerializer
from .serializers import FamilySerializer
from .serializers import SubFamilySerializer


class FamilyViewSet(TaggedDecorator, PaginateMixin, viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = Family.objects.all()
    serializer_class = FamilySerializer
    filter_backends = [DjangoFilterBackend]

    filterset_class = FamilyFilter


class SubFamilyViewSet(TaggedDecorator, PaginateMixin, viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = SubFamily.objects.all()
    serializer_class = SubFamilySerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = SubFamilyFilter


class ArticleViewSet(TaggedDecorator, PaginateMixin, viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = Article.objects.filter(disabled=False)
    serializer_class = ArticleSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = ArticleFilter

    @extend_schema(
        description="Get articles available to the calling workstation IP.",
        responses={200: ArticleSerializer(many=True)},
    )
    @action(detail=False, methods=["get"])
    def workstation(self, request, *args, **kwargs):
        workstation_ip = request.META.get("HTTP_X_FORWARDED_FOR") or request.META.get(
            "REMOTE_ADDR"
        )

        try:
            workstation = Workstation.objects.get(
                ip_address=workstation_ip, is_active=True
            )
        except Workstation.DoesNotExist:
            return Response({"error": "Workstation not found"}, status=404)

        articles = self.get_queryset().filter(workstations=workstation)

        articles = self.filter_queryset(articles)

        serializer = self.get_serializer(articles, many=True)
        return Response(serializer.data)
