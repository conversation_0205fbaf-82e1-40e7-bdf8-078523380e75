# Generated by Django 5.2.3 on 2025-06-18 01:49

import colorfield.fields
import django.db.models.deletion
import products.models
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('literals', '0002_alter_printer_ip_address_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Family',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('disabled', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True, null=True)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('button_label', models.CharField(blank=True, max_length=100, null=True)),
                ('button_label_hide', models.BooleanField(default=False)),
                ('button_image', models.ImageField(blank=True, null=True, upload_to='family_buttons/')),
                ('button_icon', models.CharField(blank=True, max_length=100, null=True)),
                ('commission_group', models.CharField(blank=True, max_length=100, null=True)),
                ('discount_group', models.CharField(blank=True, max_length=100, null=True)),
                ('template', models.TextField(blank=True, null=True)),
                ('color', colorfield.fields.ColorField(blank=True, default='#0066cc', image_field=None, max_length=25, null=True, samples=None)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('printer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='literals.printer')),
            ],
            options={
                'abstract': False,
            },
            bases=(products.models.ColorMixin, models.Model),
        ),
        migrations.CreateModel(
            name='SubFamily',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('disabled', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True, null=True)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('button_label', models.CharField(blank=True, max_length=100, null=True)),
                ('button_label_hide', models.BooleanField(default=False)),
                ('button_image', models.ImageField(blank=True, null=True, upload_to='article_family_buttons/')),
                ('button_icon', models.CharField(blank=True, max_length=100, null=True)),
                ('commission_group', models.CharField(blank=True, max_length=100, null=True)),
                ('discount_group', models.CharField(blank=True, max_length=100, null=True)),
                ('template', models.TextField(blank=True, null=True)),
                ('color', colorfield.fields.ColorField(blank=True, default='#0066cc', image_field=None, max_length=25, null=True, samples=None)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('family', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.family')),
            ],
            options={
                'abstract': False,
            },
            bases=(products.models.ColorMixin, models.Model),
        ),
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('is_favorite', models.BooleanField(default=False)),
                ('price', models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=10, null=True)),
                ('disabled', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True, null=True)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('code_dealer', models.CharField(blank=True, max_length=50, null=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('button_label', models.CharField(blank=True, max_length=100, null=True)),
                ('button_label_hide', models.BooleanField(default=False)),
                ('button_image', models.ImageField(blank=True, null=True, upload_to='article_buttons/')),
                ('button_icon', models.CharField(blank=True, max_length=100, null=True)),
                ('price_with_vat', models.BooleanField(default=False)),
                ('discount', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('default_quantity', models.PositiveSmallIntegerField(default=1)),
                ('minimum_stock', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('barcode', models.CharField(blank=True, max_length=100, null=True)),
                ('favorite', models.BooleanField(default=False)),
                ('template', models.TextField(blank=True, null=True)),
                ('template_barcode', models.TextField(blank=True, null=True)),
                ('is_unique', models.BooleanField(default=False)),
                ('color', colorfield.fields.ColorField(blank=True, default='#0066cc', image_field=None, max_length=25, null=True, samples=None)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('product_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='literals.producttype')),
                ('unit_measure', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='literals.unitmeasure')),
                ('workstation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='literals.workstation')),
                ('sub_family', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.subfamily')),
            ],
            options={
                'abstract': False,
            },
            bases=(products.models.ColorMixin, models.Model),
        ),
    ]
