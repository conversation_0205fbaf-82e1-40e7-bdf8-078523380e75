from django.contrib import admin

from .models import Article
from .models import Family
from .models import SubFamily

# Register your models here.


@admin.register(Family)
class FamilyAdmin(admin.ModelAdmin):
    list_display = ["name", "code", "description"]
    search_fields = ["name", "code", "description"]
    list_filter = ["created_by"]


@admin.register(SubFamily)
class SubFamilyAdmin(admin.ModelAdmin):
    list_display = ["name", "code", "description", "family"]
    search_fields = ["name", "code", "description"]
    list_filter = ["created_by", "family"]


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "code",
        "price",
        "button_label",
        "is_favorite",
        "description",
        "sub_family",
        "product_type",
        "unit_measure",
    ]
    search_fields = ["name", "code", "description"]
    list_filter = [
        "created_by",
        "is_favorite",
        "sub_family",
        "product_type",
        "unit_measure",
    ]
    list_editable = ["price", "is_favorite", "button_label"]
