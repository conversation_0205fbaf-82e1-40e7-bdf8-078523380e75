from django.urls import include
from django.urls import path
from rest_framework.routers import <PERSON>faultR<PERSON><PERSON>

from .views import InventoryItemViewSet
from .views import PurchaseOrderItemViewSet
from .views import PurchaseOrderViewSet
from .views import StockMovementViewSet
from .views import SupplierViewSet

router = DefaultRouter()
router.register(r"items", InventoryItemViewSet)
router.register(r"movements", StockMovementViewSet)
router.register(r"suppliers", SupplierViewSet)
router.register(r"purchase-orders", PurchaseOrderViewSet)
router.register(r"purchase-order-items", PurchaseOrderItemViewSet)

urlpatterns = [
    path("", include(router.urls)),
]
