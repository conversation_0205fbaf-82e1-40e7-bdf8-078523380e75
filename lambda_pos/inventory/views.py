from decimal import Decimal

from core.views import TaggedDecorator
from django.db import transaction
from django.db.models import Sum
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .filters import InventoryItemFilter
from .models import InventoryItem
from .models import PurchaseOrder
from .models import PurchaseOrderItem
from .models import StockMovement
from .models import Supplier
from .serializers import InventoryDashboardSerializer
from .serializers import InventoryItemSerializer
from .serializers import PurchaseOrderItemSerializer
from .serializers import PurchaseOrderSerializer
from .serializers import StockAdjustmentSerializer
from .serializers import StockMovementSerializer
from .serializers import SupplierSerializer
from .services import InventoryService


class InventoryItemViewSet(TaggedDecorator, viewsets.ModelViewSet):

    queryset = InventoryItem.objects.select_related(
        "article", "article__sub_family", "article__sub_family__family"
    ).order_by("-created_at")
    serializer_class = InventoryItemSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = InventoryItemFilter

    def get_queryset(self):
        queryset = super().get_queryset()

        # Note: Stock level filters (low_stock, out_of_stock) are handled
        # in the frontend since current_stock is a calculated property

        return queryset

    def create(self, request, *args, **kwargs):
        article_id = request.data.get("article_id")

        if article_id:
            defaults = {k: v for k, v in request.data.items() if k != "article_id"}
            inventory_item, created = InventoryItem.objects.update_or_create(
                article_id=article_id, defaults=defaults
            )
            serializer = self.get_serializer(inventory_item)
            status_code = status.HTTP_201_CREATED if created else status.HTTP_200_OK
            return Response(serializer.data, status=status_code)

        return super().create(request, *args, **kwargs)

    @action(detail=False, methods=["post"])
    def get_or_create(self, request):
        """Get existing inventory item or create new one for an article"""
        article_id = request.data.get("article_id")
        if not article_id:
            return Response(
                {"error": "article_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Try to get existing inventory item
            inventory_item = InventoryItem.objects.get(article_id=article_id)
            serializer = self.get_serializer(inventory_item)
            return Response({"created": False, "inventory_item": serializer.data})
        except InventoryItem.DoesNotExist:
            # Create new inventory item
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                inventory_item = serializer.save()
                return Response(
                    {
                        "created": True,
                        "inventory_item": self.get_serializer(inventory_item).data,
                    },
                    status=status.HTTP_201_CREATED,
                )
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["get"])
    def dashboard(self, request):
        """Get inventory dashboard data"""
        total_items = InventoryItem.objects.filter(is_active=True).count()
        # Calculate stats using properties since current_stock is a property
        inventory_items = InventoryItem.objects.filter(is_active=True)

        low_stock_items = 0
        out_of_stock_items = 0
        total_stock_value = Decimal("0")

        for item in inventory_items:
            current_stock = item.current_stock
            if current_stock == 0:
                out_of_stock_items += 1
            elif current_stock <= item.reorder_point:
                low_stock_items += 1
            total_stock_value += item.stock_value

        pending_orders = PurchaseOrder.objects.filter(
            status__in=[PurchaseOrder.STATUS.PENDING, PurchaseOrder.STATUS.APPROVED]
        ).count()

        recent_movements = StockMovement.objects.select_related(
            "inventory_item__article", "created_by"
        ).order_by("-created_at")[:10]

        # Get low stock alerts manually since current_stock is a property
        low_stock_alerts = []
        for item in inventory_items:
            if 0 < item.current_stock <= item.reorder_point:
                low_stock_alerts.append(item)
                if len(low_stock_alerts) >= 10:
                    break

        dashboard_data = {
            "total_items": total_items,
            "low_stock_items": low_stock_items,
            "out_of_stock_items": out_of_stock_items,
            "total_stock_value": total_stock_value,
            "pending_orders": pending_orders,
            "recent_movements": recent_movements,
            "low_stock_alerts": low_stock_alerts,
        }

        serializer = InventoryDashboardSerializer(dashboard_data)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def adjust_stock(self, request, pk=None):
        """Adjust stock for a specific inventory item using FIFO service"""
        inventory_item = self.get_object()
        serializer = StockAdjustmentSerializer(data=request.data)

        if serializer.is_valid():
            data = serializer.validated_data
            adjustment_type = data["adjustment_type"]
            quantity = data["quantity"]
            reason = data["reason"]
            unit_cost = data.get("unit_cost", inventory_item.average_unit_cost)

            try:
                InventoryService.adjust_stock(
                    inventory_item=inventory_item,
                    adjustment_type=adjustment_type,
                    quantity=quantity,
                    reason=reason,
                    unit_cost=unit_cost,
                    user=request.user,
                )

                return Response(
                    {
                        "message": "Stock adjusted successfully",
                        "new_stock": inventory_item.current_stock,
                    }
                )

            except Exception as e:
                return Response(
                    {"error": f"Failed to adjust stock: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["get"])
    def alerts(self, request):
        """Get stock alerts for low stock, out of stock, and expiring items"""
        alerts = InventoryService.get_stock_alerts()

        # Serialize the alerts
        serialized_alerts = {
            "low_stock": InventoryItemSerializer(alerts["low_stock"], many=True).data,
            "out_of_stock": InventoryItemSerializer(
                alerts["out_of_stock"], many=True
            ).data,
            "expiring_soon": InventoryItemSerializer(
                alerts["expiring_soon"], many=True
            ).data,
            "expired": InventoryItemSerializer(alerts["expired"], many=True).data,
        }

        return Response(serialized_alerts)


class StockMovementViewSet(TaggedDecorator, viewsets.ModelViewSet):
    """ViewSet for managing stock movements"""

    queryset = StockMovement.objects.select_related(
        "inventory_item__article", "created_by"
    ).order_by("-created_at")
    serializer_class = StockMovementSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["movement_type", "inventory_item"]

    def perform_create(self, serializer):
        """Create stock movement and update inventory"""
        with transaction.atomic():
            movement = serializer.save()
            inventory_item = movement.inventory_item

            # Update stock based on movement type
            if movement.movement_type in [
                StockMovement.MOVEMENT_TYPE.PURCHASE,
                StockMovement.MOVEMENT_TYPE.ADJUSTMENT,
            ]:
                if movement.quantity > 0:
                    inventory_item.current_stock += movement.quantity
            elif movement.movement_type in [
                StockMovement.MOVEMENT_TYPE.SALE,
                StockMovement.MOVEMENT_TYPE.WASTE,
            ]:
                inventory_item.current_stock = max(
                    0, inventory_item.current_stock - abs(movement.quantity)
                )

            inventory_item.save()

    @action(detail=False, methods=["get"])
    def analytics(self, request):
        """Get stock movement analytics"""
        from datetime import datetime, timedelta

        # Get filter parameters
        date_range_preset = request.query_params.get("date_range_preset", "last_7_days")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        # Calculate date range
        today = datetime.now().date()
        if date_range_preset == "today":
            start_date = today
            end_date = today
        elif date_range_preset == "last_7_days":
            start_date = today - timedelta(days=7)
            end_date = today
        elif date_range_preset == "last_30_days":
            start_date = today - timedelta(days=30)
            end_date = today
        elif start_date and end_date:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
        else:
            start_date = today - timedelta(days=7)
            end_date = today

        # Filter movements by date range
        movements = self.get_queryset().filter(
            created_at__date__gte=start_date, created_at__date__lte=end_date
        )

        # Calculate analytics
        total_movements = movements.count()

        # Movement type breakdown
        movement_type_stats = []
        for movement_type, label in StockMovement.MOVEMENT_TYPE.CHOICES:
            type_movements = movements.filter(movement_type=movement_type)
            total_quantity = (
                type_movements.aggregate(total=Sum("quantity"))["total"] or 0
            )
            total_value = sum(abs(m.total_value) for m in type_movements)

            movement_type_stats.append(
                {
                    "type": movement_type,
                    "label": label,
                    "count": type_movements.count(),
                    "total_quantity": float(total_quantity),
                    "total_value": float(total_value),
                }
            )

        # Daily movement trends (last 7 days)
        daily_trends = []
        for i in range(7):
            date = today - timedelta(days=i)
            day_movements = movements.filter(created_at__date=date)
            daily_trends.append(
                {
                    "date": date.isoformat(),
                    "total_movements": day_movements.count(),
                    "inbound_movements": day_movements.filter(
                        movement_type__in=[
                            StockMovement.MOVEMENT_TYPE.PURCHASE,
                            StockMovement.MOVEMENT_TYPE.ADJUSTMENT,
                        ]
                    ).count(),
                    "outbound_movements": day_movements.filter(
                        movement_type__in=[
                            StockMovement.MOVEMENT_TYPE.SALE,
                            StockMovement.MOVEMENT_TYPE.WASTE,
                        ]
                    ).count(),
                }
            )

        return Response(
            {
                "total_movements": total_movements,
                "date_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                },
                "movement_type_stats": movement_type_stats,
                "daily_trends": list(reversed(daily_trends)),  # Most recent first
                "movement_types": StockMovement.MOVEMENT_TYPE.CHOICES,
            }
        )


class SupplierViewSet(TaggedDecorator, viewsets.ModelViewSet):
    """ViewSet for managing suppliers"""

    queryset = Supplier.objects.order_by("name")
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["is_active"]


class PurchaseOrderViewSet(TaggedDecorator, viewsets.ModelViewSet):
    """ViewSet for managing purchase orders"""

    queryset = PurchaseOrder.objects.select_related("supplier", "created_by").order_by(
        "-created_at"
    )
    serializer_class = PurchaseOrderSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["status", "supplier"]

    @action(detail=True, methods=["post"])
    def approve(self, request, pk=None):
        """Approve a purchase order"""
        purchase_order = self.get_object()
        if purchase_order.status == PurchaseOrder.STATUS.PENDING:
            purchase_order.status = PurchaseOrder.STATUS.APPROVED
            purchase_order.save()
            return Response({"message": "Purchase order approved"})
        return Response(
            {"error": "Purchase order cannot be approved"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    @action(detail=True, methods=["post"])
    def receive(self, request, pk=None):
        """Mark purchase order as received and update inventory using batch system"""
        purchase_order = self.get_object()
        if purchase_order.status != PurchaseOrder.STATUS.APPROVED:
            return Response(
                {"error": "Purchase order must be approved before receiving"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        with transaction.atomic():
            purchase_order.status = PurchaseOrder.STATUS.RECEIVED
            purchase_order.save()

            # Update inventory for each item using batch system
            for item in purchase_order.items.all():
                try:
                    inventory_item, created = InventoryItem.objects.get_or_create(
                        article=item.article,
                        defaults={
                            "average_unit_cost": item.unit_cost,
                            "minimum_stock": 0,
                            "reorder_point": 0,
                            "is_active": True,
                        },
                    )
                except Exception as e:
                    # Handle race condition where another process created the item
                    if "duplicate key value violates unique constraint" in str(
                        e
                    ) and "article_id" in str(e):
                        try:
                            inventory_item = InventoryItem.objects.get(
                                article=item.article
                            )
                        except InventoryItem.DoesNotExist:
                            # This shouldn't happen, but re-raise if it does
                            raise e
                    else:
                        raise e

                # Add stock using batch system
                InventoryService.add_stock_batch(
                    inventory_item=inventory_item,
                    quantity=item.quantity_ordered,
                    unit_cost=item.unit_cost,
                    batch_number=f"PO-{purchase_order.order_number}-{item.id}",
                    purchase_order_ref=purchase_order.order_number,
                    supplier_ref=purchase_order.supplier.name,
                    user=request.user,
                )

        return Response({"message": "Purchase order received and inventory updated"})


class PurchaseOrderItemViewSet(TaggedDecorator, viewsets.ModelViewSet):
    """ViewSet for managing purchase order items"""

    queryset = PurchaseOrderItem.objects.select_related("purchase_order", "article")
    serializer_class = PurchaseOrderItemSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["purchase_order"]
