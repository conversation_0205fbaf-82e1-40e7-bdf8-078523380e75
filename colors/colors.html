<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Color Inheritance Comparison</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
        padding: 20px;
        background: #2c3e50;
        color: white;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
      }

      .comparison {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 40px;
      }

      .approach {
        background: #34495e;
        padding: 20px;
        border-radius: 12px;
      }

      .approach h2 {
        margin-top: 0;
        text-align: center;
        padding: 10px;
        border-radius: 8px;
      }

      .inheritance h2 {
        background: #e74c3c;
        color: white;
      }

      .independent h2 {
        background: #27ae60;
        color: white;
      }

      .pos-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin-top: 20px;
      }

      .product-card {
        border-radius: 12px;
        padding: 15px;
        color: white;
        font-weight: 600;
        text-align: center;
        position: relative;
        min-height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        transition: transform 0.2s;
      }

      .product-card:hover {
        transform: scale(1.02);
        cursor: pointer;
      }

      .product-name {
        font-size: 16px;
        margin-bottom: 8px;
      }

      .product-price {
        font-size: 20px;
        font-weight: bold;
      }

      .product-info {
        font-size: 12px;
        opacity: 0.9;
        margin-bottom: 8px;
      }

      .qty-badge {
        position: absolute;
        top: 8px;
        left: 8px;
        background: rgba(0, 0, 0, 0.3);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 11px;
      }

      .category-header {
        grid-column: 1 / -1;
        background: rgba(255, 255, 255, 0.1);
        padding: 8px 15px;
        border-radius: 6px;
        font-weight: bold;
        text-align: center;
        margin: 10px 0 5px 0;
      }

      .pros-cons {
        margin-top: 20px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
      }

      .pros-cons h4 {
        margin: 0 0 10px 0;
        color: #ecf0f1;
      }

      .pros-cons ul {
        margin: 0;
        padding-left: 20px;
      }

      .pros-cons li {
        margin-bottom: 5px;
        font-size: 14px;
      }

      .pros {
        color: #2ecc71;
      }

      .cons {
        color: #e74c3c;
      }

      .recommendation {
        background: #3498db;
        padding: 20px;
        border-radius: 12px;
        margin-top: 30px;
        text-align: center;
      }

      .recommendation h3 {
        margin: 0 0 15px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1 style="text-align: center; margin-bottom: 40px">
        Color Inheritance in POS Interface: Visual Comparison
      </h1>

      <div class="comparison">
        <!-- Color Inheritance Approach -->
        <div class="approach inheritance">
          <h2>Color Inheritance (Children match Parents)</h2>
          <div class="pos-grid">
            <div class="category-header">DRINKS Family (Blue)</div>

            <!-- Soft Drinks SubFamily - inherits blue -->
            <div class="product-card" style="background: #4a90e2">
              <div class="qty-badge">Qty: 15</div>
              <div class="product-name">Coca-Cola 500ml</div>
              <div class="product-info">Soft Drinks → Drinks</div>
              <div class="product-price">GH₵23.00</div>
            </div>

            <div class="product-card" style="background: #4a90e2">
              <div class="qty-badge">Qty: 12</div>
              <div class="product-name">Sprite 500ml</div>
              <div class="product-info">Soft Drinks → Drinks</div>
              <div class="product-price">GH₵20.00</div>
            </div>

            <div class="product-card" style="background: #4a90e2">
              <div class="qty-badge">Qty: 8</div>
              <div class="product-name">Fanta Orange</div>
              <div class="product-info">Soft Drinks → Drinks</div>
              <div class="product-price">GH₵23.00</div>
            </div>

            <!-- Alcoholic SubFamily - also inherits blue -->
            <div class="product-card" style="background: #4a90e2">
              <div class="qty-badge">Qty: 5</div>
              <div class="product-name">Castle Lite</div>
              <div class="product-info">Alcoholic → Drinks</div>
              <div class="product-price">GH₵15.00</div>
            </div>

            <div class="product-card" style="background: #4a90e2">
              <div class="qty-badge">Qty: 3</div>
              <div class="product-name">Star Beer</div>
              <div class="product-info">Alcoholic → Drinks</div>
              <div class="product-price">GH₵12.00</div>
            </div>

            <div class="product-card" style="background: #4a90e2">
              <div class="qty-badge">Qty: 7</div>
              <div class="product-name">Club Beer</div>
              <div class="product-info">Alcoholic → Drinks</div>
              <div class="product-price">GH₵18.00</div>
            </div>

            <div class="category-header">FOOD Family (Purple)</div>

            <!-- All food items same color -->
            <div class="product-card" style="background: #bd10e0">
              <div class="qty-badge">Qty: 0</div>
              <div class="product-name">Jollof Rice</div>
              <div class="product-info">Main Course → Food</div>
              <div class="product-price">GH₵45.00</div>
            </div>

            <div class="product-card" style="background: #bd10e0">
              <div class="qty-badge">Qty: 0</div>
              <div class="product-name">Fried Rice</div>
              <div class="product-info">Main Course → Food</div>
              <div class="product-price">GH₵40.00</div>
            </div>

            <div class="product-card" style="background: #bd10e0">
              <div class="qty-badge">Qty: 0</div>
              <div class="product-name">Kelewele</div>
              <div class="product-info">Snacks → Food</div>
              <div class="product-price">GH₵25.00</div>
            </div>
          </div>

          <div class="pros-cons">
            <h4 class="pros">✓ Advantages:</h4>
            <ul class="pros">
              <li>Clear visual grouping by family</li>
              <li>Easy to spot related items</li>
              <li>Consistent brand/category association</li>
              <li>Less cognitive load for staff</li>
            </ul>

            <h4 class="cons">✗ Disadvantages:</h4>
            <ul class="cons">
              <li>Monotonous, lacks visual variety</li>
              <li>Harder to distinguish individual items</li>
              <li>Can feel bland and repetitive</li>
              <li>Reduced visual scanning efficiency</li>
            </ul>
          </div>
        </div>

        <!-- Independent Colors Approach -->
        <div class="approach independent">
          <h2>Independent Colors (Current System)</h2>
          <div class="pos-grid">
            <div class="category-header">DRINKS Family - Varied Colors</div>

            <div class="product-card" style="background: #bd10e0">
              <div class="qty-badge">Qty: 15</div>
              <div class="product-name">Coca-Cola 500ml</div>
              <div class="product-info">Soft Drinks → Drinks</div>
              <div class="product-price">GH₵23.00</div>
            </div>

            <div class="product-card" style="background: #50e3c2">
              <div class="qty-badge">Qty: 12</div>
              <div class="product-name">Sprite 500ml</div>
              <div class="product-info">Soft Drinks → Drinks</div>
              <div class="product-price">GH₵20.00</div>
            </div>

            <div class="product-card" style="background: #f5a623">
              <div class="qty-badge">Qty: 8</div>
              <div class="product-name">Fanta Orange</div>
              <div class="product-info">Soft Drinks → Drinks</div>
              <div class="product-price">GH₵23.00</div>
            </div>

            <div class="product-card" style="background: #7ed321">
              <div class="qty-badge">Qty: 5</div>
              <div class="product-name">Castle Lite</div>
              <div class="product-info">Alcoholic → Drinks</div>
              <div class="product-price">GH₵15.00</div>
            </div>

            <div class="product-card" style="background: #e85d75">
              <div class="qty-badge">Qty: 3</div>
              <div class="product-name">Star Beer</div>
              <div class="product-info">Alcoholic → Drinks</div>
              <div class="product-price">GH₵12.00</div>
            </div>

            <div class="product-card" style="background: #4a90e2">
              <div class="qty-badge">Qty: 7</div>
              <div class="product-name">Club Beer</div>
              <div class="product-info">Alcoholic → Drinks</div>
              <div class="product-price">GH₵18.00</div>
            </div>

            <div class="category-header">FOOD Family - Varied Colors</div>

            <div class="product-card" style="background: #ff6900">
              <div class="qty-badge">Qty: 0</div>
              <div class="product-name">Jollof Rice</div>
              <div class="product-info">Main Course → Food</div>
              <div class="product-price">GH₵45.00</div>
            </div>

            <div class="product-card" style="background: #9013fe">
              <div class="qty-badge">Qty: 0</div>
              <div class="product-name">Fried Rice</div>
              <div class="product-info">Main Course → Food</div>
              <div class="product-price">GH₵40.00</div>
            </div>

            <div class="product-card" style="background: #00d084">
              <div class="qty-badge">Qty: 0</div>
              <div class="product-name">Kelewele</div>
              <div class="product-info">Snacks → Food</div>
              <div class="product-price">GH₵25.00</div>
            </div>
          </div>

          <div class="pros-cons">
            <h4 class="pros">✓ Advantages:</h4>
            <ul class="pros">
              <li>Visually engaging and dynamic</li>
              <li>Easy to distinguish items at a glance</li>
              <li>Better for quick item location</li>
              <li>More appealing interface</li>
            </ul>

            <h4 class="cons">✗ Disadvantages:</h4>
            <ul class="cons">
              <li>No visual grouping by category</li>
              <li>Harder to see family relationships</li>
              <li>Potentially more chaotic</li>
              <li>May confuse category associations</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="recommendation">
        <h3>💡 Recommended Hybrid Approach</h3>
        <p>
          <strong>Use color inheritance with subtle variations</strong> - Keep
          the family base color but add slight hue/brightness shifts for
          subfamilies and individual items. This gives you both grouping AND
          distinctiveness!
        </p>

        <div style="margin-top: 15px; font-size: 14px">
          <strong>Example:</strong> Drinks Family = Blue base → Soft Drinks =
          Light Blue → Alcoholic = Dark Blue<br />
          Individual items get slight saturation/brightness tweaks within their
          subfamily color range.
        </div>
      </div>
    </div>
  </body>
</html>
