const { ipc<PERSON><PERSON><PERSON> } = require('electron');
class KOTApp {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 5000;
        this.maxReconnectDelay = 60000; // Cap at 1 minute
        this.orders = [];
        this.audioContext = null;
        this.soundEnabled = true;
        this.autoReconnect = true;
        this.backendUrl =   process.env.HTTP_BACKEND_URL;
        this.wsUrl =  process.env.WS_BACKEND_URL+'/ws/orders/';
        this.orderDelay = 30;
        this.uiCollapsed = false;
        this.selectedOrderIndex = -1;
        
        this.init();
    }

    async init() {
        // await this.loadEnvironmentVariables();
        this.loadUiState();
        this.setupEventListeners();
        this.setupAudio();
        this.connectWebSocket();
        this.fetchOrders();
        this.updateUI();
    }

    loadUiState() {
        this.uiCollapsed = localStorage.getItem('uiCollapsed') === 'true';
        if (this.uiCollapsed) {
            document.body.classList.add('ui-collapsed');
            document.getElementById('toggleUiBtn').classList.add('is-collapsed');
        }
    }

    toggleUi() {
        this.uiCollapsed = !this.uiCollapsed;
        document.body.classList.toggle('ui-collapsed');
        document.getElementById('toggleUiBtn').classList.toggle('is-collapsed');
        localStorage.setItem('uiCollapsed', this.uiCollapsed);
    }

    setupEventListeners() {
        document.getElementById('reconnectBtn').addEventListener('click', () => this.reconnect());
        document.getElementById('refreshDataBtn').addEventListener('click', () => this.refreshData());
        document.getElementById('clearLogBtn').addEventListener('click', () => this.clearLog());
        document.getElementById('toggleUiBtn').addEventListener('click', () => this.toggleUi());
        document.getElementById('recallDataBtn').addEventListener('click', () => this.showRecallModal());

        window.addEventListener('focus', () => {
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
        });

        window.addEventListener('keydown', (e) => this.handleKeyPress(e));
    }

    setupAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.error('Failed to initialize audio context:', error);
        }
    }

    async fetchOrders() {
        try {

            const apiUrl = `${this.backendUrl}/orders/kot-orders/`
            
            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('Orders API response:', data);
            
            if (data.data && data.data.data) {
                this.orders = data.data.data.orders || [];
                this.orderDelay = data.data.data.appSetting?.orderDelay || 1;
            } else if (data.data) {
                this.orders = data.data.orders || [];
                this.orderDelay = data.data.appSetting?.orderDelay || 1;
            } else {
                this.orders = data.orders || [];
                this.orderDelay = data.appSetting?.orderDelay || 1;
            }
            
            console.log('Processed orders:', this.orders.length);
            this.updateUI();
        } catch (error) {
            console.error('Failed to fetch orders:', error);
            this.log('Failed to fetch orders: ' + error.message, 'error');
        }
    }

    async markOrderAsComplete(orderId) {
        try {
            const apiUrl = `${this.backendUrl}/orders/${orderId}/kot-orders/`;
            
            const response = await fetch(apiUrl, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            this.log(`Order ${orderId} marked as complete`, 'success');
            
            setTimeout(() => {
                this.fetchOrders();
            }, 500);
            
        } catch (error) {
            this.log('Failed to mark order as complete: ' + error.message, 'error');
        }
    }

    connectWebSocket() {
        try {
            this.ws = new WebSocket(this.wsUrl);
            this.updateConnectionStatus('connecting');

            this.ws.onopen = () => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.updateConnectionStatus('connected');
                this.log('Connected to KOT server', 'success');
                
                // Fetch fresh data after reconnection
                this.fetchOrders();
            };

            this.ws.onmessage = (event) => {
                this.handleMessage(event.data);
            };

            this.ws.onerror = (error) => {
                this.log('WebSocket error: ' + error.message, 'error');
                this.updateConnectionStatus('disconnected');
            };

            this.ws.onclose = (event) => {
                this.isConnected = false;
                this.updateConnectionStatus('disconnected');
                this.log(`WebSocket connection closed (Code: ${event.code})`, 'warning');
                
                if (this.autoReconnect) {
                    this.scheduleReconnect();
                }
            };

        } catch (error) {
            this.log('Failed to create WebSocket connection: ' + error.message, 'error');
            this.updateConnectionStatus('disconnected');
        }
    }

    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
        
        // Log long disconnection warning after 10 attempts (roughly 5+ minutes)
        if (this.reconnectAttempts === 10) {
            this.log('⚠️  Connection lost for extended period. Continuing to retry...', 'warning');
        }
        
        this.log(`Reconnecting in ${delay/1000}s (attempt ${this.reconnectAttempts})`, 'warning');
        
        setTimeout(() => {
            if (!this.isConnected && this.autoReconnect) {
                this.connectWebSocket();
                // Also fetch orders after a short delay to ensure connection is established
                setTimeout(() => {
                    if (this.isConnected) {
                        this.fetchOrders();
                    }
                }, 2000);
            }
        }, delay);
    }

    reconnect() {
        if (this.ws) {
            this.ws.close();
        }
        this.reconnectAttempts = 0;
        this.connectWebSocket();
        this.fetchOrders();
    }

    refreshData() {
        this.log('🔄 Refreshing orders data...', 'info');
        this.fetchOrders();
    }

    handleMessage(data) {
        try {
            const message = JSON.parse(data);
            
            if (message.type === 'order_update' && message.message) {
                const orderInfo = message.message;
                
                if (orderInfo.is_kot) {
                    this.log(`KOT Order received: ${orderInfo.name}`, 'success');
                    if (this.soundEnabled) {
                        this.playSound();
                    }
                    setTimeout(() => {
                        this.fetchOrders();
                    }, 1000);
                }
            }
        } catch (error) {
            this.log('Failed to parse message: ' + error.message, 'error');
        }
    }

    playSound() {
        if (!this.audioContext || !this.soundEnabled) {
            return;
        }

        try {
            if (this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(880, this.audioContext.currentTime);

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            gainNode.gain.setValueAtTime(0.5, this.audioContext.currentTime);

            oscillator.start();
            setTimeout(() => {
                oscillator.stop();
            }, 200);

        } catch (error) {
            console.error('Failed to play sound:', error);
        }
    }

    updateConnectionStatus(status) {
        const statusDot = document.getElementById('connectionStatus');

        statusDot.classList.remove('connected', 'disconnected', 'connecting');
        
        switch (status) {
            case 'connected':
                statusDot.classList.add('connected');
                break;
            case 'disconnected':
                statusDot.classList.add('disconnected');
                break;
            case 'connecting':
                statusDot.classList.add('connecting');
                break;
        }
    }

    updateUI() {
        document.getElementById('orderCount').textContent = `${this.orders.length} orders`;
        this.renderOrders();
    }

    renderOrders() {
        const ordersGrid = document.getElementById('ordersGrid');
        const previousSelectedIndex = this.selectedOrderIndex;
        
        if (this.orders.length === 0) {
            ordersGrid.innerHTML = `
                <div class="no-orders">
                    <div class="no-orders-icon">📋</div>
                    <p>No pending orders</p>
                    <small>Orders will appear here when received</small>
                </div>
            `;
            this.selectedOrderIndex = -1;
            return;
        }

        ordersGrid.innerHTML = this.orders.map(order => this.createOrderCard(order)).join('');
        
        ordersGrid.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('click', () => {
                const orderId = card.dataset.orderId;
                this.markOrderAsComplete(orderId);
            });
        });

        let newSelectedIndex = -1;
        if (this.orders.length > 0) {
            newSelectedIndex = Math.max(0, Math.min(previousSelectedIndex, this.orders.length - 1));
        }
        this.selectOrder(newSelectedIndex);
    }

    createOrderCard(order) {
        const createdAt = new Date(order.createdAt);
        const backgroundColor = this.getOrderBackgroundColor(createdAt);
        
        return `
            <div class="order-card ${backgroundColor}" data-order-id="${order.id}">
                <div class="order-header">
                    <div class="order-table">${order.table?.name || 'Unknown Table'}</div>
                    <div class="order-status">${order.status}</div>
                </div>
                
                <div class="order-items">
                    ${order.orderItems?.map(item => `
                        <div class="order-item ${item.status === 'Voided' ? 'voided' : ''}">
                            <div class="order-item-name">${item.article?.name || 'Unknown Item'}</div>
                            <div class="order-item-qty">Qty: ${item.quantity}</div>
                        </div>
                    `).join('') || ''}
                </div>
                
                <div class="order-footer">
                    <p><span class="label">By:</span> <span class="value">${this.getCreatorName(order.createdBy)}</span></p>
                    <p><span class="label">Date:</span> <span class="value">${createdAt.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</span></p>
                    <p><span class="label">Time:</span> <span class="value">${createdAt.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true })}</span></p>
                </div>
            </div>
        `;
    }

    getOrderBackgroundColor(createdAt) {
        const now = new Date();
        const timeDiffMinutes = (now - createdAt) / (1000 * 60);
        
        if (timeDiffMinutes <= this.orderDelay) {
            return 'white';
        } else if (timeDiffMinutes <= this.orderDelay * 2) {
            return 'yellow';
        } else if (timeDiffMinutes <= this.orderDelay * 3) {
            return 'orange';
        } else {
            return 'red';
        }
    }

    getCreatorName(createdBy) {
        if (!createdBy) return 'Unknown';
        
        if (createdBy.firstName && createdBy.lastName) {
            return `${createdBy.firstName} ${createdBy.lastName}`;
        } else if (createdBy.code) {
            return createdBy.code;
        } else {
            return 'Unknown';
        }
    }

    log(message, type = 'info') {
        const logContent = document.getElementById('logContent');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
        
        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
        
        while (logContent.children.length > 100) {
            logContent.removeChild(logContent.firstChild);
        }
    }

    clearLog() {
        document.getElementById('logContent').innerHTML = '';
        this.log('Log cleared', 'info');
    }

    handleKeyPress(e) {
        if (this.orders.length === 0) return;

        switch (e.key) {
            case 'ArrowUp':
            case 'ArrowDown':
            case 'ArrowLeft':
            case 'ArrowRight':
                e.preventDefault();
                this.navigateGrid(e.key);
                break;
            case 'Enter':
                e.preventDefault();
                this.confirmSelection();
                break;
        }
    }

    navigateGrid(direction) {
        const grid = document.getElementById('ordersGrid');
        const cards = grid.querySelectorAll('.order-card');
        if (cards.length === 0) return;

        const numColumns = getComputedStyle(grid).gridTemplateColumns.split(' ').length;
        let newIndex = this.selectedOrderIndex;

        if (this.selectedOrderIndex === -1) {
            newIndex = 0;
        } else {
            switch (direction) {
                case 'ArrowUp':
                    newIndex = Math.max(0, this.selectedOrderIndex - numColumns);
                    break;
                case 'ArrowDown':
                    newIndex = Math.min(cards.length - 1, this.selectedOrderIndex + numColumns);
                    break;
                case 'ArrowLeft':
                    newIndex = Math.max(0, this.selectedOrderIndex - 1);
                    break;
                case 'ArrowRight':
                    newIndex = Math.min(cards.length - 1, this.selectedOrderIndex + 1);
                    break;
            }
        }
        this.selectOrder(newIndex);
    }

    selectOrder(index) {
        const cards = document.querySelectorAll('.order-card');
        if (this.selectedOrderIndex !== -1 && cards[this.selectedOrderIndex]) {
            cards[this.selectedOrderIndex].classList.remove('selected');
        }

        this.selectedOrderIndex = index;

        if (this.selectedOrderIndex !== -1 && cards[this.selectedOrderIndex]) {
            const selectedCard = cards[this.selectedOrderIndex];
            selectedCard.classList.add('selected');
            selectedCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    confirmSelection() {
        if (this.selectedOrderIndex !== -1) {
            const selectedCard = document.querySelectorAll('.order-card')[this.selectedOrderIndex];
            if (selectedCard) {
                const orderId = selectedCard.dataset.orderId;
                this.markOrderAsComplete(orderId);
            }
        }
    }

    showRecallModal() {
        let modal = document.getElementById('recallModal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'recallModal';
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100vw';
            modal.style.height = '100vh';
            modal.style.background = 'rgba(0,0,0,0.6)';
            modal.style.display = 'flex';
            modal.style.alignItems = 'center';
            modal.style.justifyContent = 'center';
            modal.style.zIndex = '9999';
            document.body.appendChild(modal);
        }
        modal.innerHTML = '';
        const content = document.createElement('div');
        content.style.background = '#232323';
        content.style.borderRadius = '16px';
        content.style.padding = '2em 2.5em';
        content.style.minWidth = '350px';
        content.style.maxWidth = '90vw';
        content.style.maxHeight = '80vh';
        content.style.overflowY = 'auto';
        content.style.boxShadow = '0 8px 32px rgba(0,0,0,0.4)';
        
        const title = document.createElement('h2');
        title.textContent = 'Recall Order (Set to Printed)';
        title.style.color = '#fff';
        title.style.marginBottom = '1em';
        content.appendChild(title);
        
        const allOrders = this.orders;
        if (allOrders.length === 0) {
            const noOrders = document.createElement('div');
            noOrders.style.color = '#fff';
            noOrders.style.padding = '1em';
            noOrders.textContent = 'No orders available.';
            content.appendChild(noOrders);
        } else {
            const ul = document.createElement('ul');
            ul.style.listStyle = 'none';
            ul.style.padding = '0';
            allOrders.forEach(order => {
                const creator = order.createdBy || {};
                const creatorName = (creator.firstName || creator.first_name || '') + (creator.lastName || creator.last_name ? ' ' + (creator.lastName || creator.last_name) : '');
                const displayName = `${order.name || order.table?.name || 'No Name'} - Created by: ${creatorName || 'Unknown'} [${order.status}]`;
                const li = document.createElement('li');
                li.style.margin = '0.5em 0';
                li.style.background = '#18181b';
                li.style.color = '#fff';
                li.style.padding = '0.75em 1em';
                li.style.borderRadius = '8px';
                li.style.cursor = 'pointer';
                li.textContent = displayName;
                li.addEventListener('click', () => this.recallOrderFromModal(order));
                ul.appendChild(li);
            });
            content.appendChild(ul);
        }
        // Close button
        const closeBtn = document.createElement('button');
        closeBtn.textContent = 'Close';
        closeBtn.style.marginTop = '2em';
        closeBtn.style.background = '#444';
        closeBtn.style.color = '#fff';
        closeBtn.style.padding = '0.5em 2em';
        closeBtn.style.border = 'none';
        closeBtn.style.borderRadius = '8px';
        closeBtn.style.fontSize = '1.1em';
        closeBtn.style.cursor = 'pointer';
        closeBtn.addEventListener('click', () => { modal.style.display = 'none'; });
        content.appendChild(closeBtn);
        modal.appendChild(content);
        modal.style.display = 'flex';
    }

    async recallOrderFromModal(order) {
        const modal = document.getElementById('recallModal');
        if (modal) {
            modal.innerHTML = '<div style="color:#fff;padding:2em;">Recalling order...</div>';
        }
        try {
            const response = await fetch(`${this.backendUrl}/api/orders/${order.id}/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status: 'Printed' })
            });
            if (!response.ok) throw new Error('Failed to update order');
            this.log(`Order #${order.id} recalled (set to Printed)`, 'success');
            await this.fetchOrders();
        } catch (e) {
            this.log(`Failed to recall order #${order.id}: ${e.message}`, 'error');
        } finally {
            if (modal) modal.style.display = 'none';
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new KOTApp();
}); 