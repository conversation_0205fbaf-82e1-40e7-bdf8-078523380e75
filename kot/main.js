require('dotenv').config();
const { app, BrowserWindow, ipcMain, Menu } = require('electron');
const path = require('path');

let mainWindow;

function createWindow() {
  const isFullScreen = process.env.OPEN_IN_FULL_SCREEN === 'true';
  const hideCloseButton = process.env.HIDE_CLOSE_BUTTON === 'true';
  const hideMinimizeButton = process.env.HIDE_MINIMIZE_BUTTON === 'true';

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    fullscreen: isFullScreen,
    closable: !hideCloseButton,
    minimizable: !hideMinimizeButton,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    title: 'Kitchen Order Display',
    show: false, // Don't show until ready
    autoHideMenuBar: true
  });

  // Load the index.html file
  mainWindow.loadFile('index.html');

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Minimize to system tray on startup (optional)
    // mainWindow.minimize();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Prevent new window creation
  mainWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });
}

// Create window when Electron is ready
app.whenReady().then(() => {
  createWindow();

  // On macOS, re-create window when dock icon is clicked
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed (except on macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// Handle IPC messages from renderer
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-env-vars', () => {
 
  
  return {
    HTTP_BACKEND_URL: process.env.HTTP_BACKEND_URL,
    WS_BACKEND_URL: process.env.WS_BACKEND_URL,
    NODE_ENV: process.env.NODE_ENV,
  };
});

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// Security: Prevent navigation to external URLs
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    // Only allow local files
    if (parsedUrl.protocol !== 'file:') {
      event.preventDefault();
    }
  });
}); 