# KOT Sound Player - Electron App

A standalone Electron application for Kitchen Order Ticket (KOT) sound notifications. This app connects to your Django WebSocket server and plays sound alerts when new KOT orders are received, without browser restrictions.

## Features

- 🔔 **Sound Notifications**: Plays beep sounds when KOT orders are received
- 🔄 **Auto Reconnect**: Automatically reconnects to WebSocket server with exponential backoff
- ⚙️ **Configurable Settings**: Customize backend URL, sound settings, and auto-reconnect
- 📊 **Real-time Logging**: View connection status and event logs
- 🎨 **Modern UI**: Clean, dark theme interface
- 💾 **Persistent Settings**: Settings are saved locally and restored on startup

## Installation

1. **Install Dependencies**
   ```bash
   cd kot
   npm install
   ```

2. **Start the App**
   ```bash
   npm start
   ```

3. **Build for Distribution** (Optional)
   ```bash
   npm run build
   ```

## Configuration

### Backend URL
Set the WebSocket URL to your Django backend:
- Default: `ws://localhost:8000/ws/orders/`
- Format: `ws://your-server:port/ws/orders/`

### Settings
- **Sound Notifications**: Enable/disable sound alerts
- **Auto Reconnect**: Enable automatic reconnection on connection loss
- **Test Sound**: Test the sound notification

## Usage

1. **Start the App**: Run `npm start` to launch the application
2. **Configure Backend**: Set your Django WebSocket URL in the settings panel
3. **Monitor Connection**: Watch the connection status indicator (green = connected, red = disconnected)
4. **Receive Notifications**: When KOT orders are sent, the app will play a beep sound
5. **View Logs**: Check the event log for connection status and received messages

## File Structure

```
kot/
├── main.js              # Main Electron process
├── renderer.js          # Renderer process (UI logic)
├── index.html           # Main UI
├── styles.css           # Styling
├── package.json         # Dependencies and scripts
└── README.md           # This file
```

## Development

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Development Commands
```bash
# Install dependencies
npm install

# Start in development mode
npm run dev

# Build for distribution
npm run build

# Create distributable packages
npm run dist
```

### Building for Different Platforms

The app can be built for:
- **Windows**: Creates an NSIS installer
- **macOS**: Creates a DMG file
- **Linux**: Creates an AppImage

## Troubleshooting

### Sound Not Working
1. Check if sound is enabled in settings
2. Test sound using the "Test Sound" button
3. Ensure your system volume is not muted
4. Check browser console for audio context errors

### Connection Issues
1. Verify the WebSocket URL is correct
2. Ensure your Django server is running
3. Check firewall settings
4. View the event log for connection errors

### App Not Starting
1. Ensure Node.js is installed
2. Run `npm install` to install dependencies
3. Check for any error messages in the terminal

## Integration with Django

This app expects WebSocket messages in the following format:
```json
{
  "type": "order_update",
  "message": {
    "id": "order-id",
    "name": "Table Name",
    "created_at": "2023-01-01 12:00:00",
    "action": "created",
    "is_kot": true
  }
}
```

The app will only play sounds when `is_kot` is `true`.

## License

MIT License - see LICENSE file for details.

## Support

For issues or questions, please check the event log in the app or refer to the troubleshooting section above. # kot
