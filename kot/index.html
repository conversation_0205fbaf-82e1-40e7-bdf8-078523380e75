<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kitchen Order Display</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <h1 class="title">Kitchen Order Ticket</h1>
            </div>
            <div class="header-right">
                <div class="controls">
                </div>
            </div>
        </header>

        <button id="toggleUiBtn" class="btn btn-secondary btn-small" title="Toggle UI Panels">👁️</button>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Orders Display -->
            <div class="orders-section">
                <div class="section-header">
                    <h2>Pending Orders <div id="connectionStatus" class="status-dot"></div></h2>
                    <div class="order-count" id="orderCount">0 orders</div>
                </div>
                
                <div class="orders-grid" id="ordersGrid">
                    <div class="no-orders">
                        <div class="no-orders-icon">📋</div>
                        <p>No pending orders</p>
                        <small>Orders will appear here when received</small>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer / Log Section -->
        <footer class="log-section" id="logSection">
            <div class="log-header">
                <h3>Event Log</h3>
                <div>
                    <button id="refreshDataBtn" class="btn btn-primary btn-small">Refresh Data</button>
                    <button id="reconnectBtn" class="btn btn-primary btn-small">Reconnect</button>
                    <button id="clearLogBtn" class="btn btn-secondary btn-small">Clear</button>
                    <button id="recallDataBtn" class="btn btn-primary btn-small">Recall data</button>
                </div>
            </div>
            <div class="log-content" id="logContent"></div>
            <div id="recallListContainer" style="display:none;"></div>
        </footer>
    </div>

    <script src="renderer.js"></script>
</body>
</html> 