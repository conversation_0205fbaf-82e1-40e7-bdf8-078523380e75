/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
    user-select: none;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: #2d2d2d;
    border-bottom: 1px solid #404040;
    min-height: 70px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #ffffff;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #666;
    transition: background-color 0.3s ease;
    display: inline-block;
    margin-left: 0.5rem;
    vertical-align: middle;
}

.status-dot.connected {
    background-color: #10b981;
}

.status-dot.disconnected {
    background-color: #ef4444;
}

.status-dot.connecting {
    background-color: #f59e0b;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.header-right {
    display: flex;
    align-items: center;
}

.controls {
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-small {
    padding: 0.15rem 0.4rem;
    font-size: 0.7rem;
    min-width: auto;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Orders Section */
.orders-section {
    flex: 1;
    padding: 1.5rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #ffffff;
}

.order-count {
    background-color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.875rem;
    color: #d1d5db;
}

.orders-grid {
    flex: 1;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 300px));
    gap: 1rem;
    padding-right: 0.5rem;
}

.orders-grid::-webkit-scrollbar {
    width: 8px;
}

.orders-grid::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 4px;
}

.orders-grid::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 4px;
}

.orders-grid::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

.no-orders {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #9ca3af;
    text-align: center;
}

.no-orders-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.no-orders p {
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
}

.no-orders small {
    font-size: 0.875rem;
    opacity: 0.7;
}

/* Order Cards */
.order-card {
    background-color: #262626;
    border-radius: 12px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #404040;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 300px;
    max-width: 300px;
    min-height: 200px;
}

.order-card.selected {
    border: 3px solid #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
    transform: translateY(-4px) scale(1.02);
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.order-card:active {
    transform: translateY(0);
}

.order-card.white {
    background-color: #ffffff;
    color: #000000;
}

.order-card.yellow {
    background-color: #fef3c7;
    color: #000000;
}

.order-card.orange {
    background-color: #fed7aa;
    color: #000000;
}

.order-card.red {
    background-color: #fecaca;
    color: #000000;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-table {
    font-weight: 600;
    font-size: 1.125rem;
}

.order-status {
    background-color: #f59e0b;
    color: #000000;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.order-items {
    flex: 1;
}

.order-item {
    background-color: #374151;
    padding: 0.5rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
}

.order-item.voided {
    text-decoration: line-through;
    opacity: 0.6;
    background-color: #4b5563;
}

.order-item:last-child {
    margin-bottom: 0;
}

.order-item-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.order-item-qty {
    font-size: 0.875rem;
    opacity: 0.8;
}

.order-footer {
    border-top: 1px solid #404040;
    padding-top: 0.75rem;
}

.order-footer p {
    margin-bottom: 0.25rem;
}

.order-footer p:last-child {
    margin-bottom: 0;
}

.order-footer .label {
    font-weight: 500;
    opacity: 0.7;
}

.order-footer .value {
    font-weight: 600;
}

.order-card.white .order-item {
    background-color: #f3f4f6;
    color: #000000;
}

.order-card.yellow .order-item {
    background-color: #fde68a;
    color: #000000;
}

.order-card.orange .order-item {
    background-color: #fdba74;
    color: #000000;
}

.order-card.red .order-item {
    background-color: #fca5a5;
    color: #000000;
}

.order-card.white .order-footer {
    border-top-color: #e5e7eb;
}

.order-card.yellow .order-footer {
    border-top-color: #f59e0b;
}

.order-card.orange .order-footer {
    border-top-color: #ea580c;
}

.order-card.red .order-footer {
    border-top-color: #dc2626;
}

/* Log Section */
.log-section {
    width: 300px;
    background-color: #2d2d2d;
    border-left: 1px solid #404040;
    display: flex;
    flex-direction: column;
}

.log-header {
    padding: 1rem;
    border-bottom: 1px solid #404040;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.log-header div {
    display: flex;
    gap: 0.5rem;
}

.log-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
}

.log-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    line-height: 1.4;
}

.log-content::-webkit-scrollbar {
    width: 6px;
}

.log-content::-webkit-scrollbar-track {
    background: #374151;
}

.log-content::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 3px;
}

.log-entry {
    margin-bottom: 0.5rem;
    word-wrap: break-word;
}

.log-entry:last-child {
    margin-bottom: 0;
}

.log-entry .timestamp {
    color: #9ca3af;
    font-weight: 600;
}

.log-entry.info {
    color: #d1d5db;
}

.log-entry.warning {
    color: #f59e0b;
}

.log-entry.error {
    color: #ef4444;
}

.log-entry.success {
    color: #10b981;
}

/* UI Toggling for Focus Mode */
#toggleUiBtn {
    position: fixed;
    top: 23px;
    right: 24px;
    z-index: 1000;
    transition: all 0.3s ease;
}

#toggleUiBtn.is-collapsed {
    top: 10px;
    right: 10px;
    opacity: 0.5;
}

#toggleUiBtn:hover {
    opacity: 1;
}

body.ui-collapsed .header,
body.ui-collapsed .log-section {
    display: none;
}

body.ui-collapsed .main-content {
    height: 100vh;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 0.75rem 1rem;
        min-height: 60px;
    }

    .main-content {
        flex-direction: column;
    }

    .orders-grid {
        grid-template-columns: 1fr;
    }
}