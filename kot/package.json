{"name": "kot-electron-app", "version": "1.0.0", "description": "Electron app for Kitchen Order Ticket with sound notifications", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "kot", "restaurant", "pos"], "author": "Lambda POS", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"ws": "^8.14.2", "dotenv": "^16.3.1"}, "build": {"appId": "com.lambda.kot", "productName": "KOT Sound Player", "directories": {"output": "dist"}, "files": ["main.js", "renderer.js", "index.html", "styles.css", ".env", "assets/**/*"], "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}}}