const express = require("express");
const router = express.Router();
const { isVerifiedUser } = require("../middlewares/tokenVerification");
const {
  createOrder,
  verifyPayment,
  webHookVerification,
} = require("../controllers/paymentController");

/**
 * @swagger
 * /api/payment/create-order:
 *   post:
 *     summary: Create a payment order
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       201:
 *         description: Payment order created
 *       400:
 *         description: Bad request
 */

/**
 * @swagger
 * /api/payment/verify-payment:
 *   post:
 *     summary: Verify a payment
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Payment verified
 *       400:
 *         description: Verification failed
 */

/**
 * @swagger
 * /api/payment/webhook-verification:
 *   post:
 *     summary: Webhook verification for payment
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processed
 *       400:
 *         description: Webhook failed
 */

router.route("/create-order").post(isVerifiedUser, createOrder);
router.route("/verify-payment").post(isVerifiedUser, verifyPayment);
router.route("/webhook-verification").post(webHookVerification);

module.exports = router;
