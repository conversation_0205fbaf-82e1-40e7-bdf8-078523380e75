const express = require("express");
const {
  addTable,
  getTables,
  updateTable,
} = require("../controllers/tableController");
const router = express.Router();
const { isVerifiedUser } = require("../middlewares/tokenVerification");

/**
 * @swagger
 * /api/table:
 *   post:
 *     summary: Add a new table
 *     tags: [Table]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       201:
 *         description: Table added successfully
 *       400:
 *         description: Bad request
 *   get:
 *     summary: Get all tables
 *     tags: [Table]
 *     responses:
 *       200:
 *         description: List of tables
 */
router.route("/").post(isVerifiedUser, addTable);
router.route("/").get(isVerifiedUser, getTables);

/**
 * @swagger
 * /api/table/{id}:
 *   put:
 *     summary: Update table by ID
 *     tags: [Table]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Table updated
 *       404:
 *         description: Table not found
 */
router.route("/:id").put(isVerifiedUser, updateTable);

module.exports = router;
