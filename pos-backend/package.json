{"name": "pos-backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon app.js", "start": "node app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "express-oas-generator": "^1.0.48", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.5", "razorpay": "^2.9.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"nodemon": "^3.1.9"}, "description": ""}