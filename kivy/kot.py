#!/usr/bin/env python3
"""
Kitchen Order Ticket (KOT) - <PERSON>vy App
Beautiful, modern UI design with proper layout
"""

import kivy
kivy.require('2.3.1')

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.widget import Widget
from kivy.core.audio import SoundLoader
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, NumericProperty, ListProperty
from kivy.uix.behaviors import ButtonBehavior
from kivy.core.window import Window
from kivy.graphics import Color, RoundedRectangle, Line, Ellipse
from kivy.animation import Animation
import websocket
import threading
import json
import os
import requests
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()
WS_BACKEND_URL = f"{os.getenv('WS_BACKEND_URL').rstrip('/')}"
HTTP_BACKEND_URL = f"{os.getenv('HTTP_BACKEND_URL').rstrip('/')}"


class ModernButton(ButtonBehavior, Widget):
    """Modern styled button with hover effects"""
    text = StringProperty("")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size_hint = (None, None)
        self.size = (dp(120), dp(40))
        
        with self.canvas.before:
            Color(0.2, 0.6, 0.9, 1)  # Blue background
            self.bg_rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[20])
            
        self.label = Label(
            text=self.text,
            color=(1, 1, 1, 1),
            font_size=dp(14),
            bold=True
        )
        self.add_widget(self.label)
        
        self.bind(pos=self.update_bg, size=self.update_bg, text=self.update_label)
    
    def update_bg(self, *args):
        self.bg_rect.pos = self.pos
        self.bg_rect.size = self.size
    
    def update_label(self, *args):
        self.label.text = self.text
    
    def on_press(self):
        # Animate button press
        anim = Animation(size=(self.size[0] * 0.95, self.size[1] * 0.95), duration=0.1)
        anim += Animation(size=self.size, duration=0.1)
        anim.start(self)


class StatusBadge(Widget):
    """Status badge widget"""
    text = StringProperty("")
    bg_color = ListProperty([1, 0.8, 0, 1])  # Yellow by default
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size_hint = (None, None)
        self.size = (dp(80), dp(30))
        
        with self.canvas.before:
            Color(*self.bg_color)
            self.bg_rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[15])
            
        self.label = Label(
            text=self.text,
            color=(0, 0, 0, 1),
            font_size=dp(11),
            bold=True
        )
        self.add_widget(self.label)
        
        self.bind(pos=self.update_bg, size=self.update_bg, text=self.update_label)
    
    def update_bg(self, *args):
        self.bg_rect.pos = self.pos
        self.bg_rect.size = self.size
        self.canvas.before.clear()
        with self.canvas.before:
            Color(*self.bg_color)
            self.bg_rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[15])
    
    def update_label(self, *args):
        self.label.text = self.text


class OrderCard(ButtonBehavior, BoxLayout):
    """Beautiful order card with modern design"""
    
    def __init__(self, order_data, on_click_callback, **kwargs):
        super().__init__(**kwargs)
        self.order_data = order_data
        self.on_click_callback = on_click_callback
        self.size_hint_y = None
        self.height = dp(160)
        
        # Set layout properties directly on the class
        self.orientation = 'vertical'
        self.padding = dp(20)
        self.spacing = dp(12)
        
        # Extract data
        self.table_code = str(order_data.get('tableCode', 'N/A'))
        self.table_name = str(order_data.get('tableName', 'N/A'))
        
        article = order_data.get('article', {})
        if isinstance(article, dict):
            self.item_name = article.get('name', 'Unknown Item')
            self.description = article.get('description', article.get('notes', ''))
        else:
            self.item_name = str(article) if article else 'Unknown Item'
            self.description = ''
        
        self.quantity = order_data.get('quantity', 0)
        self.item_id = str(order_data.get('id', ''))
        
        created_by_info = order_data.get('createdBy', {})
        if isinstance(created_by_info, dict):
            first_name = created_by_info.get('firstName', '')
            last_name = created_by_info.get('lastName', '')
            if first_name or last_name:
                self.created_by_name = f"{first_name} {last_name}".strip()
            else:
                self.created_by_name = ""
        else:
            self.created_by_name = 'Unknown'
        
        # Format time
        created_at = order_data.get('createdAt', '')
        try:
            if created_at and 'T' in created_at:
                if created_at.endswith('Z'):
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                else:
                    dt = datetime.fromisoformat(created_at)
                self.created_at = dt.strftime('%H:%M:%S')
            else:
                self.created_at = created_at[:8] if len(created_at) >= 8 else created_at
        except:
            self.created_at = created_at[:8] if len(created_at) >= 8 else created_at
        
        self.setup_ui()
    
    def setup_ui(self):
        # Card background with shadow effect
        with self.canvas.before:
            # Shadow
            Color(0, 0, 0, 0.3)
            self.shadow = RoundedRectangle(
                pos=(self.x + dp(2), self.y - dp(2)), 
                size=self.size, 
                radius=[12]
            )
            # Card background
            Color(0.18, 0.18, 0.18, 1)  # Dark card background
            self.bg_rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[12])
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        
        # Header section
        header = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(35))
        
        # Table badge
        table_badge = StatusBadge(
            text=f"#{self.table_code}",
            bg_color=[1, 0.8, 0, 1]  # Yellow
        )
        header.add_widget(table_badge)
        
        # Spacer
        header.add_widget(Widget(size_hint_x=0.3))
        
        # Table name
        table_label = Label(
            text=f"Table {self.table_name}",
            color=(0.9, 0.9, 0.9, 1),
            font_size=dp(16),
            bold=True,
            halign='center'
        )
        table_label.bind(texture_size=table_label.setter('text_size'))
        header.add_widget(table_label)
        
        # Spacer
        header.add_widget(Widget(size_hint_x=0.3))
        
        # Time badge
        time_badge = StatusBadge(
            text=self.created_at,
            bg_color=[0.3, 0.8, 0.3, 1]  # Green
        )
        header.add_widget(time_badge)
        
        self.add_widget(header)
        
        # Divider line
        divider = Widget(size_hint_y=None, height=dp(1))
        with divider.canvas:
            Color(0.4, 0.4, 0.4, 1)
            # Bind the line to the divider's width
            self.divider_line = Line(points=[self.x, self.y, self.x + self.width, self.y], width=1)
            divider.bind(width=lambda instance, width: setattr(self.divider_line, 'points', [instance.x, instance.y, instance.x + width, instance.y]))
        self.add_widget(divider)
        
        # Item details section
        details = BoxLayout(orientation='vertical', spacing=dp(8))
        
        # Item name
        item_label = Label(
            text=self.item_name,
            color=(1, 1, 1, 1),
            font_size=dp(18),
            bold=True,
            halign='left',
            size_hint_y=None,
            height=dp(25)
        )
        item_label.bind(texture_size=item_label.setter('text_size'))
        details.add_widget(item_label)
        
        # Quantity and description row
        info_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(20))
        
        quantity_label = Label(
            text=f"Qty: {self.quantity}",
            color=(0.8, 0.8, 0.8, 1),
            font_size=dp(14),
            halign='left',
            size_hint_x=0.3
        )
        quantity_label.bind(texture_size=quantity_label.setter('text_size'))
        info_row.add_widget(quantity_label)
        
        if self.description:
            desc_label = Label(
                text=f"By: {self.created_by_name}",
                color=(0.7, 0.7, 0.7, 1),
                font_size=dp(12),
                halign='left'
            )
            desc_label.bind(texture_size=desc_label.setter('text_size'))
            info_row.add_widget(desc_label)
        
        details.add_widget(info_row)
        
        # Action button
        action_btn = ModernButton(text="MARK DONE")
        action_btn.bind(on_press=self.on_action_press)
        
        action_container = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50))
        action_container.add_widget(Widget())  # Spacer
        action_container.add_widget(action_btn)
        action_container.add_widget(Widget())  # Spacer
        
        details.add_widget(action_container)
        
        self.add_widget(details)
    
    def update_graphics(self, *args):
        self.shadow.pos = (self.x + dp(2), self.y - dp(2))
        self.shadow.size = self.size
        self.bg_rect.pos = self.pos
        self.bg_rect.size = self.size
        self.divider_line.points = [self.x, self.y, self.x + self.width, self.y]
    
    def on_action_press(self, instance):
        if self.on_click_callback:
            self.on_click_callback(self.item_id)
    
    def on_press(self):
        # Animate card press
        anim = Animation(size=(self.width * 0.98, self.height * 0.98), duration=0.1)
        anim += Animation(size=(self.width, self.height), duration=0.1)
        anim.start(self)


class HeaderWidget(Widget):
    """Modern header with gradient background"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size_hint_y = None
        self.height = dp(80)
        
        with self.canvas.before:
            # Gradient background (simulated with multiple rectangles)
            Color(0.1, 0.2, 0.4, 1)  # Dark blue
            self.bg1 = RoundedRectangle(pos=self.pos, size=self.size, radius=[0, 0, 20, 20])
            Color(0.2, 0.3, 0.5, 0.8)
            self.bg2 = RoundedRectangle(pos=self.pos, size=(self.size[0], self.size[1]/2), radius=[0, 0, 20, 20])
        
        self.bind(pos=self.update_bg, size=self.update_bg)
        
        # Header content
        layout = BoxLayout(orientation='horizontal', padding=dp(25), spacing=dp(20))
        
        # App title with icon
        title_container = BoxLayout(orientation='horizontal', spacing=dp(15), size_hint_x=0.6)
        
        # Icon placeholder (you can replace with actual icon)
        icon = Widget(size_hint_x=None, width=dp(50))
        with icon.canvas:
            Color(1, 0.8, 0, 1)  # Yellow
            Ellipse(pos=(dp(10), dp(20)), size=(dp(40), dp(40)))
            Color(0.1, 0.1, 0.1, 1)
            Line(circle=(dp(30), dp(40), dp(15)), width=3)
        title_container.add_widget(icon)
        
        title = Label(
            text="Kitchen Order Ticket",
            color=(1, 1, 1, 1),
            font_size=dp(24),
            bold=True,
            halign='left'
        )
        title.bind(texture_size=title.setter('text_size'))
        title_container.add_widget(title)
        
        layout.add_widget(title_container)
        
        # Stats section
        self.stats_container = BoxLayout(orientation='horizontal', spacing=dp(20), size_hint_x=0.4)
        
        # Pending orders counter
        self.pending_widget = self.create_stat_widget("0", "Pending Orders", [1, 0.5, 0, 1])
        self.stats_container.add_widget(self.pending_widget)
        
        # Status indicator
        self.status_widget = self.create_stat_widget("●", "Online", [0, 0.8, 0, 1])
        self.stats_container.add_widget(self.status_widget)
        
        layout.add_widget(self.stats_container)
        self.add_widget(layout)
    
    def create_stat_widget(self, value, label, color):
        container = BoxLayout(orientation='vertical', spacing=dp(5))
        
        value_label = Label(
            text=value,
            color=color,
            font_size=dp(20),
            bold=True
        )
        
        label_widget = Label(
            text=label,
            color=(0.8, 0.8, 0.8, 1),
            font_size=dp(12)
        )
        
        container.add_widget(value_label)
        container.add_widget(label_widget)
        return container
    
    def update_bg(self, *args):
        self.bg1.pos = self.pos
        self.bg1.size = self.size
        self.bg2.pos = self.pos
        self.bg2.size = (self.size[0], self.size[1]/2)
    
    def update_pending_count(self, count):
        if self.pending_widget and len(self.pending_widget.children) >= 2:
            self.pending_widget.children[1].text = str(count)


class WebSocketClient:
    def __init__(self, url, on_message_callback):
        self.url = url
        self.on_message_callback = on_message_callback
        self.ws = None
        self.connected = False
        
    def connect(self):
        try:
            print(f"Connecting to WebSocket: {self.url}")
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            self.ws.run_forever()
        except Exception as e:
            print(f"WebSocket connection error: {e}")
    
    def on_open(self, ws):
        print("Connected to Django WebSocket")
        self.connected = True
        
    def on_message(self, ws, message):
        print(f"Message received: {message}")
        Clock.schedule_once(lambda dt: self.on_message_callback(message), 0)
            
    def on_error(self, ws, error):
        print(f"WebSocket error: {error}")
        self.connected = False
        
    def on_close(self, ws, close_status_code, close_msg):
        print("WebSocket connection closed")
        self.connected = False
        
    def disconnect(self):
        if self.ws:
            self.ws.close()


class SoundPlayer:
    def __init__(self):
        self.sound = None
        self.load_sound()
        
    def load_sound(self):
        sound_paths = [
            "notification.wav", "alert.wav", "beep.wav",
            "sounds/notification.wav", "sounds/alert.wav"
        ]
        
        for path in sound_paths:
            if os.path.exists(path):
                try:
                    self.sound = SoundLoader.load(path)
                    if self.sound:
                        print(f"Loaded sound: {path}")
                        return
                except Exception as e:
                    print(f"Failed to load {path}: {e}")
        
        print("No sound file found, creating a default beep.")
        self.create_simple_beep()
    
    def create_simple_beep(self):
        """Create a simple beep sound if no sound file is available"""
        try:
            import wave
            import struct
            import math
            
            sample_rate = 44100
            duration = 0.12
            frequency = 880
            
            num_samples = int(sample_rate * duration)
            audio_data = []
            
            for i in range(num_samples):
                value = math.sin(2 * math.pi * frequency * i / sample_rate)
                audio_data.append(int(value * 32767))
            
            with wave.open("beep.wav", 'w') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(sample_rate)
                
                for sample in audio_data:
                    wav_file.writeframes(struct.pack('<h', sample))
            
            self.sound = SoundLoader.load("beep.wav")
            print("Created and loaded beep.wav")
            
        except Exception as e:
            print(f"Failed to create beep sound: {e}")
    
    def play_sound(self):
        if self.sound:
            try:
                self.sound.play()
                print("Sound played")
            except Exception as e:
                print(f"Failed to play sound: {e}")
        else:
            print("No sound available")


class KOTApp(App):
    def __init__(self):
        super().__init__()
        self.websocket_client = None
        self.sound_player = SoundPlayer()
        self.connection_thread = None
        self.orders = []
        self.is_loading = True
        self.error_message = ""
        
    def build(self):
        self.title = "Kitchen Order Ticket - Modern"
        
        # Set window properties
        Window.size = (1000, 800)
        Window.minimum_width = 800
        Window.minimum_height = 600
        Window.clearcolor = (0.05, 0.05, 0.05, 1)  # Very dark background
        
        # Main container
        main_container = FloatLayout()
        
        # Background gradient effect
        with main_container.canvas.before:
            Color(0.05, 0.05, 0.1, 1)
            self.main_bg = RoundedRectangle(pos=(0, 0), size=Window.size)
        
        # Main layout
        main_layout = BoxLayout(orientation='vertical')
        
        # Header
        self.header = HeaderWidget()
        main_layout.add_widget(self.header)
        
        # Content area with padding
        content_container = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(20))
        
        # Loading/Error overlay
        self.overlay_container = FloatLayout()
        
        # Loading spinner area
        self.loading_container = BoxLayout(
            orientation='vertical', 
            spacing=dp(20),
            size_hint=(None, None),
            size=(dp(200), dp(100)),
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        
        self.loading_label = Label(
            text="Loading orders...",
            color=(0.8, 0.8, 0.8, 1),
            font_size=dp(18)
        )
        
        # Error container
        self.error_container = BoxLayout(
            orientation='vertical',
            spacing=dp(15),
            size_hint=(None, None),
            size=(dp(400), dp(150)),
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        
        self.error_label = Label(
            text="",
            color=(1, 0.4, 0.4, 1),
            font_size=dp(16),
            text_size=(dp(350), None),
            halign='center'
        )
        
        retry_btn = ModernButton(text="Retry")
        retry_btn.bind(on_press=lambda x: self.fetch_orders())
        
        self.error_container.add_widget(self.error_label)
        self.error_container.add_widget(retry_btn)
        
        # Orders scroll view
        self.orders_container = GridLayout(
            cols=1,
            spacing=dp(15),
            size_hint_y=None,
            padding=dp(10)
        )
        self.orders_container.bind(minimum_height=self.orders_container.setter('height'))
        
        scroll_view = ScrollView(
            do_scroll_x=False,
            bar_width=dp(10),
            scroll_wheel_distance=dp(60),
            bar_color=[0.3, 0.7, 1, 0.5],
            bar_inactive_color=[0.3, 0.7, 1, 0.2]
        )
        scroll_view.add_widget(self.orders_container)
        
        content_container.add_widget(scroll_view)
        
        # Floating refresh button
        refresh_btn = ModernButton(text="Refresh")
        refresh_btn.size = (dp(100), dp(40))
        refresh_btn.pos_hint = {'right': 0.95, 'y': 0.05}
        refresh_btn.bind(on_press=lambda x: self.fetch_orders())
        
        main_layout.add_widget(content_container)
        main_container.add_widget(main_layout)
        main_container.add_widget(self.overlay_container)
        main_container.add_widget(refresh_btn)
        
        return main_container
    
    def on_start(self):
        print("Starting modern KOT application...")
        self.connect_websocket()
        self.fetch_orders()
    
    def connect_websocket(self):
        if not WS_BACKEND_URL or WS_BACKEND_URL == "None":
            print("WebSocket URL not configured")
            return
            
        url = f"{WS_BACKEND_URL}/ws/orders/"
        self.websocket_client = WebSocketClient(url, self.handle_websocket_message)
        self.connection_thread = threading.Thread(target=self.websocket_client.connect)
        self.connection_thread.daemon = True
        self.connection_thread.start()
    
    def handle_websocket_message(self, message):
        print("New order notification received")
        self.sound_player.play_sound()
        Clock.schedule_once(lambda dt: self.fetch_orders(), 1)
    
    def fetch_orders(self):
        print("Fetching orders from API...")
        self.is_loading = True
        self.error_message = ""
        self.orders = []
        self.update_ui()
        
        def fetch_thread():
            try:
                if not HTTP_BACKEND_URL or HTTP_BACKEND_URL == "None":
                    raise Exception("HTTP_BACKEND_URL not configured")
                    
                url = f"{HTTP_BACKEND_URL}/orders/ordered-items/kot-orders/"
                headers = {'Accept': 'application/json', 'Content-Type': 'application/json'}
                
                print(f"GET Request to: {url}")
                response = requests.get(url, headers=headers, timeout=15)
                print(f"GET Response Status: {response.status_code}")
                response.raise_for_status()
                
                data = response.json()
                print("GET Response JSON parsed successfully.")
                
                if isinstance(data, dict) and data.get('status') == 'success' and 'data' in data:
                    self.orders = data['data']
                    print(f"Successfully parsed {len(self.orders)} orders from response.")
                elif isinstance(data, list):
                    self.orders = data
                    print(f"Successfully parsed {len(self.orders)} orders from a list response.")
                else:
                    self.orders = []
                    print(f"Could not find order data in response. Response keys: {data.keys() if isinstance(data, dict) else 'N/A'}")

                self.is_loading = False
                self.error_message = ""
                
            except requests.exceptions.Timeout:
                self.orders = []
                self.is_loading = False
                self.error_message = "Request timeout - please check your connection"
                print(f"Error: {self.error_message}")
                
            except requests.exceptions.ConnectionError:
                self.orders = []
                self.is_loading = False
                self.error_message = "Cannot connect to server"
                print(f"Error: {self.error_message}")
                
            except Exception as e:
                self.orders = []
                self.is_loading = False
                self.error_message = f"Error: {str(e)}"
                print(f"An unexpected error occurred: {self.error_message}")
            
            Clock.schedule_once(lambda dt: self.update_ui(), 0)
        
        threading.Thread(target=fetch_thread, daemon=True).start()
    
    def mark_item_as_done(self, item_id):
        print(f"Marking item {item_id} as done...")
        
        def mark_thread():
            try:
                url = f"{HTTP_BACKEND_URL}/orders/ordered-items/kot-orders/{item_id}/"
                print(f"PATCH URL: {url}")
                headers = {'Accept': 'application/json', 'Content-Type': 'application/json'}
                
                response = requests.patch(url, json={}, headers=headers, timeout=10)
                print(f"PATCH Response Status: {response.status_code}")
                print(f"PATCH Response Body: {response.text}")
                response.raise_for_status()
                
                print(f"Item {item_id} marked as done successfully, refreshing orders...")
                Clock.schedule_once(lambda dt: self.fetch_orders(), 0)
                
            except Exception as e:
                print(f"Error marking item as done: {e}")
        
        threading.Thread(target=mark_thread, daemon=True).start()
    
    def update_ui(self):
        print(f"Updating UI - orders: {len(self.orders)}, loading: {self.is_loading}")
        
        # Update header
        self.header.update_pending_count(len(self.orders))
        
        # Clear overlays
        self.overlay_container.clear_widgets()
        self.orders_container.clear_widgets()
        
        if self.is_loading:
            self.loading_container.clear_widgets()
            self.loading_container.add_widget(self.loading_label)
            self.overlay_container.add_widget(self.loading_container)
            return
        
        if self.error_message:
            self.error_label.text = self.error_message
            self.overlay_container.add_widget(self.error_container)
            return
        
        if not self.orders:
            no_orders = Label(
                text="🍽️ No pending orders\nEverything is caught up!",
                color=(0.6, 0.6, 0.6, 1),
                font_size=dp(18),
                halign='center'
            )
            self.orders_container.add_widget(no_orders)
        else:
            for order in self.orders:
                try:
                    card = OrderCard(order, self.mark_item_as_done)
                    self.orders_container.add_widget(card)
                except Exception as e:
                    print(f"Error creating order card for {order.get('id', 'N/A')}: {e}")
        
        print("UI update complete")
    
    def on_stop(self):
        print("Stopping application...")
        if self.websocket_client:
            self.websocket_client.disconnect()


if __name__ == '__main__':
    KOTApp().run()