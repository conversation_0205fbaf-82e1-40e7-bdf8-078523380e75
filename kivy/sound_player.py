#!/usr/bin/env python3
"""
Simple Kivy WebSocket Sound Player
Connects to Django WebSocket and plays sound on message receipt
"""

import kivy
kivy.require('2.2.1')

from kivy.app import App
from kivy.core.audio import SoundLoader
from kivy.clock import Clock
from kivy.uix.scrollview import ScrollView
from kivy.uix.label import Label
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.widget import Widget
from kivy.uix.behaviors import ButtonBehavior
from kivy.graphics import Color, RoundedRectangle
from kivy.core.window import Window
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty
import websocket
import threading
import json
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()
WS_BACKEND_URL = f"{os.getenv('WS_BACKEND_URL').rstrip('/')}"


class SimpleWebSocketClient:
    def __init__(self, url, on_message_callback):
        self.url = url
        self.on_message_callback = on_message_callback
        self.ws = None
        self.connected = False
        self.reconnect_delay = 5  # Start with 5 seconds delay
        self.max_reconnect_delay = 60  # Maximum delay of 60 seconds
        self.should_reconnect = True
        
    def connect(self):
        while self.should_reconnect:
            try:
                print(f"Attempting to connect to {self.url}")
                self.ws = websocket.WebSocketApp(
                    self.url,
                    on_open=self.on_open,
                    on_message=self.on_message,
                    on_error=self.on_error,
                    on_close=self.on_close
                )
                self.ws.run_forever()
                
                # If we get here, connection was closed
                if self.should_reconnect:
                    print(f"Connection lost. Reconnecting in {self.reconnect_delay} seconds...")
                    import time
                    time.sleep(self.reconnect_delay)
                    # Increase delay for next attempt (exponential backoff)
                    self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)
                    
            except Exception as e:
                print(f"WebSocket connection error: {e}")
                if self.should_reconnect:
                    print(f"Reconnecting in {self.reconnect_delay} seconds...")
                    import time
                    time.sleep(self.reconnect_delay)
                    # Increase delay for next attempt (exponential backoff)
                    self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)
    
    def on_open(self, ws):
        print("Connected to Django WebSocket")
        self.connected = True
        # Reset reconnect delay on successful connection
        self.reconnect_delay = 5
        
    def on_message(self, ws, message):
        # Trigger sound playback on main thread
        Clock.schedule_once(lambda dt: self.on_message_callback(message), 0)
            
    def on_error(self, ws, error):
        print(f"WebSocket error: {error}")
        self.connected = False
        
    def on_close(self, ws, close_status_code, close_msg):
        print(f"Connection closed. Code: {close_status_code}, Message: {close_msg}")
        self.connected = False
        
    def disconnect(self):
        self.should_reconnect = False
        if self.ws:
            self.ws.close()


class SimpleSoundPlayer:
    def __init__(self):
        self.sound = None
        self.load_sound()
        
    def load_sound(self):
        # Try to load a sound file
        sound_paths = [
            "notification.wav",
            "alert.wav", 
            "beep.wav",
            "sounds/notification.wav",
            "sounds/alert.wav"
        ]
        
        for path in sound_paths:
            if os.path.exists(path):
                try:
                    self.sound = SoundLoader.load(path)
                    if self.sound:
                        print(f"Loaded sound: {path}")
                        return
                except Exception as e:
                    print(f"Failed to load {path}: {e}")
        
        print("No sound file found, creating simple beep...")
        self.create_simple_beep()
    
    def create_simple_beep(self):
        """Create a simple beep sound if no sound file is available"""
        try:
            import wave
            import struct
            import math
            
            # Create a simple beep sound
            sample_rate = 44100
            duration = 0.3
            frequency = 800
            
            num_samples = int(sample_rate * duration)
            audio_data = []
            
            for i in range(num_samples):
                value = math.sin(2 * math.pi * frequency * i / sample_rate)
                audio_data.append(int(value * 32767))
            
            # Save as WAV file
            with wave.open("beep.wav", 'w') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(sample_rate)
                
                for sample in audio_data:
                    wav_file.writeframes(struct.pack('<h', sample))
            
            # Load the created sound
            self.sound = SoundLoader.load("beep.wav")
            print("Created and loaded beep sound")
            
        except Exception as e:
            print(f"Failed to create beep sound: {e}")
    
    def play_sound(self):
        if self.sound:
            try:
                self.sound.play()
                print("Sound played")
            except Exception as e:
                print(f"Failed to play sound: {e}")
        else:
            print("No sound available")



class ModernButton(ButtonBehavior, Widget):
    """Modern styled button with hover and press effects"""
    text = StringProperty("")
    bg_color = ListProperty([0.8, 0.2, 0.2, 1])  # Default red color
    text_color = ListProperty([1, 1, 1, 1])  # White text
    hover_color = ListProperty([0.9, 0.3, 0.3, 1])  # Lighter red on hover
    press_color = ListProperty([0.7, 0.1, 0.1, 1])  # Darker red when pressed
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size_hint_y = None
        self.height = dp(50)
        self.padding = dp(10)
        
        # Background with rounded corners
        with self.canvas.before:
            self.bg_color_state = Color(rgba=self.bg_color)
            self.bg_rect = RoundedRectangle(
                pos=self.pos, 
                size=self.size, 
                radius=[dp(25)]  # More pronounced rounded corners
            )
            
        # Button label
        self.label = Label(
            text=self.text,
            color=self.text_color,
            font_size=dp(16),
            bold=True,
            halign='center',
            valign='middle'
        )
        self.add_widget(self.label)
        
        self.bind(
            pos=self._update_rect,
            size=self._update_rect,
            text=self._update_label
        )
    
    def _update_rect(self, *args):
        """Update background rectangle position and size"""
        self.bg_rect.pos = self.pos
        self.bg_rect.size = self.size
        self.label.pos = self.pos
        self.label.size = self.size
    
    def _update_label(self, *args):
        """Update label text"""
        self.label.text = self.text
    
    def on_state(self, instance, value):
        """Change appearance based on button state"""
        if value == 'down':
            self.bg_color_state.rgba = self.press_color
        else:
            self.bg_color_state.rgba = self.hover_color if self.hovered else self.bg_color
    
    def on_hover(self, instance, value):
        """Handle hover state changes"""
        if not self.state == 'down':  # Don't change if button is pressed
            self.bg_color_state.rgba = self.hover_color if value else self.bg_color
    
    def on_touch_down(self, touch):
        """Handle touch down events"""
        if self.collide_point(*touch.pos):
            self.state = 'down'
            return super().on_touch_down(touch)
        return False
    
    def on_touch_up(self, touch):
        """Handle touch up events"""
        if self.state == 'down':
            self.state = 'normal'
            if self.collide_point(*touch.pos):
                self.dispatch('on_release')
        return super().on_touch_up(touch)


class LogViewerApp(App):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.websocket_client = None
        self.sound_player = SimpleSoundPlayer()
        self.connection_thread = None
        self.log_label = None
        self.reconnect_btn = None

    def build(self):
        self.title = "Alarm Event Logger"
        Window.size = (800, 350)
        Window.clearcolor = (0.1, 0.1, 0.1, 1)

        # Main layout
        main_layout = BoxLayout(orientation='vertical', spacing=dp(10), padding=dp(10))

        # Log viewer
        scroll_view = ScrollView()
        
        self.log_label = Label(
            text="[b]Alarm Log Viewer[/b]\n\n",
            size_hint_y=None,
            markup=True,
            font_size='14sp',
            padding=(dp(10), dp(10)),
            halign='left',
            valign='top'
        )
        self.log_label.bind(texture_size=self.log_label.setter('size'))
        
        scroll_view.add_widget(self.log_label)
        main_layout.add_widget(scroll_view)

        # Button layout for Shutdown and Reconnect
        button_layout = BoxLayout(orientation='horizontal', spacing=dp(10), size_hint_y=None, height=dp(50))
        
        # Reconnect button
        self.reconnect_btn = ModernButton(text="Connecting...")
        self.reconnect_btn.bg_color = [0.2, 0.6, 0.8, 1]  # Blue color
        self.reconnect_btn.hover_color = [0.3, 0.7, 0.9, 1]
        self.reconnect_btn.press_color = [0.1, 0.5, 0.7, 1]
        self.reconnect_btn.bind(on_press=self.manual_reconnect)
        button_layout.add_widget(self.reconnect_btn)
        
        # Shutdown button
        shutdown_btn = ModernButton(text="Shutdown Logger")
        shutdown_btn.bg_color = [0.8, 0.2, 0.2, 1]  # Red color
        shutdown_btn.hover_color = [0.9, 0.3, 0.3, 1]
        shutdown_btn.press_color = [0.7, 0.1, 0.1, 1]
        shutdown_btn.bind(on_press=self.stop)
        button_layout.add_widget(shutdown_btn)
        
        main_layout.add_widget(button_layout)

        return main_layout

    def update_reconnect_button(self, connected):
        """Update reconnect button text and state based on connection status"""
        if self.reconnect_btn:
            if connected:
                self.reconnect_btn.text = "Connected"
                self.reconnect_btn.bg_color = [0.2, 0.8, 0.2, 1]  # Green
                self.reconnect_btn.hover_color = [0.3, 0.9, 0.3, 1]
                self.reconnect_btn.press_color = [0.1, 0.7, 0.1, 1]
            else:
                self.reconnect_btn.text = "Reconnect"
                self.reconnect_btn.bg_color = [0.8, 0.2, 0.2, 1]  # Red
                self.reconnect_btn.hover_color = [0.9, 0.3, 0.3, 1]
                self.reconnect_btn.press_color = [0.7, 0.1, 0.1, 1]

    def on_start(self):
        """Start WebSocket connection and minimize window."""
        Window.minimize()
        self.log_message("[color=00ff00]Connecting to Alarm Server...[/color]")
        self.connect_websocket()

    def connect_websocket(self):
        url = f"{WS_BACKEND_URL}/ws/orders/"
        self.websocket_client = SimpleWebSocketClient(url, self.handle_message)
        
        # Add logging hooks to the client instance
        self.websocket_client.on_open = self.on_ws_open
        self.websocket_client.on_close = self.on_ws_close
        self.websocket_client.on_error = self.on_ws_error
        
        self.connection_thread = threading.Thread(target=self.websocket_client.connect, daemon=True)
        self.connection_thread.start()

    def on_ws_open(self, ws):
        self.websocket_client.connected = True
        Clock.schedule_once(lambda dt: self.log_message("[color=00ff00] Connection opened.[/color]"), 0)
        Clock.schedule_once(lambda dt: self.update_reconnect_button(True), 0)

    def on_ws_close(self, ws, code, msg):
        self.websocket_client.connected = False
        message = f"[color=ff0000] Connection closed. Code: {code}[/color]"
        Clock.schedule_once(lambda dt: self.log_message(message), 0)
        Clock.schedule_once(lambda dt: self.update_reconnect_button(False), 0)
    
    def on_ws_error(self, ws, error):
        message = f"[color=ff0000]WebSocket error: {error}[/color]"
        Clock.schedule_once(lambda dt: self.log_message(message), 0)
        Clock.schedule_once(lambda dt: self.update_reconnect_button(False), 0)

    def handle_message(self, message):
        """Handle incoming WebSocket message."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[b]{timestamp}[/b]: "

        try:
            data = json.loads(message)
            msg_type = data.get('type')
            
            if msg_type == 'order_update':
                order_info = data.get('message', {})
                name = order_info.get('name', 'N/A')
                is_kot = order_info.get('is_kot', False)
                action = order_info.get('action', 'updated')
                log_entry += f"Order '{name}' was {action}."
                
                # Only play sound if is_kot is true
                if is_kot:
                    self.sound_player.play_sound()
                    log_entry += " [color=ffff00](KOT - Sound played)[/color]"
            elif 'name' in data:
                log_entry += f"Received event: {data.get('name')}"
            else:
                # Fallback for any other structure
                log_entry += f"Received message:\n{json.dumps(data, indent=2)}"

        except (json.JSONDecodeError, AttributeError):
            log_entry += f"Received non-JSON message: {message}"

        self.log_message(log_entry)

    def log_message(self, text):
        if self.log_label:
            # Prepend new messages to the top
            self.log_label.text = f"{text}\n{'-'*50}\n" + self.log_label.text
            # Limit log length to prevent performance issues
            log_lines = self.log_label.text.split('\n')
            if len(log_lines) > 200:
                self.log_label.text = '\n'.join(log_lines[:200])

    def on_stop(self):
        """Clean up when app stops."""
        print("Logger shutting down...")
        if self.websocket_client:
            self.websocket_client.disconnect()

    def manual_reconnect(self, instance):
        """Manually trigger reconnection only if disconnected"""
        if self.websocket_client and self.websocket_client.connected:
            self.log_message("[color=ffff00]Already connected![/color]")
            return
        
        self.log_message("[color=ffff00]Manual reconnection requested...[/color]")
        
        # Disconnect current connection if it exists
        if self.websocket_client:
            self.websocket_client.disconnect()
        
        # Reset reconnect delay for immediate reconnection
        if self.websocket_client:
            self.websocket_client.reconnect_delay = 5
            self.websocket_client.should_reconnect = True
        
        # Start new connection thread
        self.connection_thread = threading.Thread(target=self.websocket_client.connect, daemon=True)
        self.connection_thread.start()
        
        self.log_message("[color=00ff00]Reconnection initiated...[/color]")


if __name__ == '__main__':
    LogViewerApp().run() 